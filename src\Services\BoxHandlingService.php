<?php

namespace App\Services;

use App\Services\BillingEventLogger;

/**
 * Box Handling Service
 * 
 * Manages all box-related business operations including:
 * - Box lifecycle management
 * - Box movement tracking
 * - Capacity management
 * - Status automation
 */
class BoxHandlingService extends BaseService
{
    /**
     * Create a new box with proper validation and setup
     */
    public function createBox($data)
    {
        $this->validateRequired($data, ['name', 'warehouse_id', 'client_prefix']);
        $data = $this->sanitizeData($data);
        
        $this->validateCompanyAccess($this->user['company_id']);

        try {
            $this->db->beginTransaction();

            // Generate box ID and location
            $boxNumber = $this->getNextBoxNumber($data['client_prefix']);
            $boxId = $data['client_prefix'] . '-BOX' . str_pad($boxNumber, 3, '0', STR_PAD_LEFT);
            
            // Generate storage location code
            $storageLocationCode = $this->generateStorageLocationCode(
                $data['warehouse_id'],
                $data['row_number'] ?? 'R1',
                $data['shelf_number'] ?? 'S1',
                'B' . str_pad($boxNumber, 2, '0', STR_PAD_LEFT)
            );

            // Create box record
            $newBoxId = $this->db->execute(
                "INSERT INTO boxes (
                    company_id, box_id, client_prefix, box_number, name, description,
                    warehouse_id, storage_type, storage_location_code, row_number, 
                    shelf_number, position_number, capacity, status, created_by, 
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'empty', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $boxId,
                    $data['client_prefix'],
                    $boxNumber,
                    $data['name'],
                    $data['description'] ?? null,
                    $data['warehouse_id'],
                    $data['storage_type'] ?? 'physical',
                    $storageLocationCode,
                    $data['row_number'] ?? 'R1',
                    $data['shelf_number'] ?? 'S1',
                    'B' . str_pad($boxNumber, 2, '0', STR_PAD_LEFT),
                    $data['capacity'] ?? 100,
                    $this->user['id']
                ]
            );

            // Generate barcode if not provided
            if (!empty($data['barcode'])) {
                $this->updateBoxBarcode($newBoxId, $data['barcode']);
            } else {
                $this->generateBarcodeForBox($newBoxId, $boxId);

                // Log billing event for barcode generation
                if (!empty($data['client_id'])) {
                    BillingEventLogger::logBoxBarcodeGenerated($data['client_id'], $newBoxId, $boxId);
                }
            }

            // Log billing event for box handling
            if (!empty($data['client_id'])) {
                BillingEventLogger::logBoxHandling($data['client_id'], $newBoxId, 'creation', $boxId);
            }

            // Log activity
            $this->logActivity('create', 'box', $newBoxId, "Created box: {$boxId} at {$storageLocationCode}");

            $this->db->commit();

            return [
                'id' => $newBoxId,
                'box_id' => $boxId,
                'storage_location_code' => $storageLocationCode,
                'status' => 'empty'
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to create box: ' . $e->getMessage());
        }
    }

    /**
     * Move box to new location
     */
    public function moveBox($boxId, $newWarehouseId, $newLocation = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        try {
            $this->db->beginTransaction();

            // Get current box
            $box = $this->getBoxById($boxId);
            if (!$box) {
                throw new \Exception('Box not found');
            }

            // Generate new storage location code
            $newStorageLocationCode = $this->generateStorageLocationCode(
                $newWarehouseId,
                $newLocation['row_number'] ?? 'R1',
                $newLocation['shelf_number'] ?? 'S1',
                $newLocation['position_number'] ?? $box['position_number']
            );

            // Update box location
            $this->db->execute(
                "UPDATE boxes SET 
                 warehouse_id = ?, storage_location_code = ?, 
                 row_number = ?, shelf_number = ?, position_number = ?,
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [
                    $newWarehouseId,
                    $newStorageLocationCode,
                    $newLocation['row_number'] ?? 'R1',
                    $newLocation['shelf_number'] ?? 'S1',
                    $newLocation['position_number'] ?? $box['position_number'],
                    $boxId,
                    $this->user['company_id']
                ]
            );

            // Log movement
            $this->logBoxMovement($boxId, $box['storage_location_code'], $newStorageLocationCode);

            // Log billing event for box handling (movement)
            if (!empty($box['client_id'])) {
                BillingEventLogger::logBoxHandling($box['client_id'], $boxId, 'movement', $box['box_id']);
            }

            $this->db->commit();

            return [
                'success' => true,
                'old_location' => $box['storage_location_code'],
                'new_location' => $newStorageLocationCode
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to move box: ' . $e->getMessage());
        }
    }

    /**
     * Add bundle to box
     */
    public function addBundleToBox($boxId, $bundleId)
    {
        $this->validateCompanyAccess($this->user['company_id']);

        try {
            $this->db->beginTransaction();

            // Validate box and bundle exist
            $box = $this->getBoxById($boxId);
            $bundle = $this->getBundleById($bundleId);

            if (!$box || !$bundle) {
                throw new \Exception('Box or bundle not found');
            }

            // Check capacity
            if ($box['current_count'] >= $box['capacity']) {
                throw new \Exception('Box is at full capacity');
            }

            // Add bundle to box (using junction table)
            $this->db->execute(
                "INSERT INTO box_bundles (box_id, bundle_id, added_at, added_by) 
                 VALUES (?, ?, NOW(), ?)",
                [$boxId, $bundleId, $this->user['id']]
            );

            // Update box count and status
            $this->updateBoxStatus($boxId);

            $this->logActivity('add_bundle', 'box', $boxId, "Added bundle {$bundle['name']} to box {$box['box_id']}");

            $this->db->commit();

            return ['success' => true];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to add bundle to box: ' . $e->getMessage());
        }
    }

    /**
     * Remove bundle from box
     */
    public function removeBundleFromBox($boxId, $bundleId)
    {
        $this->validateCompanyAccess($this->user['company_id']);

        try {
            $this->db->beginTransaction();

            // Remove bundle from box
            $this->db->execute(
                "DELETE FROM box_bundles WHERE box_id = ? AND bundle_id = ?",
                [$boxId, $bundleId]
            );

            // Update box status
            $this->updateBoxStatus($boxId);

            $this->logActivity('remove_bundle', 'box', $boxId, "Removed bundle from box");

            $this->db->commit();

            return ['success' => true];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to remove bundle from box: ' . $e->getMessage());
        }
    }

    /**
     * Get box details with contents
     */
    public function getBoxDetails($boxId)
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $box = $this->getBoxById($boxId);
        if (!$box) {
            throw new \Exception('Box not found');
        }

        // Get bundles in this box
        $bundles = $this->db->fetchAll(
            "SELECT b.*, bb.added_at, bb.added_by,
                    u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM box_bundles bb
             JOIN bundles b ON bb.bundle_id = b.id
             LEFT JOIN users u ON bb.added_by = u.id
             LEFT JOIN documents d ON b.id = d.bundle_id
             WHERE bb.box_id = ? AND b.company_id = ?
             GROUP BY b.id
             ORDER BY bb.added_at DESC",
            [$boxId, $this->user['company_id']]
        );

        // Get movement history
        $movements = $this->getBoxMovementHistory($boxId);

        return [
            'box' => $box,
            'bundles' => $bundles,
            'movements' => $movements,
            'total_bundles' => count($bundles),
            'total_documents' => array_sum(array_column($bundles, 'document_count'))
        ];
    }

    /**
     * Search boxes with advanced filters
     */
    public function searchBoxes($filters = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $where = ["b.company_id = ?"];
        $params = [$this->user['company_id']];

        // Apply filters
        if (!empty($filters['search'])) {
            $where[] = "(b.box_id LIKE ? OR b.name LIKE ? OR b.storage_location_code LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($filters['status'])) {
            $where[] = "b.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['storage_type'])) {
            $where[] = "b.storage_type = ?";
            $params[] = $filters['storage_type'];
        }

        if (!empty($filters['warehouse_id'])) {
            $where[] = "b.warehouse_id = ?";
            $params[] = $filters['warehouse_id'];
        }

        $whereClause = implode(' AND ', $where);
        $orderBy = $filters['sort'] ?? 'b.created_at DESC';

        $sql = "SELECT b.*, w.name as warehouse_name,
                       COUNT(bb.bundle_id) as bundle_count
                FROM boxes b
                LEFT JOIN warehouses w ON b.warehouse_id = w.id
                LEFT JOIN box_bundles bb ON b.id = bb.box_id
                WHERE {$whereClause}
                GROUP BY b.id
                ORDER BY {$orderBy}";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get next box number for client prefix
     */
    private function getNextBoxNumber($clientPrefix)
    {
        $lastBox = $this->db->fetch(
            "SELECT box_number FROM boxes
             WHERE client_prefix = ? AND company_id = ?
             ORDER BY box_number DESC LIMIT 1",
            [$clientPrefix, $this->user['company_id']]
        );

        return $lastBox ? $lastBox['box_number'] + 1 : 1;
    }

    /**
     * Generate storage location code
     */
    private function generateStorageLocationCode($warehouseId, $row, $shelf, $position)
    {
        $warehouse = $this->db->fetch(
            "SELECT code FROM warehouses WHERE id = ?",
            [$warehouseId]
        );

        $warehouseCode = $warehouse ? $warehouse['code'] : 'WH';
        return "{$warehouseCode}-{$row}-{$shelf}-{$position}";
    }

    /**
     * Update box barcode
     */
    private function updateBoxBarcode($boxId, $barcode)
    {
        $this->db->execute(
            "UPDATE boxes SET barcode_value = ?, barcode_generated_at = NOW()
             WHERE id = ?",
            [$barcode, $boxId]
        );
    }

    /**
     * Generate barcode for box
     */
    private function generateBarcodeForBox($boxId, $boxIdString)
    {
        // Generate QR code or barcode based on box ID
        $barcode = 'BOX-' . $boxIdString . '-' . time();
        $this->updateBoxBarcode($boxId, $barcode);
    }

    /**
     * Get box by ID
     */
    private function getBoxById($boxId)
    {
        return $this->db->fetch(
            "SELECT * FROM boxes WHERE id = ? AND company_id = ?",
            [$boxId, $this->user['company_id']]
        );
    }

    /**
     * Get bundle by ID
     */
    private function getBundleById($bundleId)
    {
        return $this->db->fetch(
            "SELECT * FROM bundles WHERE id = ? AND company_id = ?",
            [$bundleId, $this->user['company_id']]
        );
    }

    /**
     * Update box status based on contents
     */
    private function updateBoxStatus($boxId)
    {
        // Count bundles in box
        $count = $this->db->fetch(
            "SELECT COUNT(*) as count FROM box_bundles WHERE box_id = ?",
            [$boxId]
        )['count'];

        // Get box capacity
        $box = $this->getBoxById($boxId);
        $capacity = $box['capacity'];

        // Determine status
        $status = 'empty';
        if ($count > 0) {
            $status = ($count >= $capacity) ? 'full' : 'partial';
        }

        // Update box
        $this->db->execute(
            "UPDATE boxes SET current_count = ?, status = ?, updated_at = NOW()
             WHERE id = ?",
            [$count, $status, $boxId]
        );
    }

    /**
     * Log box movement
     */
    private function logBoxMovement($boxId, $oldLocation, $newLocation)
    {
        $this->db->execute(
            "INSERT INTO box_movements (
                box_id, old_location, new_location, moved_by, moved_at
            ) VALUES (?, ?, ?, ?, NOW())",
            [$boxId, $oldLocation, $newLocation, $this->user['id']]
        );

        $this->logActivity(
            'move',
            'box',
            $boxId,
            "Moved box from {$oldLocation} to {$newLocation}"
        );
    }

    /**
     * Get box movement history
     */
    private function getBoxMovementHistory($boxId)
    {
        return $this->db->fetchAll(
            "SELECT bm.*, u.first_name, u.last_name
             FROM box_movements bm
             LEFT JOIN users u ON bm.moved_by = u.id
             WHERE bm.box_id = ?
             ORDER BY bm.moved_at DESC",
            [$boxId]
        );
    }
}
