-- Cleanup bundles table - remove unnecessary fields and simplify structure
-- This migration removes redundant fields and focuses on essential bundle information

-- Remove redundant fields that are not needed for simplified bundle creation
ALTER TABLE bundles 
DROP COLUMN IF EXISTS document_type,
DROP COLUMN IF EXISTS year,
DROP COLUMN IF EXISTS department,
DROP COLUMN IF EXISTS confidentiality_flag,
DROP COLUMN IF EXISTS pages_volume,
DROP COLUMN IF EXISTS scan_digitization_status,
DROP COLUMN IF EXISTS contents_summary,
DROP COLUMN IF EXISTS box_id,
DROP COLUMN IF EXISTS intake_id,
DROP COLUMN IF EXISTS bundle_type,
DROP COLUMN IF EXISTS location_id;

-- Update the status enum to be more appropriate
ALTER TABLE bundles 
MODIFY COLUMN status ENUM('active', 'inactive', 'archived') DEFAULT 'active';

-- Add indexes for better performance on essential fields
ALTER TABLE bundles 
ADD INDEX IF NOT EXISTS idx_bundles_name (name),
ADD INDEX IF NOT EXISTS idx_bundles_access_level (access_level);

-- Update any existing bundles with old status values
UPDATE bundles SET status = 'active' WHERE status = 'open';
UPDATE bundles SET status = 'archived' WHERE status = 'closed';

-- Clean up any test data or invalid entries
UPDATE bundles SET 
    category = 'general' WHERE category IS NULL OR category = '',
    priority = 'medium' WHERE priority IS NULL OR priority = '',
    access_level = 'private' WHERE access_level IS NULL OR access_level = '',
    retention_period = 7 WHERE retention_period IS NULL OR retention_period < 1;

-- Ensure reference numbers are properly formatted for existing bundles
UPDATE bundles 
SET reference_number = CONCAT('BUN-', YEAR(created_at), '-', LPAD(id, 6, '0'))
WHERE reference_number IS NULL OR reference_number = '';
