<?php

namespace App\Controllers;

/**
 * Compliance & Retention Management Controller
 * 
 * Advanced compliance management features for super admin
 * Provides retention policies, destruction scheduling, and audit reports
 */
class ComplianceController extends BaseController
{
    /**
     * Compliance Management Dashboard
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get compliance overview statistics
            $complianceStats = $this->getComplianceStats();
            
            // Get upcoming destructions
            $upcomingDestructions = $this->getUpcomingDestructions();
            
            // Get retention policy overview
            $retentionPolicies = $this->getRetentionPolicies();
            
            // Get compliance alerts
            $complianceAlerts = $this->getComplianceAlerts();

            $this->view('super-admin/compliance-management', [
                'title' => 'Compliance & Retention Management',
                'complianceStats' => $complianceStats,
                'upcomingDestructions' => $upcomingDestructions,
                'retentionPolicies' => $retentionPolicies,
                'complianceAlerts' => $complianceAlerts
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading compliance management: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/dashboard');
        }
    }

    /**
     * Retention Policies Management
     */
    public function retentionPolicies()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->createRetentionPolicy();
        }

        try {
            // Get all retention policies
            $policies = $this->getAllRetentionPolicies();
            
            // Get document types for policy creation
            $documentTypes = $this->getDocumentTypes();
            
            // Get compliance regulations
            $regulations = $this->getComplianceRegulations();

            $this->view('super-admin/retention-policies', [
                'title' => 'Retention Policies',
                'policies' => $policies,
                'documentTypes' => $documentTypes,
                'regulations' => $regulations
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading retention policies: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/compliance-management');
        }
    }

    /**
     * Destruction Scheduling
     */
    public function destructionSchedule()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get scheduled destructions
            $scheduledDestructions = $this->getScheduledDestructions();
            
            // Get documents eligible for destruction
            $eligibleDocuments = $this->getDocumentsEligibleForDestruction();
            
            // Get destruction statistics
            $destructionStats = $this->getDestructionStats();

            $this->view('super-admin/destruction-schedule', [
                'title' => 'Document Destruction Schedule',
                'scheduledDestructions' => $scheduledDestructions,
                'eligibleDocuments' => $eligibleDocuments,
                'destructionStats' => $destructionStats
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading destruction schedule: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/compliance-management');
        }
    }

    /**
     * Legal Hold Management
     */
    public function legalHolds()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->createLegalHold();
        }

        try {
            // Get active legal holds
            $activeLegalHolds = $this->getActiveLegalHolds();
            
            // Get documents under legal hold
            $documentsUnderHold = $this->getDocumentsUnderLegalHold();
            
            // Get legal hold statistics
            $legalHoldStats = $this->getLegalHoldStats();

            $this->view('super-admin/legal-holds', [
                'title' => 'Legal Hold Management',
                'activeLegalHolds' => $activeLegalHolds,
                'documentsUnderHold' => $documentsUnderHold,
                'legalHoldStats' => $legalHoldStats
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading legal holds: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/compliance-management');
        }
    }

    /**
     * Compliance Reports
     */
    public function reports()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get report parameters
            $reportType = $_GET['type'] ?? 'overview';
            $dateRange = $_GET['range'] ?? '30';
            
            // Generate compliance report
            $reportData = $this->generateComplianceReport($reportType, $dateRange);
            
            // Get available report types
            $reportTypes = $this->getAvailableReportTypes();

            $this->view('super-admin/compliance-reports', [
                'title' => 'Compliance Reports',
                'reportData' => $reportData,
                'reportTypes' => $reportTypes,
                'currentType' => $reportType,
                'currentRange' => $dateRange
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error generating compliance report: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/compliance-management');
        }
    }

    /**
     * Create retention policy
     */
    private function createRetentionPolicy()
    {
        try {
            $data = $this->validateRetentionPolicyData($_POST);
            
            $policyId = $this->db->execute(
                "INSERT INTO retention_policies (
                    document_type, retention_years, destruction_method, 
                    compliance_regulation, description, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())",
                [
                    $data['document_type'],
                    $data['retention_years'],
                    $data['destruction_method'],
                    $data['compliance_regulation'],
                    $data['description'] ?? null,
                    $this->user['id']
                ]
            );

            // Apply policy to existing documents
            $this->applyRetentionPolicyToExistingDocuments($policyId, $data['document_type']);

            $this->setFlashMessage('Retention policy created successfully', 'success');
            $this->redirect('/super-admin/compliance-management/retention-policies');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create retention policy: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/compliance-management/retention-policies');
        }
    }

    /**
     * Create legal hold
     */
    private function createLegalHold()
    {
        try {
            $data = $this->validateLegalHoldData($_POST);
            
            $holdId = $this->db->execute(
                "INSERT INTO legal_holds (
                    hold_name, case_number, description, hold_reason,
                    created_by, start_date, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'active', NOW())",
                [
                    $data['hold_name'],
                    $data['case_number'],
                    $data['description'],
                    $data['hold_reason'],
                    $this->user['id'],
                    $data['start_date']
                ]
            );

            // Apply hold to specified documents
            if (!empty($data['document_ids'])) {
                $this->applyLegalHoldToDocuments($holdId, $data['document_ids']);
            }

            $this->setFlashMessage('Legal hold created successfully', 'success');
            $this->redirect('/super-admin/compliance-management/legal-holds');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create legal hold: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/compliance-management/legal-holds');
        }
    }

    /**
     * Get compliance overview statistics
     */
    private function getComplianceStats()
    {
        return $this->db->fetch(
            "SELECT 
                COUNT(DISTINCT rp.id) as total_policies,
                COUNT(DISTINCT ds.id) as scheduled_destructions,
                COUNT(DISTINCT lh.id) as active_legal_holds,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN ds.id END) as destructions_next_30_days,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN ds.id END) as destructions_next_7_days,
                COUNT(DISTINCT d.id) as documents_under_hold,
                COUNT(DISTINCT CASE WHEN ds.status = 'overdue' THEN ds.id END) as overdue_destructions
             FROM retention_policies rp
             LEFT JOIN destruction_schedules ds ON ds.status IN ('scheduled', 'overdue')
             LEFT JOIN legal_holds lh ON lh.status = 'active'
             LEFT JOIN legal_hold_documents lhd ON lh.id = lhd.legal_hold_id
             LEFT JOIN documents d ON lhd.document_id = d.id",
            []
        );
    }

    /**
     * Get upcoming destructions
     */
    private function getUpcomingDestructions()
    {
        return $this->db->fetchAll(
            "SELECT 
                ds.*,
                d.title as document_title,
                d.reference_number,
                c.name as company_name,
                rp.document_type,
                rp.destruction_method
             FROM destruction_schedules ds
             JOIN documents d ON ds.document_id = d.id
             JOIN companies c ON d.company_id = c.id
             LEFT JOIN retention_policies rp ON d.document_type = rp.document_type
             WHERE ds.status = 'scheduled' 
             AND ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
             ORDER BY ds.scheduled_date ASC
             LIMIT 20",
            []
        );
    }

    /**
     * Get retention policies
     */
    private function getRetentionPolicies()
    {
        return $this->db->fetchAll(
            "SELECT 
                rp.*,
                u.first_name,
                u.last_name,
                COUNT(d.id) as affected_documents
             FROM retention_policies rp
             LEFT JOIN users u ON rp.created_by = u.id
             LEFT JOIN documents d ON d.document_type = rp.document_type
             GROUP BY rp.id
             ORDER BY rp.created_at DESC",
            []
        );
    }

    /**
     * Get compliance alerts
     */
    private function getComplianceAlerts()
    {
        return $this->db->fetchAll(
            "SELECT 
                'destruction_overdue' as alert_type,
                'critical' as severity,
                CONCAT('Document destruction overdue: ', d.title) as title,
                CONCAT('Scheduled for ', ds.scheduled_date, ' but not completed') as message,
                c.name as company_name,
                ds.created_at
             FROM destruction_schedules ds
             JOIN documents d ON ds.document_id = d.id
             JOIN companies c ON d.company_id = c.id
             WHERE ds.status = 'scheduled' AND ds.scheduled_date < CURDATE()
             
             UNION ALL
             
             SELECT 
                'retention_expiring' as alert_type,
                'warning' as severity,
                CONCAT('Document retention expiring: ', d.title) as title,
                CONCAT('Retention period ends in 30 days') as message,
                c.name as company_name,
                d.created_at
             FROM documents d
             JOIN companies c ON d.company_id = c.id
             JOIN retention_policies rp ON d.document_type = rp.document_type
             WHERE DATE_ADD(d.created_at, INTERVAL rp.retention_years YEAR) <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
             AND d.id NOT IN (SELECT document_id FROM legal_hold_documents WHERE status = 'active')
             
             ORDER BY created_at DESC
             LIMIT 10",
            []
        );
    }

    /**
     * Additional helper methods
     */
    private function getAllRetentionPolicies()
    {
        return $this->db->fetchAll(
            "SELECT * FROM retention_policies ORDER BY created_at DESC",
            []
        );
    }

    private function getDocumentTypes()
    {
        return $this->db->fetchAll(
            "SELECT DISTINCT document_type FROM documents WHERE document_type IS NOT NULL ORDER BY document_type",
            []
        );
    }

    private function getComplianceRegulations()
    {
        return [
            'GDPR' => 'General Data Protection Regulation',
            'HIPAA' => 'Health Insurance Portability and Accountability Act',
            'SOX' => 'Sarbanes-Oxley Act',
            'PCI_DSS' => 'Payment Card Industry Data Security Standard',
            'FERPA' => 'Family Educational Rights and Privacy Act',
            'CUSTOM' => 'Custom Regulation'
        ];
    }

    private function validateRetentionPolicyData($data)
    {
        $errors = [];
        
        if (empty($data['document_type'])) {
            $errors[] = 'Document type is required';
        }
        
        if (empty($data['retention_years']) || !is_numeric($data['retention_years'])) {
            $errors[] = 'Valid retention period in years is required';
        }
        
        if (empty($data['destruction_method'])) {
            $errors[] = 'Destruction method is required';
        }
        
        if (!empty($errors)) {
            throw new \Exception(implode(', ', $errors));
        }
        
        return $data;
    }

    private function validateLegalHoldData($data)
    {
        $errors = [];
        
        if (empty($data['hold_name'])) {
            $errors[] = 'Hold name is required';
        }
        
        if (empty($data['case_number'])) {
            $errors[] = 'Case number is required';
        }
        
        if (empty($data['hold_reason'])) {
            $errors[] = 'Hold reason is required';
        }
        
        if (!empty($errors)) {
            throw new \Exception(implode(', ', $errors));
        }
        
        return $data;
    }

    private function applyRetentionPolicyToExistingDocuments($policyId, $documentType)
    {
        // Implementation for applying retention policy to existing documents
    }

    private function applyLegalHoldToDocuments($holdId, $documentIds)
    {
        // Implementation for applying legal hold to documents
    }

    private function getScheduledDestructions()
    {
        // Implementation for getting scheduled destructions
        return [];
    }

    private function getDocumentsEligibleForDestruction()
    {
        // Implementation for getting documents eligible for destruction
        return [];
    }

    private function getDestructionStats()
    {
        // Implementation for getting destruction statistics
        return [];
    }

    private function getActiveLegalHolds()
    {
        // Implementation for getting active legal holds
        return [];
    }

    private function getDocumentsUnderLegalHold()
    {
        // Implementation for getting documents under legal hold
        return [];
    }

    private function getLegalHoldStats()
    {
        // Implementation for getting legal hold statistics
        return [];
    }

    private function generateComplianceReport($type, $range)
    {
        // Implementation for generating compliance reports
        return [];
    }

    private function getAvailableReportTypes()
    {
        return [
            'overview' => 'Compliance Overview',
            'retention' => 'Retention Policy Report',
            'destruction' => 'Destruction Schedule Report',
            'legal_holds' => 'Legal Holds Report',
            'audit' => 'Audit Trail Report'
        ];
    }
}
