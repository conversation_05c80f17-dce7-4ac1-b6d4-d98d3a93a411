-- Enhanced Intake Workflow Migration
-- Adds missing columns for physical document management and enhanced tracking

-- Add enhanced tracking columns to document_intake table
ALTER TABLE document_intake 
ADD COLUMN physical_intake_code VARCHAR(100) NULL AFTER reference_number,
ADD COLUMN digitization_required BOOLEAN DEFAULT FALSE AFTER box_id,
ADD COLUMN digitization_status ENUM('pending', 'in_progress', 'completed', 'not_required') DEFAULT 'not_required' AFTER digitization_required,
ADD COLUMN barcode_value VARCHAR(255) NULL AFTER digitization_status,
ADD COLUMN qr_code_value TEXT NULL AFTER barcode_value,
ADD COLUMN barcode_generated_at TIMESTAMP NULL AFTER qr_code_value,
ADD COLUMN retention_period_years INT DEFAULT 7 AFTER barcode_generated_at,
ADD COLUMN destruction_date DATE NULL AFTER retention_period_years,
ADD COLUMN physical_location VARCHAR(255) NULL AFTER destruction_date,
ADD COLUMN sorting_category VARCHAR(100) NULL AFTER physical_location;

-- Add indexes for new columns
ALTER TABLE document_intake
ADD INDEX idx_physical_code (physical_intake_code),
ADD INDEX idx_barcode (barcode_value),
ADD INDEX idx_digitization (digitization_required, digitization_status),
ADD INDEX idx_retention (destruction_date),
ADD INDEX idx_sorting (sorting_category);

-- Add box_id foreign key constraint if not exists
ALTER TABLE document_intake 
ADD CONSTRAINT fk_intake_box 
FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL;

-- Create document_sorting_categories table for physical sorting
CREATE TABLE IF NOT EXISTS document_sorting_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    category_code VARCHAR(20) NOT NULL,
    description TEXT NULL,
    color_code VARCHAR(7) DEFAULT '#3B82F6',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    UNIQUE KEY unique_category_code (company_id, category_code),
    INDEX idx_company_active (company_id, is_active),
    INDEX idx_sort_order (sort_order)
);

-- Insert default sorting categories
INSERT INTO document_sorting_categories (company_id, category_name, category_code, description, color_code, sort_order, created_by) VALUES
(1, 'Human Resources', 'HR', 'Employee records, contracts, and HR documents', '#10B981', 1, 1),
(1, 'Finance', 'FIN', 'Financial records, invoices, and accounting documents', '#F59E0B', 2, 1),
(1, 'Legal', 'LEG', 'Legal documents, contracts, and compliance records', '#EF4444', 3, 1),
(1, 'Operations', 'OPS', 'Operational documents and procedures', '#8B5CF6', 4, 1),
(1, 'Marketing', 'MKT', 'Marketing materials and campaign documents', '#EC4899', 5, 1),
(1, 'General', 'GEN', 'General documents and miscellaneous files', '#6B7280', 6, 1);

-- Create digitization_queue table for tracking digitization requests
CREATE TABLE IF NOT EXISTS digitization_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    intake_id INT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    requested_by INT NOT NULL,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_to INT NULL,
    assigned_at TIMESTAMP NULL,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    estimated_pages INT NULL,
    actual_pages INT NULL,
    scan_quality ENUM('draft', 'standard', 'high', 'archive') DEFAULT 'standard',
    notes TEXT NULL,
    completed_at TIMESTAMP NULL,
    file_path VARCHAR(500) NULL,
    file_size BIGINT NULL,
    
    FOREIGN KEY (intake_id) REFERENCES document_intake(id) ON DELETE CASCADE,
    FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_status_priority (status, priority),
    INDEX idx_assigned (assigned_to, status),
    INDEX idx_requested (requested_by, requested_at)
);

-- Create client_portal_access table for client access tracking
CREATE TABLE IF NOT EXISTS client_portal_access (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    client_name VARCHAR(255) NOT NULL,
    client_id VARCHAR(50) NULL,
    access_token VARCHAR(255) UNIQUE NOT NULL,
    access_level ENUM('view_only', 'request_retrieval', 'full_access') DEFAULT 'view_only',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    login_count INT DEFAULT 0,
    expires_at TIMESTAMP NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_company_client (company_id, client_name),
    INDEX idx_access_token (access_token),
    INDEX idx_active (is_active, expires_at)
);

-- Create retention_policies table for document retention management
CREATE TABLE IF NOT EXISTS retention_policies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    policy_name VARCHAR(255) NOT NULL,
    document_type VARCHAR(100) NOT NULL,
    retention_years INT NOT NULL,
    destruction_method ENUM('shred', 'incinerate', 'digital_delete', 'return_to_client') DEFAULT 'shred',
    auto_destroy BOOLEAN DEFAULT FALSE,
    notification_days INT DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    UNIQUE KEY unique_policy (company_id, document_type),
    INDEX idx_company_active (company_id, is_active),
    INDEX idx_document_type (document_type)
);

-- Insert default retention policies
INSERT INTO retention_policies (company_id, policy_name, document_type, retention_years, destruction_method, created_by) VALUES
(1, 'Financial Records Policy', 'invoice', 7, 'shred', 1),
(1, 'Contract Policy', 'contract', 10, 'return_to_client', 1),
(1, 'HR Records Policy', 'hr_document', 7, 'shred', 1),
(1, 'Legal Documents Policy', 'legal_document', 15, 'shred', 1),
(1, 'General Documents Policy', 'general', 5, 'shred', 1);

-- Update existing intake records with default values
UPDATE document_intake 
SET physical_intake_code = CONCAT('LEGACY-', DATE_FORMAT(created_at, '%Y%m%d'), '-', LPAD(id, 4, '0'))
WHERE physical_intake_code IS NULL;

-- Create view for intake workflow dashboard
CREATE OR REPLACE VIEW intake_workflow_summary AS
SELECT 
    di.id,
    di.reference_number,
    di.physical_intake_code,
    di.client_name,
    di.status,
    di.priority,
    di.digitization_required,
    di.digitization_status,
    di.created_at,
    di.processed_at,
    di.completed_at,
    b.name as bundle_name,
    box.box_id as box_identifier,
    box.storage_location_code,
    dsc.category_name as sorting_category,
    CASE 
        WHEN di.destruction_date IS NOT NULL AND di.destruction_date <= CURDATE() THEN 'overdue'
        WHEN di.destruction_date IS NOT NULL AND di.destruction_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'due_soon'
        ELSE 'current'
    END as retention_status
FROM document_intake di
LEFT JOIN bundles b ON di.bundle_id = b.id
LEFT JOIN boxes box ON di.box_id = box.id
LEFT JOIN document_sorting_categories dsc ON di.sorting_category = dsc.category_code
WHERE di.status != 'cancelled';
