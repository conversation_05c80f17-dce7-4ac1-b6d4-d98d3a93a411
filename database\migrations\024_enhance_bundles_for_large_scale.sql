-- Enhance bundles table for large-scale management
-- Add archive status, improve indexing, and optimize for performance

-- Add archive status column
ALTER TABLE bundles 
ADD COLUMN archive_status ENUM('active', 'archived', 'auto_archived') DEFAULT 'active' AFTER status;

-- Add last_activity_at column for smart archiving
ALTER TABLE bundles 
ADD COLUMN last_activity_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP AFTER updated_at;

-- Add indexes for performance optimization
ALTER TABLE bundles 
ADD INDEX idx_bundles_archive_status (archive_status);

ALTER TABLE bundles 
ADD INDEX idx_bundles_created_at (created_at);

ALTER TABLE bundles 
ADD INDEX idx_bundles_last_activity (last_activity_at);

ALTER TABLE bundles 
ADD INDEX idx_bundles_company_archive_created (company_id, archive_status, created_at);

-- Update existing bundles with last_activity_at
UPDATE bundles 
SET last_activity_at = updated_at 
WHERE last_activity_at IS NULL;

-- Auto-archive bundles older than 1 year with no recent activity
UPDATE bundles 
SET archive_status = 'auto_archived' 
WHERE archive_status = 'active' 
  AND last_activity_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)
  AND created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- Add comments for documentation
ALTER TABLE bundles 
MODIFY COLUMN archive_status ENUM('active', 'archived', 'auto_archived') DEFAULT 'active' 
COMMENT 'Archive status: active for current bundles, archived for manually archived, auto_archived for system archived';

ALTER TABLE bundles 
MODIFY COLUMN last_activity_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP 
COMMENT 'Last activity timestamp for smart archiving and filtering';
