<?php
echo "<h1>Test File Working</h1>";
echo "<p>This file is accessible at: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";

// Test if our files exist
$files = [
    'super-admin.php',
    'debug-routing.php', 
    'simple-test.php',
    'direct-route-test.php'
];

echo "<h2>File Existence Check</h2>";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ {$file} exists</p>";
    } else {
        echo "<p>❌ {$file} does not exist</p>";
    }
}

echo "<h2>Directory Contents</h2>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
        echo "<p>📄 {$file}</p>";
    }
}
?>
