<?php

namespace App\Controllers;

/**
 * Request Controller
 * 
 * Manages document requests from clients (retrieval, digitization, destruction)
 * Admin-side request management
 */
class RequestController extends BaseController
{
    /**
     * Display requests list
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager']);
        
        try {
            // Get filter parameters
            $status = $_GET['status'] ?? '';
            $type = $_GET['type'] ?? '';
            $priority = $_GET['priority'] ?? '';
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 20;
            $offset = ($page - 1) * $limit;

            // Build query
            $where = ["service_provider_company_id = ?"];
            $params = [$this->user['company_id']];

            if (!empty($status)) {
                $where[] = "status = ?";
                $params[] = $status;
            }

            if (!empty($type)) {
                $where[] = "request_type = ?";
                $params[] = $type;
            }

            if (!empty($priority)) {
                $where[] = "priority = ?";
                $params[] = $priority;
            }

            $whereClause = implode(' AND ', $where);

            // Get requests
            $requests = $this->db->fetchAll(
                "SELECT dr.*, 
                        client.first_name as client_first_name, client.last_name as client_last_name,
                        client.email as client_email,
                        cc.client_company_name,
                        assigned.first_name as assigned_first_name, assigned.last_name as assigned_last_name,
                        approved.first_name as approved_first_name, approved.last_name as approved_last_name
                 FROM document_requests dr
                 JOIN users client ON dr.client_user_id = client.id
                 LEFT JOIN client_companies cc ON dr.client_user_id = cc.client_user_id 
                     AND dr.service_provider_company_id = cc.service_provider_company_id
                 LEFT JOIN users assigned ON dr.assigned_to = assigned.id
                 LEFT JOIN users approved ON dr.approved_by = approved.id
                 WHERE {$whereClause}
                 ORDER BY dr.requested_date DESC
                 LIMIT ? OFFSET ?",
                array_merge($params, [$limit, $offset])
            );

            // Get total count
            $totalCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM document_requests WHERE {$whereClause}",
                $params
            );

            // Get filter options
            $filterOptions = $this->getRequestFilterOptions();

            $this->view('requests/index', [
                'title' => 'Document Requests',
                'requests' => $requests,
                'totalCount' => $totalCount,
                'currentPage' => $page,
                'totalPages' => ceil($totalCount / $limit),
                'filterOptions' => $filterOptions,
                'filters' => [
                    'status' => $status,
                    'type' => $type,
                    'priority' => $priority
                ]
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading requests: ' . $e->getMessage(), 'error');
            $this->redirect('/dashboard');
        }
    }

    /**
     * Show request details
     */
    public function show($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager', 'editor']);
        
        try {
            $request = $this->getRequestById($id);
            if (!$request) {
                throw new \Exception('Request not found');
            }

            // Get request activity log
            $activityLog = $this->getRequestActivityLog($id);

            // Get target details (document/bundle/box)
            $targetDetails = $this->getRequestTargetDetails($request['target_type'], $request['target_id']);

            // Get available staff for assignment
            $availableStaff = $this->getAvailableStaff();

            $this->view('requests/show', [
                'title' => 'Request Details - ' . $request['request_number'],
                'request' => $request,
                'activityLog' => $activityLog,
                'targetDetails' => $targetDetails,
                'availableStaff' => $availableStaff
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading request: ' . $e->getMessage(), 'error');
            $this->redirect('/app/requests');
        }
    }

    /**
     * Approve request
     */
    public function approve($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager']);
        
        try {
            $request = $this->getRequestById($id);
            if (!$request) {
                throw new \Exception('Request not found');
            }

            if ($request['status'] !== 'pending') {
                throw new \Exception('Request is not in pending status');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'admin_notes' => 'max:1000',
                'assigned_to' => 'integer'
            ]);

            // Update request
            $this->db->execute(
                "UPDATE document_requests SET 
                 status = 'approved', 
                 approved_by = ?, 
                 approved_date = NOW(),
                 assigned_to = ?,
                 admin_notes = ?,
                 updated_at = NOW()
                 WHERE id = ? AND service_provider_company_id = ?",
                [
                    $this->user['id'],
                    $data['assigned_to'] ?? null,
                    $data['admin_notes'] ?? null,
                    $id,
                    $this->user['company_id']
                ]
            );

            // Log activity
            $this->logRequestActivity($id, 'approved', 'Request approved by admin', 'pending', 'approved');

            // If assigned to staff, log assignment
            if (!empty($data['assigned_to'])) {
                $this->logRequestActivity($id, 'assigned', 'Request assigned to staff member', null, null);
            }

            $this->setFlashMessage('Request approved successfully', 'success');
            $this->redirect("/app/requests/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to approve request: ' . $e->getMessage(), 'error');
            $this->redirect("/app/requests/{$id}");
        }
    }

    /**
     * Reject request
     */
    public function reject($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager']);
        
        try {
            $request = $this->getRequestById($id);
            if (!$request) {
                throw new \Exception('Request not found');
            }

            if ($request['status'] !== 'pending') {
                throw new \Exception('Request is not in pending status');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'admin_notes' => 'required|max:1000'
            ]);

            // Update request
            $this->db->execute(
                "UPDATE document_requests SET 
                 status = 'rejected', 
                 approved_by = ?, 
                 approved_date = NOW(),
                 admin_notes = ?,
                 updated_at = NOW()
                 WHERE id = ? AND service_provider_company_id = ?",
                [
                    $this->user['id'],
                    $data['admin_notes'],
                    $id,
                    $this->user['company_id']
                ]
            );

            // Log activity
            $this->logRequestActivity($id, 'rejected', 'Request rejected by admin', 'pending', 'rejected');

            $this->setFlashMessage('Request rejected', 'success');
            $this->redirect("/app/requests/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to reject request: ' . $e->getMessage(), 'error');
            $this->redirect("/app/requests/{$id}");
        }
    }

    /**
     * Mark request as completed
     */
    public function complete($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager', 'editor']);
        
        try {
            $request = $this->getRequestById($id);
            if (!$request) {
                throw new \Exception('Request not found');
            }

            if (!in_array($request['status'], ['approved', 'in_progress'])) {
                throw new \Exception('Request is not in approved or in_progress status');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'completion_notes' => 'required|max:1000'
            ]);

            // Update request
            $this->db->execute(
                "UPDATE document_requests SET 
                 status = 'completed', 
                 completed_by = ?, 
                 completed_date = NOW(),
                 completion_notes = ?,
                 updated_at = NOW()
                 WHERE id = ? AND service_provider_company_id = ?",
                [
                    $this->user['id'],
                    $data['completion_notes'],
                    $id,
                    $this->user['company_id']
                ]
            );

            // Log activity
            $this->logRequestActivity($id, 'completed', 'Request completed', $request['status'], 'completed');

            $this->setFlashMessage('Request marked as completed', 'success');
            $this->redirect("/app/requests/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to complete request: ' . $e->getMessage(), 'error');
            $this->redirect("/app/requests/{$id}");
        }
    }

    /**
     * Get request by ID
     */
    private function getRequestById($id)
    {
        return $this->db->fetch(
            "SELECT dr.*, 
                    client.first_name as client_first_name, client.last_name as client_last_name,
                    client.email as client_email,
                    cc.client_company_name,
                    assigned.first_name as assigned_first_name, assigned.last_name as assigned_last_name,
                    approved.first_name as approved_first_name, approved.last_name as approved_last_name,
                    completed.first_name as completed_first_name, completed.last_name as completed_last_name
             FROM document_requests dr
             JOIN users client ON dr.client_user_id = client.id
             LEFT JOIN client_companies cc ON dr.client_user_id = cc.client_user_id 
                 AND dr.service_provider_company_id = cc.service_provider_company_id
             LEFT JOIN users assigned ON dr.assigned_to = assigned.id
             LEFT JOIN users approved ON dr.approved_by = approved.id
             LEFT JOIN users completed ON dr.completed_by = completed.id
             WHERE dr.id = ? AND dr.service_provider_company_id = ?",
            [$id, $this->user['company_id']]
        );
    }

    /**
     * Log request activity
     */
    private function logRequestActivity($requestId, $action, $description, $oldStatus, $newStatus)
    {
        try {
            $this->db->execute(
                "INSERT INTO request_activity_log (
                    request_id, action, description, old_status, new_status, user_id
                ) VALUES (?, ?, ?, ?, ?, ?)",
                [$requestId, $action, $description, $oldStatus, $newStatus, $this->user['id']]
            );
        } catch (\Exception $e) {
            error_log("Failed to log request activity: " . $e->getMessage());
        }
    }
}
