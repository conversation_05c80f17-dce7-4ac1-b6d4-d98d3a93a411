<?php

namespace App\Services;

use App\Services\ServiceFactory;

/**
 * Billing Event Logger
 * 
 * Helper class to automatically log billing events throughout the application
 * This should be used by other services to track billable activities
 */
class BillingEventLogger
{
    private static $billingService = null;

    /**
     * Initialize the billing event logger
     */
    public static function initialize()
    {
        if (self::$billingService === null) {
            self::$billingService = ServiceFactory::getBillingService();
        }
    }

    /**
     * Log a new intake event
     */
    public static function logIntakeCreated($clientId, $intakeId, $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'intake.new',
            'event_type' => 'intake.created',
            'entity_type' => 'intake',
            'entity_id' => $intakeId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log box barcode generation
     */
    public static function logBoxBarcodeGenerated($clientId, $boxId, $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'barcode.box',
            'event_type' => 'box.barcode_generated',
            'entity_type' => 'box',
            'entity_id' => $boxId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log bundle barcode generation
     */
    public static function logBundleBarcodeGenerated($clientId, $bundleId, $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'barcode.bundle',
            'event_type' => 'bundle.barcode_generated',
            'entity_type' => 'bundle',
            'entity_id' => $bundleId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log box handling event
     */
    public static function logBoxHandling($clientId, $boxId, $handlingType = 'general', $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'box.handling',
            'event_type' => "box.{$handlingType}",
            'entity_type' => 'box',
            'entity_id' => $boxId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log box packing/repacking
     */
    public static function logBoxPacking($clientId, $boxId, $packingType = 'packing', $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'box.packing',
            'event_type' => "box.{$packingType}",
            'entity_type' => 'box',
            'entity_id' => $boxId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log bundle/file handling
     */
    public static function logBundleHandling($clientId, $bundleId, $handlingType = 'general', $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'bundle.handling',
            'event_type' => "bundle.{$handlingType}",
            'entity_type' => 'bundle',
            'entity_id' => $bundleId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log box registration/indexing
     */
    public static function logBoxRegistration($clientId, $boxId, $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'box.registration',
            'event_type' => 'box.registered',
            'entity_type' => 'box',
            'entity_id' => $boxId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log search operations
     */
    public static function logSearch($clientId, $searchType, $entityId = null, $referenceNumber = null)
    {
        self::initialize();
        
        $serviceCode = $searchType === 'box' ? 'search.box' : 'search.bundle';
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => $serviceCode,
            'event_type' => "search.{$searchType}",
            'entity_type' => $searchType,
            'entity_id' => $entityId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log delivery to client
     */
    public static function logDeliveryToClient($clientId, $entityType, $entityId, $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'delivery.to_client',
            'event_type' => 'delivery.to_client',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log collection from client
     */
    public static function logCollectionFromClient($clientId, $entityType, $entityId, $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'collection.from_client',
            'event_type' => 'collection.from_client',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log permanent withdrawal
     */
    public static function logPermanentWithdrawal($clientId, $entityType, $entityId, $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'withdrawal.permanent',
            'event_type' => 'withdrawal.permanent',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log document destruction
     */
    public static function logDestruction($clientId, $weightKg, $entityType = null, $entityId = null, $referenceNumber = null)
    {
        self::initialize();
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => 'destruction.per_kg',
            'event_type' => 'destruction.completed',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'reference_number' => $referenceNumber,
            'quantity' => $weightKg
        ]);
    }

    /**
     * Log supply usage (boxes, lids, etc.)
     */
    public static function logSupplyUsage($clientId, $supplyType, $quantity = 1, $referenceNumber = null)
    {
        self::initialize();
        
        $serviceCode = "supply.{$supplyType}";
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => $serviceCode,
            'event_type' => "supply.{$supplyType}_used",
            'entity_type' => 'supply',
            'reference_number' => $referenceNumber,
            'quantity' => $quantity
        ]);
    }

    /**
     * Log digital services
     */
    public static function logDigitalService($clientId, $serviceType, $entityId = null, $referenceNumber = null)
    {
        self::initialize();
        
        $serviceCode = "digital.{$serviceType}";
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => $serviceCode,
            'event_type' => "digital.{$serviceType}",
            'entity_type' => 'document',
            'entity_id' => $entityId,
            'reference_number' => $referenceNumber,
            'quantity' => 1
        ]);
    }

    /**
     * Log monthly storage charges (typically run as a batch job)
     */
    public static function logMonthlyStorage($clientId, $storageType, $quantity, $referenceNumber = null)
    {
        self::initialize();
        
        $serviceCode = "storage.{$storageType}";
        
        return self::$billingService->logServiceEvent([
            'client_id' => $clientId,
            'service_code' => $serviceCode,
            'event_type' => 'storage.monthly_charge',
            'entity_type' => 'storage',
            'reference_number' => $referenceNumber,
            'quantity' => $quantity
        ]);
    }

    /**
     * Batch log multiple events (useful for bulk operations)
     */
    public static function logBatchEvents($events)
    {
        self::initialize();
        
        $results = [];
        foreach ($events as $event) {
            $results[] = self::$billingService->logServiceEvent($event);
        }
        
        return $results;
    }
}
