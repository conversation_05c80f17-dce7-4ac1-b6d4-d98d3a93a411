# Billing Module Implementation Guide

## Overview

The billing module provides comprehensive tracking and invoicing for physical document management services. It automatically logs billable events, calculates costs, and generates professional invoices.

## ✅ Features Implemented

### 1. **Service Rate Management**
- Comprehensive rate table with 25+ predefined services
- Categorized services (processing, handling, search, delivery, storage, supplies, digital)
- Rate versioning and activation controls
- Easy rate updates through admin interface

### 2. **Automatic Event Logging**
- Real-time billing event capture
- Detailed audit trail with user, timestamp, and context
- Flexible quantity and rate handling
- Entity linking (box, bundle, document references)

### 3. **Invoice Generation**
- Monthly invoice automation
- Professional invoice layout with line items
- Tax calculation support
- PDF generation ready
- Unique invoice numbering system

### 4. **Client Billing Dashboard**
- Real-time running totals
- Current month unbilled services view
- Invoice history and status tracking
- Payment recording and tracking

### 5. **Integration Framework**
- `BillingEventLogger` helper class for easy integration
- Service Factory pattern for consistent access
- Automatic billing in existing business services

## 🏗️ Architecture

### Database Tables

1. **`billing_rates`** - Service pricing and definitions
2. **`billing_events`** - Individual billable service events
3. **`billing_invoices`** - Generated invoices with totals
4. **`billing_payments`** - Payment tracking and reconciliation

### Key Classes

1. **`BillingService`** - Core billing operations
2. **`BillingEventLogger`** - Helper for logging events
3. **`BillingController`** - Web interface and API
4. **`ServiceFactory`** - Centralized service access

## 📊 Service Rates Implemented

### Processing Services
- New Intake: $80.00 each
- Box Handling: $5.00 each
- Box Packing/Repacking: $10.00 each
- Bundle/File Handling: $5.00 each
- Box Registration: $2.00 each
- Permanent Withdrawal: $10.00 each
- Document Destruction: $3.00 per kg

### Search Services
- Search Box: $5.00 each
- Search Bundle/File: $5.00 each

### Delivery Services
- Deliver to Client: $80.00 each
- Collect from Client: $80.00 each

### Barcode Services
- Box Barcode: $0.20 each
- Bundle Barcode: $0.15 each

### Storage Supplies
- Double Size Document Box: $14.00 each
- Lid for Double Size Box: $4.00 each
- Half Size Document Box: $8.00 each
- Large Size Document Box: $12.00 each
- Drawing Roll: $8.00 each
- Lever Arch Size Box: $6.00 each

### Digital Services
- Email Document: $2.00 each
- CD Production: $15.00 each

### Storage Services (Monthly)
- Small Box Storage: $1.00 per month
- Medium Box Storage: $1.50 per month
- Large Box Storage: $2.00 per month

## 🚀 Quick Start

### 1. Run Migration
```bash
php run_billing_migration.php
```

### 2. Access Billing Dashboard
Navigate to: `/app/billing`

### 3. Log Your First Billing Event
```php
use App\Services\BillingEventLogger;

// Log a new intake
BillingEventLogger::logIntakeCreated($clientId, $intakeId, $referenceNumber);

// Log box barcode generation
BillingEventLogger::logBoxBarcodeGenerated($clientId, $boxId, $boxReference);

// Log search operation
BillingEventLogger::logSearch($clientId, 'box', $boxId, $searchQuery);
```

### 4. Generate Monthly Invoice
1. Go to billing dashboard
2. Click "Generate Invoice"
3. Select client and billing period
4. Review and send to client

## 🔧 Integration Examples

### Automatic Billing in Services

The `BoxHandlingService` has been updated to show integration:

```php
// In createBox method
if (!empty($data['client_id'])) {
    BillingEventLogger::logBoxHandling($data['client_id'], $newBoxId, 'creation', $boxId);
    
    if ($barcodeGenerated) {
        BillingEventLogger::logBoxBarcodeGenerated($data['client_id'], $newBoxId, $boxId);
    }
}
```

### Manual Event Logging

```php
use App\Services\ServiceFactory;

$billingService = ServiceFactory::getBillingService();

$result = $billingService->logServiceEvent([
    'client_id' => 123,
    'service_code' => 'search.box',
    'event_type' => 'search.performed',
    'quantity' => 1,
    'entity_type' => 'box',
    'entity_id' => 456,
    'reference_number' => 'BOX-001',
    'notes' => 'Customer requested urgent search'
]);
```

## 📋 Usage Workflows

### Monthly Billing Process

1. **Throughout the Month**: Services automatically log billing events
2. **Month End**: Generate invoices for all clients
3. **Send Invoices**: Email or print invoices to clients
4. **Track Payments**: Record payments as they arrive
5. **Reconciliation**: Review paid vs unpaid invoices

### Adding New Services

1. Insert new rate in `billing_rates` table
2. Add helper method to `BillingEventLogger`
3. Integrate logging into relevant service
4. Test with sample data

### Client Onboarding

1. Create client company record
2. Set up billing preferences (tax rate, payment terms)
3. Begin logging services immediately
4. Generate first invoice at month-end

## 🎯 Recommended Next Steps

### Phase 2 Enhancements (Recommended)

1. **PDF Invoice Generation**
   - Integrate TCPDF or similar library
   - Professional invoice templates
   - Email delivery automation

2. **Recurring Storage Billing**
   - Monthly batch job for storage charges
   - Automated box counting and billing
   - Storage tier management

3. **Advanced Reporting**
   - Revenue analytics dashboard
   - Client profitability analysis
   - Service usage trends

4. **Payment Integration**
   - Online payment processing
   - Automatic payment reconciliation
   - Payment reminder automation

### Phase 3 Enhancements (Future)

1. **Multi-currency Support**
2. **Complex Tax Calculations**
3. **Contract-based Pricing**
4. **API for External Integrations**

## 🔍 Monitoring & Maintenance

### Key Metrics to Track

- Monthly recurring revenue (MRR)
- Average revenue per client
- Service utilization rates
- Payment collection times
- Outstanding invoice amounts

### Regular Maintenance

- Monthly invoice generation
- Payment reconciliation
- Rate adjustments (annual)
- Client billing preference updates
- Archive old billing data (yearly)

## 🛡️ Security Considerations

- All billing data is company-scoped
- User permissions control access
- Audit trails for all changes
- Secure payment data handling
- Regular backup of billing data

## 📞 Support & Troubleshooting

### Common Issues

1. **Missing Billing Events**: Check service integration points
2. **Incorrect Rates**: Verify active rates in billing_rates table
3. **Invoice Generation Errors**: Check client data completeness
4. **Payment Tracking**: Ensure proper payment recording workflow

### Debug Tools

- Billing event logs in database
- Service status monitoring
- Invoice generation logs
- Payment reconciliation reports

This billing module provides a solid foundation for document management service billing while maintaining flexibility for future enhancements.
