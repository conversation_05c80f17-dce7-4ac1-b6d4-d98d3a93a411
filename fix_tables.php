232@<?php
require_once 'src/autoload.php';

use App\Core\Database;

$db = Database::getInstance();

echo "Fixing table structures...\n";

// Add missing columns to document_intake table
echo "Adding missing columns to document_intake table...\n";
try {
    $db->exec("ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number");
    echo "✓ Added client_name column\n";
} catch (Exception $e) {
    echo "✗ client_name: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name");
    echo "✓ Added client_id column\n";
} catch (Exception $e) {
    echo "✗ client_id: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count");
    echo "✓ Added date_range_start column\n";
} catch (Exception $e) {
    echo "✗ date_range_start: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start");
    echo "✓ Added date_range_end column\n";
} catch (Exception $e) {
    echo "✗ date_range_end: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end");
    echo "✓ Added sensitivity_level column\n";
} catch (Exception $e) {
    echo "✗ sensitivity_level: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level");
    echo "✓ Added department column\n";
} catch (Exception $e) {
    echo "✗ department: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id");
    echo "✓ Added box_id column\n";
} catch (Exception $e) {
    echo "✗ box_id: " . $e->getMessage() . "\n";
}

// Add missing columns to bundles table
echo "\nAdding missing columns to bundles table...\n";
try {
    $db->exec("ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id");
    echo "✓ Added document_type column\n";
} catch (Exception $e) {
    echo "✗ document_type: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type");
    echo "✓ Added year column\n";
} catch (Exception $e) {
    echo "✗ year: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year");
    echo "✓ Added department column\n";
} catch (Exception $e) {
    echo "✗ department: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department");
    echo "✓ Added confidentiality_flag column\n";
} catch (Exception $e) {
    echo "✗ confidentiality_flag: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag");
    echo "✓ Added pages_volume column\n";
} catch (Exception $e) {
    echo "✗ pages_volume: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume");
    echo "✓ Added scan_digitization_status column\n";
} catch (Exception $e) {
    echo "✗ scan_digitization_status: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status");
    echo "✓ Added contents_summary column\n";
} catch (Exception $e) {
    echo "✗ contents_summary: " . $e->getMessage() . "\n";
}

// Add foreign keys
echo "\nAdding foreign keys...\n";
try {
    $db->exec("ALTER TABLE bundles ADD FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL");
    echo "✓ Added bundles.box_id foreign key\n";
} catch (Exception $e) {
    echo "✗ bundles.box_id foreign key: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE document_intake ADD FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL");
    echo "✓ Added document_intake.box_id foreign key\n";
} catch (Exception $e) {
    echo "✗ document_intake.box_id foreign key: " . $e->getMessage() . "\n";
}

// Add indexes
echo "\nAdding indexes...\n";
try {
    $db->exec("CREATE INDEX idx_bundles_document_type ON bundles(document_type)");
    echo "✓ Added document_type index\n";
} catch (Exception $e) {
    echo "✗ document_type index: " . $e->getMessage() . "\n";
}

try {
    $db->exec("CREATE INDEX idx_bundles_year ON bundles(year)");
    echo "✓ Added year index\n";
} catch (Exception $e) {
    echo "✗ year index: " . $e->getMessage() . "\n";
}

try {
    $db->exec("CREATE INDEX idx_bundles_department ON bundles(department)");
    echo "✓ Added department index\n";
} catch (Exception $e) {
    echo "✗ department index: " . $e->getMessage() . "\n";
}

// Add missing columns to warehouses table for capacity tracking
echo "\nAdding missing columns to warehouses table...\n";
try {
    $db->exec("ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'");
    echo "✓ Added capacity_percentage column\n";
} catch (Exception $e) {
    echo "✗ capacity_percentage: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'");
    echo "✓ Added efficiency_score column\n";
} catch (Exception $e) {
    echo "✗ efficiency_score: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'");
    echo "✓ Added last_capacity_update column\n";
} catch (Exception $e) {
    echo "✗ last_capacity_update: " . $e->getMessage() . "\n";
}

// Update existing warehouses to have some default capacity data
echo "\nUpdating existing warehouses with default capacity data...\n";
try {
    $db->exec("UPDATE warehouses SET capacity_percentage = 0.00, efficiency_score = 0.00 WHERE capacity_percentage IS NULL OR efficiency_score IS NULL");
    echo "✓ Updated existing warehouses with default capacity data\n";
} catch (Exception $e) {
    echo "✗ Failed to update existing warehouses: " . $e->getMessage() . "\n";
}

// Create missing system_performance_metrics table
echo "\nCreating missing system_performance_metrics table...\n";
try {
    $db->exec("CREATE TABLE IF NOT EXISTS system_performance_metrics (
        id INT PRIMARY KEY AUTO_INCREMENT,
        metric_name VARCHAR(100) NOT NULL,
        metric_category ENUM('database', 'storage', 'network', 'application', 'warehouse') NOT NULL,
        metric_value DECIMAL(15,4) NOT NULL,
        metric_unit VARCHAR(20) NOT NULL COMMENT 'Unit of measurement (ms, MB, %, etc.)',
        threshold_warning DECIMAL(15,4) NULL,
        threshold_critical DECIMAL(15,4) NULL,
        status ENUM('normal', 'warning', 'critical') DEFAULT 'normal',
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_performance_metrics_name (metric_name),
        INDEX idx_performance_metrics_category (metric_category),
        INDEX idx_performance_metrics_status (status),
        INDEX idx_performance_metrics_recorded_at (recorded_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created system_performance_metrics table\n";
} catch (Exception $e) {
    echo "✗ system_performance_metrics table: " . $e->getMessage() . "\n";
}

// Insert sample performance metrics
echo "\nInserting sample performance metrics...\n";
try {
    $db->exec("INSERT IGNORE INTO system_performance_metrics (metric_name, metric_category, metric_value, metric_unit, threshold_warning, threshold_critical) VALUES
        ('Database Response Time', 'database', 45.50, 'ms', 100.00, 200.00),
        ('Storage Usage', 'storage', 68.75, '%', 80.00, 90.00),
        ('Active Connections', 'database', 25, 'connections', 50, 80),
        ('Memory Usage', 'application', 512.75, 'MB', 1024.00, 1536.00),
        ('Warehouse Utilization', 'warehouse', 75.25, '%', 85.00, 95.00)");
    echo "✓ Inserted sample performance metrics\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample performance metrics: " . $e->getMessage() . "\n";
}

// Create missing system_alerts table
echo "\nCreating missing system_alerts table...\n";
try {
    $db->exec("CREATE TABLE IF NOT EXISTS system_alerts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        alert_type VARCHAR(50) NOT NULL,
        severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        status ENUM('active', 'acknowledged', 'resolved') DEFAULT 'active',
        source_system VARCHAR(100) NULL,
        affected_component VARCHAR(100) NULL,
        threshold_value DECIMAL(10,2) NULL,
        current_value DECIMAL(10,2) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        acknowledged_at TIMESTAMP NULL,
        acknowledged_by INT NULL,
        resolved_at TIMESTAMP NULL,
        resolved_by INT NULL,
        FOREIGN KEY (acknowledged_by) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_system_alerts_status (status),
        INDEX idx_system_alerts_severity (severity),
        INDEX idx_system_alerts_type (alert_type),
        INDEX idx_system_alerts_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created system_alerts table\n";
} catch (Exception $e) {
    echo "✗ system_alerts table: " . $e->getMessage() . "\n";
}

// Insert sample system alerts
echo "\nInserting sample system alerts...\n";
try {
    $db->exec("INSERT IGNORE INTO system_alerts (alert_type, severity, title, message, source_system) VALUES
        ('performance', 'medium', 'Database Response Time High', 'Database response time exceeded 500ms threshold', 'database'),
        ('capacity', 'high', 'Storage Capacity Warning', 'Storage usage has reached 85% capacity', 'storage'),
        ('security', 'low', 'Multiple Login Attempts', 'User attempted login 5 times in 10 minutes', 'authentication')");
    echo "✓ Inserted sample system alerts\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample system alerts: " . $e->getMessage() . "\n";
}

// Create missing box_movements table
echo "\nCreating missing box_movements table...\n";
try {
    $db->exec("CREATE TABLE IF NOT EXISTS box_movements (
        id INT PRIMARY KEY AUTO_INCREMENT,
        box_id INT NOT NULL,
        from_location VARCHAR(255) NULL COMMENT 'Previous location code',
        to_location VARCHAR(255) NOT NULL COMMENT 'New location code',
        movement_type ENUM('intake', 'relocation', 'retrieval', 'return', 'disposal') DEFAULT 'relocation',
        moved_by INT NOT NULL,
        reason TEXT NULL COMMENT 'Reason for movement',
        notes TEXT NULL COMMENT 'Additional notes',
        movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
        FOREIGN KEY (moved_by) REFERENCES users(id) ON DELETE RESTRICT,
        INDEX idx_box_movements_box_id (box_id),
        INDEX idx_box_movements_moved_by (moved_by),
        INDEX idx_box_movements_date (movement_date),
        INDEX idx_box_movements_type (movement_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created box_movements table\n";
} catch (Exception $e) {
    echo "✗ box_movements table: " . $e->getMessage() . "\n";
}

// Insert sample box movements
echo "\nInserting sample box movements...\n";
try {
    $db->exec("INSERT IGNORE INTO box_movements (box_id, from_location, to_location, movement_type, moved_by, reason) VALUES
        (1, NULL, 'WH-R1-S1-B01', 'intake', 1, 'Initial placement'),
        (2, NULL, 'WH-R1-S1-B02', 'intake', 1, 'Initial placement'),
        (3, NULL, 'WH-R1-S2-B01', 'intake', 1, 'Initial placement')");
    echo "✓ Inserted sample box movements\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample box movements: " . $e->getMessage() . "\n";
}

// Create missing warehouse_zones table
echo "\nCreating missing warehouse_zones table...\n";
try {
    $db->exec("CREATE TABLE IF NOT EXISTS warehouse_zones (
        id INT PRIMARY KEY AUTO_INCREMENT,
        warehouse_id INT NOT NULL,
        zone_name VARCHAR(100) NOT NULL,
        zone_code VARCHAR(20) NOT NULL,
        zone_type ENUM('storage', 'processing', 'staging', 'restricted') DEFAULT 'storage',
        capacity INT DEFAULT 0 COMMENT 'Maximum boxes in this zone',
        current_usage INT DEFAULT 0 COMMENT 'Current boxes in this zone',
        temperature_controlled BOOLEAN DEFAULT FALSE,
        security_level ENUM('low', 'medium', 'high', 'maximum') DEFAULT 'medium',
        description TEXT NULL,
        status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
        UNIQUE KEY unique_warehouse_zone_code (warehouse_id, zone_code),
        INDEX idx_warehouse_zones_warehouse_id (warehouse_id),
        INDEX idx_warehouse_zones_status (status),
        INDEX idx_warehouse_zones_type (zone_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created warehouse_zones table\n";
} catch (Exception $e) {
    echo "✗ warehouse_zones table: " . $e->getMessage() . "\n";
}

// Insert sample warehouse zones
echo "\nInserting sample warehouse zones...\n";
try {
    $db->exec("INSERT IGNORE INTO warehouse_zones (warehouse_id, zone_name, zone_code, zone_type, capacity, description) VALUES
        (1, 'Storage Zone A', 'ZONE-A', 'storage', 500, 'Main storage area for regular documents'),
        (1, 'Storage Zone B', 'ZONE-B', 'storage', 300, 'Secondary storage area'),
        (1, 'Processing Zone', 'ZONE-P', 'processing', 50, 'Document processing and sorting area'),
        (1, 'Restricted Zone', 'ZONE-R', 'restricted', 100, 'High-security storage for confidential documents')");
    echo "✓ Inserted sample warehouse zones\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample warehouse zones: " . $e->getMessage() . "\n";
}

// Create missing capacity_analytics table
echo "\nCreating missing capacity_analytics table...\n";
try {
    $db->exec("CREATE TABLE IF NOT EXISTS capacity_analytics (
        id INT PRIMARY KEY AUTO_INCREMENT,
        warehouse_id INT NOT NULL,
        zone_id INT NULL,
        total_capacity INT NOT NULL,
        used_capacity INT NOT NULL,
        available_capacity INT NOT NULL,
        utilization_percentage DECIMAL(5,2) NOT NULL,
        efficiency_score DECIMAL(5,2) NULL COMMENT 'Calculated efficiency score',
        recorded_date DATE NOT NULL,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
        FOREIGN KEY (zone_id) REFERENCES warehouse_zones(id) ON DELETE SET NULL,
        UNIQUE KEY unique_daily_record (warehouse_id, zone_id, recorded_date),
        INDEX idx_capacity_analytics_warehouse_id (warehouse_id),
        INDEX idx_capacity_analytics_recorded_date (recorded_date),
        INDEX idx_capacity_analytics_utilization (utilization_percentage)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created capacity_analytics table\n";
} catch (Exception $e) {
    echo "✗ capacity_analytics table: " . $e->getMessage() . "\n";
}

// Insert sample capacity analytics
echo "\nInserting sample capacity analytics...\n";
try {
    $db->exec("INSERT IGNORE INTO capacity_analytics (warehouse_id, total_capacity, used_capacity, available_capacity, utilization_percentage, recorded_date) VALUES
        (1, 1000, 750, 250, 75.00, CURDATE()),
        (1, 1000, 720, 280, 72.00, DATE_SUB(CURDATE(), INTERVAL 1 DAY)),
        (1, 1000, 680, 320, 68.00, DATE_SUB(CURDATE(), INTERVAL 2 DAY)),
        (1, 1000, 650, 350, 65.00, DATE_SUB(CURDATE(), INTERVAL 3 DAY))");
    echo "✓ Inserted sample capacity analytics\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample capacity analytics: " . $e->getMessage() . "\n";
}

// Create missing warehouse_efficiency_reports table
echo "\nCreating missing warehouse_efficiency_reports table...\n";
try {
    $db->exec("CREATE TABLE IF NOT EXISTS warehouse_efficiency_reports (
        id INT PRIMARY KEY AUTO_INCREMENT,
        warehouse_id INT NOT NULL,
        report_date DATE NOT NULL,
        total_movements INT DEFAULT 0,
        avg_movement_time DECIMAL(8,2) DEFAULT 0 COMMENT 'Average time in minutes',
        staff_productivity_score DECIMAL(5,2) DEFAULT 0,
        space_utilization_score DECIMAL(5,2) DEFAULT 0,
        overall_efficiency_score DECIMAL(5,2) DEFAULT 0,
        bottlenecks_identified JSON NULL COMMENT 'Identified bottlenecks and issues',
        recommendations JSON NULL COMMENT 'System recommendations',
        generated_by INT NULL,
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
        FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_daily_report (warehouse_id, report_date),
        INDEX idx_efficiency_reports_warehouse_id (warehouse_id),
        INDEX idx_efficiency_reports_date (report_date),
        INDEX idx_efficiency_reports_efficiency_score (overall_efficiency_score)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created warehouse_efficiency_reports table\n";
} catch (Exception $e) {
    echo "✗ warehouse_efficiency_reports table: " . $e->getMessage() . "\n";
}

// Insert sample efficiency report
echo "\nInserting sample efficiency report...\n";
try {
    $db->exec("INSERT IGNORE INTO warehouse_efficiency_reports (warehouse_id, report_date, total_movements, avg_movement_time, staff_productivity_score, space_utilization_score, overall_efficiency_score) VALUES
        (1, CURDATE(), 45, 12.5, 85.5, 75.0, 80.25)");
    echo "✓ Inserted sample efficiency report\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample efficiency report: " . $e->getMessage() . "\n";
}

// Add missing columns to barcodes table
echo "\nAdding missing columns to barcodes table...\n";
try {
    $db->exec("ALTER TABLE barcodes ADD COLUMN last_scanned_at TIMESTAMP NULL COMMENT 'Last time this barcode was scanned'");
    echo "✓ Added last_scanned_at column to barcodes table\n";
} catch (Exception $e) {
    echo "✗ last_scanned_at column: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE barcodes ADD COLUMN barcode_image_path VARCHAR(500) NULL COMMENT 'Path to generated barcode image'");
    echo "✓ Added barcode_image_path column to barcodes table\n";
} catch (Exception $e) {
    echo "✗ barcode_image_path column: " . $e->getMessage() . "\n";
}

// Create missing barcode_audit_trail table
echo "\nCreating missing barcode_audit_trail table...\n";
try {
    $db->exec("CREATE TABLE IF NOT EXISTS barcode_audit_trail (
        id INT PRIMARY KEY AUTO_INCREMENT,
        barcode_value VARCHAR(255) NOT NULL,
        barcode_type ENUM('box', 'bundle', 'document') NOT NULL,
        entity_id INT NOT NULL COMMENT 'ID of the box, bundle, or document',
        action ENUM('generated', 'scanned', 'printed', 'verified', 'invalidated') NOT NULL,
        scanned_by INT NULL,
        scanner_device VARCHAR(100) NULL COMMENT 'Device used for scanning',
        location VARCHAR(255) NULL COMMENT 'Location where scan occurred',
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        additional_data JSON NULL COMMENT 'Additional metadata',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (scanned_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_barcode_audit_barcode (barcode_value),
        INDEX idx_barcode_audit_type (barcode_type),
        INDEX idx_barcode_audit_entity (entity_id),
        INDEX idx_barcode_audit_action (action),
        INDEX idx_barcode_audit_scanned_by (scanned_by),
        INDEX idx_barcode_audit_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created barcode_audit_trail table\n";
} catch (Exception $e) {
    echo "✗ barcode_audit_trail table: " . $e->getMessage() . "\n";
}

// Insert sample barcode audit trail data
echo "\nInserting sample barcode audit trail data...\n";
try {
    $db->exec("INSERT IGNORE INTO barcode_audit_trail (barcode_value, barcode_type, entity_id, action, scanned_by, scanner_device, location) VALUES
        ('BOX001', 'box', 1, 'generated', 1, 'Web Interface', 'Main Warehouse'),
        ('BOX002', 'box', 2, 'generated', 1, 'Web Interface', 'Main Warehouse'),
        ('BOX003', 'box', 3, 'generated', 1, 'Web Interface', 'Main Warehouse'),
        ('BOX001', 'box', 1, 'scanned', 1, 'Mobile Scanner', 'Warehouse Floor'),
        ('BOX002', 'box', 2, 'scanned', 1, 'Mobile Scanner', 'Warehouse Floor')");
    echo "✓ Inserted sample barcode audit trail data\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample barcode audit trail data: " . $e->getMessage() . "\n";
}

// Create missing compliance management tables
echo "\nCreating missing compliance management tables...\n";

// Create retention_policies table
try {
    $db->exec("CREATE TABLE IF NOT EXISTS retention_policies (
        id INT PRIMARY KEY AUTO_INCREMENT,
        document_type VARCHAR(100) NOT NULL,
        retention_years INT NOT NULL,
        destruction_method ENUM('shredding', 'secure_disposal', 'digital_deletion', 'incineration') DEFAULT 'shredding',
        compliance_regulation VARCHAR(100) NULL COMMENT 'Regulatory requirement (SOX, HIPAA, etc.)',
        description TEXT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
        INDEX idx_retention_policies_document_type (document_type),
        INDEX idx_retention_policies_active (is_active),
        INDEX idx_retention_policies_regulation (compliance_regulation)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created retention_policies table\n";
} catch (Exception $e) {
    echo "✗ retention_policies table: " . $e->getMessage() . "\n";
}

// Create destruction_schedules table
try {
    $db->exec("CREATE TABLE IF NOT EXISTS destruction_schedules (
        id INT PRIMARY KEY AUTO_INCREMENT,
        document_id INT NOT NULL,
        scheduled_date DATE NOT NULL,
        destruction_method ENUM('shredding', 'secure_disposal', 'digital_deletion', 'incineration') DEFAULT 'shredding',
        status ENUM('scheduled', 'completed', 'cancelled', 'overdue') DEFAULT 'scheduled',
        reason TEXT NULL,
        completion_date DATE NULL,
        completion_notes TEXT NULL,
        certificate_path VARCHAR(500) NULL COMMENT 'Path to destruction certificate',
        created_by INT NOT NULL,
        completed_by INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
        FOREIGN KEY (completed_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_destruction_schedules_document_id (document_id),
        INDEX idx_destruction_schedules_scheduled_date (scheduled_date),
        INDEX idx_destruction_schedules_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created destruction_schedules table\n";
} catch (Exception $e) {
    echo "✗ destruction_schedules table: " . $e->getMessage() . "\n";
}

// Create legal_holds table
try {
    $db->exec("CREATE TABLE IF NOT EXISTS legal_holds (
        id INT PRIMARY KEY AUTO_INCREMENT,
        hold_name VARCHAR(255) NOT NULL,
        case_number VARCHAR(100) NULL,
        description TEXT NULL,
        hold_reason TEXT NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NULL,
        status ENUM('active', 'released', 'expired') DEFAULT 'active',
        release_reason TEXT NULL,
        created_by INT NOT NULL,
        released_by INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
        FOREIGN KEY (released_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_legal_holds_status (status),
        INDEX idx_legal_holds_start_date (start_date),
        INDEX idx_legal_holds_case_number (case_number)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created legal_holds table\n";
} catch (Exception $e) {
    echo "✗ legal_holds table: " . $e->getMessage() . "\n";
}

// Create legal_hold_documents table
try {
    $db->exec("CREATE TABLE IF NOT EXISTS legal_hold_documents (
        id INT PRIMARY KEY AUTO_INCREMENT,
        legal_hold_id INT NOT NULL,
        document_id INT NOT NULL,
        status ENUM('active', 'released') DEFAULT 'active',
        added_date DATE NOT NULL,
        released_date DATE NULL,
        notes TEXT NULL,
        added_by INT NOT NULL,
        released_by INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (legal_hold_id) REFERENCES legal_holds(id) ON DELETE CASCADE,
        FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
        FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE RESTRICT,
        FOREIGN KEY (released_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_hold_document (legal_hold_id, document_id),
        INDEX idx_legal_hold_documents_legal_hold_id (legal_hold_id),
        INDEX idx_legal_hold_documents_document_id (document_id),
        INDEX idx_legal_hold_documents_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created legal_hold_documents table\n";
} catch (Exception $e) {
    echo "✗ legal_hold_documents table: " . $e->getMessage() . "\n";
}

// Create compliance_audit_log table
try {
    $db->exec("CREATE TABLE IF NOT EXISTS compliance_audit_log (
        id INT PRIMARY KEY AUTO_INCREMENT,
        audit_type ENUM('retention_policy', 'destruction', 'legal_hold', 'access', 'modification') NOT NULL,
        entity_type ENUM('document', 'bundle', 'box', 'policy', 'legal_hold') NOT NULL,
        entity_id INT NOT NULL,
        action ENUM('created', 'updated', 'deleted', 'accessed', 'applied', 'released') NOT NULL,
        old_values JSON NULL COMMENT 'Previous values before change',
        new_values JSON NULL COMMENT 'New values after change',
        user_id INT NOT NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        compliance_regulation VARCHAR(100) NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
        INDEX idx_compliance_audit_log_audit_type (audit_type),
        INDEX idx_compliance_audit_log_entity (entity_type, entity_id),
        INDEX idx_compliance_audit_log_action (action),
        INDEX idx_compliance_audit_log_user_id (user_id),
        INDEX idx_compliance_audit_log_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created compliance_audit_log table\n";
} catch (Exception $e) {
    echo "✗ compliance_audit_log table: " . $e->getMessage() . "\n";
}

// Insert sample compliance data
echo "\nInserting sample compliance data...\n";

// Insert sample retention policies
try {
    $db->exec("INSERT IGNORE INTO retention_policies (document_type, retention_years, destruction_method, compliance_regulation, description, created_by) VALUES
        ('Financial Records', 7, 'shredding', 'SOX', 'Financial documents must be retained for 7 years per Sarbanes-Oxley Act', 1),
        ('Employee Records', 5, 'secure_disposal', 'FLSA', 'Employee records retention per Fair Labor Standards Act', 1),
        ('Medical Records', 10, 'secure_disposal', 'HIPAA', 'Medical records retention per HIPAA requirements', 1),
        ('Tax Documents', 7, 'shredding', 'IRS', 'Tax document retention per IRS guidelines', 1),
        ('Contracts', 10, 'digital_deletion', 'General', 'Contract documents retention policy', 1)");
    echo "✓ Inserted sample retention policies\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample retention policies: " . $e->getMessage() . "\n";
}

// Insert sample legal holds
try {
    $db->exec("INSERT IGNORE INTO legal_holds (hold_name, case_number, description, hold_reason, start_date, created_by) VALUES
        ('Litigation Hold - ABC Corp', 'CASE-2024-001', 'Legal hold for pending litigation', 'Pending lawsuit regarding contract dispute', CURDATE(), 1),
        ('Regulatory Investigation', 'REG-2024-002', 'Hold for regulatory compliance investigation', 'SEC investigation into financial reporting', CURDATE(), 1),
        ('Employment Dispute Hold', 'EMP-2024-003', 'Hold for employment-related legal matter', 'Discrimination lawsuit filed by former employee', DATE_SUB(CURDATE(), INTERVAL 30 DAY), 1)");
    echo "✓ Inserted sample legal holds\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample legal holds: " . $e->getMessage() . "\n";
}

// Insert sample destruction schedules (if documents exist)
try {
    $documents = $db->fetchAll("SELECT id FROM documents LIMIT 3", []);
    if (!empty($documents)) {
        $destructionSchedules = [];
        foreach ($documents as $index => $document) {
            $scheduledDate = date('Y-m-d', strtotime('+' . (30 + $index * 30) . ' days'));
            $destructionSchedules[] = "({$document['id']}, '{$scheduledDate}', 'shredding', 'Retention period expired', 1)";
        }
        if (!empty($destructionSchedules)) {
            $db->exec("INSERT IGNORE INTO destruction_schedules (document_id, scheduled_date, destruction_method, reason, created_by) VALUES " . implode(', ', $destructionSchedules));
        }
        echo "✓ Inserted sample destruction schedules\n";
    } else {
        echo "- No documents found, skipping destruction schedules\n";
    }
} catch (Exception $e) {
    echo "✗ Failed to insert sample destruction schedules: " . $e->getMessage() . "\n";
}

// Insert sample compliance audit log entries
try {
    $db->exec("INSERT IGNORE INTO compliance_audit_log (audit_type, entity_type, entity_id, action, user_id, compliance_regulation, notes) VALUES
        ('retention_policy', 'policy', 1, 'created', 1, 'SOX', 'Created financial records retention policy'),
        ('legal_hold', 'legal_hold', 1, 'created', 1, 'General', 'Created legal hold for litigation case'),
        ('destruction', 'document', 1, 'created', 1, 'SOX', 'Scheduled document for destruction'),
        ('access', 'document', 1, 'accessed', 1, 'HIPAA', 'Document accessed for compliance review')");
    echo "✓ Inserted sample compliance audit log entries\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample compliance audit log entries: " . $e->getMessage() . "\n";
}

// Add missing columns to documents table
echo "\nAdding missing columns to documents table...\n";
try {
    $db->exec("ALTER TABLE documents ADD COLUMN reference_number VARCHAR(100) NULL COMMENT 'Document reference number for tracking'");
    echo "✓ Added reference_number column to documents table\n";
} catch (Exception $e) {
    echo "✗ reference_number column: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE documents ADD COLUMN box_id INT NULL COMMENT 'Physical box where document is stored'");
    echo "✓ Added box_id column to documents table\n";
} catch (Exception $e) {
    echo "✗ box_id column: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE documents ADD COLUMN warehouse_id INT NULL COMMENT 'Warehouse where document is stored'");
    echo "✓ Added warehouse_id column to documents table\n";
} catch (Exception $e) {
    echo "✗ warehouse_id column: " . $e->getMessage() . "\n";
}

// Add foreign keys for new columns
try {
    $db->exec("ALTER TABLE documents ADD FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL");
    echo "✓ Added documents.box_id foreign key\n";
} catch (Exception $e) {
    echo "✗ documents.box_id foreign key: " . $e->getMessage() . "\n";
}

try {
    $db->exec("ALTER TABLE documents ADD FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE SET NULL");
    echo "✓ Added documents.warehouse_id foreign key\n";
} catch (Exception $e) {
    echo "✗ documents.warehouse_id foreign key: " . $e->getMessage() . "\n";
}

// Update existing documents with sample reference numbers
echo "\nUpdating existing documents with reference numbers...\n";
try {
    $db->exec("UPDATE documents SET reference_number = CONCAT('DOC-', YEAR(created_at), '-', LPAD(id, 6, '0')) WHERE reference_number IS NULL");
    echo "✓ Updated existing documents with reference numbers\n";
} catch (Exception $e) {
    echo "✗ Failed to update document reference numbers: " . $e->getMessage() . "\n";
}

// Fix column types for custom intake options
echo "\nFixing column types for custom intake options...\n";

// Change source column from ENUM to VARCHAR to allow custom values
try {
    $db->exec("ALTER TABLE document_intake MODIFY COLUMN source VARCHAR(100) NOT NULL COMMENT 'Document source - allows custom values'");
    echo "✓ Changed source column to VARCHAR(100) to allow custom values\n";
} catch (Exception $e) {
    echo "✗ source column modification: " . $e->getMessage() . "\n";
}

// Change document_type column from VARCHAR(50) to VARCHAR(100) for longer custom types
try {
    $db->exec("ALTER TABLE document_intake MODIFY COLUMN document_type VARCHAR(100) NOT NULL COMMENT 'Document type - allows custom values'");
    echo "✓ Changed document_type column to VARCHAR(100) for longer custom types\n";
} catch (Exception $e) {
    echo "✗ document_type column modification: " . $e->getMessage() . "\n";
}

// Create custom intake options tables
echo "\nCreating custom intake options tables...\n";

// Create custom_document_sources table
try {
    $db->exec("CREATE TABLE IF NOT EXISTS custom_document_sources (
        id INT PRIMARY KEY AUTO_INCREMENT,
        company_id INT NOT NULL,
        source_name VARCHAR(100) NOT NULL,
        description TEXT NULL,
        usage_count INT DEFAULT 1,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
        UNIQUE KEY unique_company_source (company_id, source_name),
        INDEX idx_custom_sources_company_id (company_id),
        INDEX idx_custom_sources_active (is_active),
        INDEX idx_custom_sources_usage (usage_count)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created custom_document_sources table\n";
} catch (Exception $e) {
    echo "✗ custom_document_sources table: " . $e->getMessage() . "\n";
}

// Create custom_document_types table
try {
    $db->exec("CREATE TABLE IF NOT EXISTS custom_document_types (
        id INT PRIMARY KEY AUTO_INCREMENT,
        company_id INT NOT NULL,
        type_name VARCHAR(100) NOT NULL,
        description TEXT NULL,
        retention_years INT NULL COMMENT 'Default retention period for this type',
        usage_count INT DEFAULT 1,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
        UNIQUE KEY unique_company_type (company_id, type_name),
        INDEX idx_custom_types_company_id (company_id),
        INDEX idx_custom_types_active (is_active),
        INDEX idx_custom_types_usage (usage_count)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✓ Created custom_document_types table\n";
} catch (Exception $e) {
    echo "✗ custom_document_types table: " . $e->getMessage() . "\n";
}

// Insert sample custom sources and types
echo "\nInserting sample custom intake options...\n";
try {
    $db->exec("INSERT IGNORE INTO custom_document_sources (company_id, source_name, description, created_by) VALUES
        (1, 'Client Portal Upload', 'Documents uploaded through client portal', 1),
        (1, 'Mobile App Scan', 'Documents scanned using mobile application', 1),
        (1, 'Secure FTP', 'Documents received via secure FTP transfer', 1),
        (1, 'Third-party Integration', 'Documents from integrated third-party systems', 1)");
    echo "✓ Inserted sample custom document sources\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample custom sources: " . $e->getMessage() . "\n";
}

try {
    $db->exec("INSERT IGNORE INTO custom_document_types (company_id, type_name, description, retention_years, created_by) VALUES
        (1, 'Purchase Order', 'Purchase orders and procurement documents', 7, 1),
        (1, 'Employee Handbook', 'HR policy and handbook documents', 5, 1),
        (1, 'Insurance Policy', 'Insurance policies and related documents', 10, 1),
        (1, 'Marketing Material', 'Marketing and promotional materials', 3, 1),
        (1, 'Training Certificate', 'Employee training and certification documents', 7, 1)");
    echo "✓ Inserted sample custom document types\n";
} catch (Exception $e) {
    echo "✗ Failed to insert sample custom types: " . $e->getMessage() . "\n";
}

echo "\nTable fixes completed!\n";
?>
