<?php
$title = $viewUser['first_name'] . ' ' . $viewUser['last_name'];
ob_start();
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?= e($viewUser['first_name'] . ' ' . $viewUser['last_name']) ?></h1>
                <p class="text-gray-600 mt-1">User Details and Activity</p>
            </div>
            <div class="flex items-center space-x-3">
                <?php if (in_array($user['role'], ['super_admin', 'company_admin']) && $viewUser['id'] != $user['id']): ?>
                <a href="<?= url("/app/users/{$viewUser['id']}/edit") ?>"
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit User
                </a>
                <?php endif; ?>
                <a href="<?= url('/app/users') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Users
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <!-- User Information -->
        <div class="lg:col-span-2 space-y-8">
            
            <!-- Basic Information -->
            <div class="bg-white rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Full Name</label>
                        <p class="text-gray-900"><?= e($viewUser['first_name'] . ' ' . $viewUser['last_name']) ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Email Address</label>
                        <p class="text-gray-900"><?= e($viewUser['email']) ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Username</label>
                        <p class="text-gray-900"><?= e($viewUser['username']) ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Phone Number</label>
                        <p class="text-gray-900"><?= e($viewUser['phone'] ?? 'Not provided') ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Role</label>
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                            <?php
                            switch($viewUser['role']) {
                                case 'super_admin': echo 'bg-red-100 text-red-800'; break;
                                case 'company_admin': echo 'bg-purple-100 text-purple-800'; break;
                                case 'manager': echo 'bg-blue-100 text-blue-800'; break;
                                case 'editor': echo 'bg-green-100 text-green-800'; break;
                                case 'viewer': echo 'bg-gray-100 text-gray-800'; break;
                                case 'client': echo 'bg-orange-100 text-orange-800'; break;
                                default: echo 'bg-gray-100 text-gray-800';
                            }
                            ?>">
                            <?= ucwords(str_replace('_', ' ', $viewUser['role'])) ?>
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Status</label>
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                            <?php
                            switch($viewUser['status']) {
                                case 'active': echo 'bg-green-100 text-green-800'; break;
                                case 'inactive': echo 'bg-gray-100 text-gray-800'; break;
                                case 'suspended': echo 'bg-red-100 text-red-800'; break;
                                default: echo 'bg-gray-100 text-gray-800';
                            }
                            ?>">
                            <?= ucfirst($viewUser['status']) ?>
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Company</label>
                        <p class="text-gray-900"><?= e($viewUser['company_name'] ?? 'N/A') ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Member Since</label>
                        <p class="text-gray-900"><?= date('F j, Y', strtotime($viewUser['created_at'])) ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Last Login</label>
                        <p class="text-gray-900"><?= $viewUser['last_login'] ? date('F j, Y g:i A', strtotime($viewUser['last_login'])) : 'Never' ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Email Verified</label>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            <?= $viewUser['email_verified'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                            <?= $viewUser['email_verified'] ? 'Verified' : 'Not Verified' ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <?php if (!empty($activity)): ?>
            <div class="bg-white rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
                
                <div class="space-y-4">
                    <?php foreach ($activity as $activityItem): ?>
                    <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-900"><?= e($activityItem['description']) ?></p>
                            <p class="text-xs text-gray-500 mt-1">
                                <?= date('M j, Y g:i A', strtotime($activityItem['created_at'])) ?>
                            </p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            
            <!-- User Avatar -->
            <div class="bg-white rounded-xl shadow-sm p-6 text-center">
                <div class="w-24 h-24 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <span class="text-2xl font-bold text-blue-600">
                        <?= strtoupper(substr($viewUser['first_name'], 0, 1) . substr($viewUser['last_name'], 0, 1)) ?>
                    </span>
                </div>
                <h4 class="text-lg font-semibold text-gray-900"><?= e($viewUser['first_name'] . ' ' . $viewUser['last_name']) ?></h4>
                <p class="text-sm text-gray-500"><?= ucwords(str_replace('_', ' ', $viewUser['role'])) ?></p>
            </div>

            <!-- User Statistics -->
            <?php if (!empty($userStats)): ?>
            <div class="bg-white rounded-xl shadow-sm p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h4>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Documents Created</span>
                        <span class="text-sm font-semibold text-gray-900"><?= number_format($userStats['documents_created'] ?? 0) ?></span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Last Activity</span>
                        <span class="text-sm font-semibold text-gray-900">
                            <?= isset($userStats['last_activity']) ? date('M j', strtotime($userStats['last_activity'])) : 'N/A' ?>
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Login Count</span>
                        <span class="text-sm font-semibold text-gray-900"><?= number_format($userStats['login_count'] ?? 0) ?></span>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <?php if (in_array($user['role'], ['super_admin', 'company_admin']) && $viewUser['id'] != $user['id']): ?>
            <div class="bg-white rounded-xl shadow-sm p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    <a href="<?= url("/app/users/{$viewUser['id']}/edit") ?>"
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit User
                    </a>
                    
                    <?php if ($viewUser['status'] === 'active'): ?>
                    <button onclick="toggleUserStatus('<?= $viewUser['id'] ?>', 'suspended')"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-orange-300 text-sm font-medium rounded-lg text-orange-700 bg-white hover:bg-orange-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                        Suspend User
                    </button>
                    <?php elseif ($viewUser['status'] === 'suspended'): ?>
                    <button onclick="toggleUserStatus('<?= $viewUser['id'] ?>', 'active')"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-green-300 text-sm font-medium rounded-lg text-green-700 bg-white hover:bg-green-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Activate User
                    </button>
                    <?php endif; ?>
                    
                    <button onclick="deleteUser('<?= $viewUser['id'] ?>')"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-red-300 text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete User
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function toggleUserStatus(userId, newStatus) {
    const action = newStatus === 'active' ? 'activate' : 'suspend';
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        // Implementation would go here
        alert(`User ${action} functionality would be implemented here`);
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        fetch(`<?= url('/app/users') ?>/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '<?= url('/app/users') ?>';
            } else {
                alert('Error: ' + (data.error || 'Failed to delete user'));
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
