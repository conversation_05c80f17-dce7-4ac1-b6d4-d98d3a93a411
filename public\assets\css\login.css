/* Modern Login Page Specific Styles */

/* Ensure Tailwind utilities work properly */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

body {
    font-family: 'Inter', sans-serif;
}

/* Custom CSS Variables for consistent theming */
:root {
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-tertiary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Enhanced Blob Animation */
@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1) rotate(0deg);
    }
    25% {
        transform: translate(30px, -50px) scale(1.1) rotate(90deg);
    }
    50% {
        transform: translate(-20px, 20px) scale(0.9) rotate(180deg);
    }
    75% {
        transform: translate(-30px, -30px) scale(1.05) rotate(270deg);
    }
    100% {
        transform: translate(0px, 0px) scale(1) rotate(360deg);
    }
}

/* Smooth fade in animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Floating animation for decorative elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Pulse glow effect */
@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    }
    50% {
        box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
    }
}

/* Gradient animation */
@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Shimmer effect for loading states */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Apply animations */
.animate-blob {
    animation: blob 7s infinite;
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-scale {
    animation: fadeInScale 0.6s ease-out forwards;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
}

.animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

.animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Animation delays */
.animation-delay-300 {
    animation-delay: 300ms;
}

.animation-delay-600 {
    animation-delay: 600ms;
}

.animation-delay-900 {
    animation-delay: 900ms;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

/* Modern glassmorphism effects */
.glass-card {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-xl);
}

.glass-input {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-input:focus {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Enhanced button styles */
.modern-gradient-button {
    background: var(--gradient-primary);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.modern-gradient-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-gradient-button:hover::before {
    opacity: 1;
}

.modern-gradient-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

/* Ripple effect */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .animate-blob {
        display: none;
    }
    
    .glass-card {
        margin: 1rem;
        backdrop-filter: blur(15px);
    }
}

@media (max-width: 480px) {
    .glass-card {
        margin: 0.5rem;
        padding: 1.5rem;
    }
    
    .modern-gradient-button {
        padding: 0.875rem 1.5rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .glass-card {
        background: rgba(17, 24, 39, 0.8);
        border-color: rgba(75, 85, 99, 0.3);
    }
    
    .glass-input {
        background: rgba(31, 41, 55, 0.7);
        border-color: rgba(75, 85, 99, 0.3);
        color: white;
    }
    
    .glass-input::placeholder {
        color: rgba(156, 163, 175, 0.8);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .glass-card {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(0, 0, 0, 0.2);
    }
    
    .glass-input {
        border-width: 2px;
    }
    
    .modern-gradient-button {
        border: 2px solid rgba(0, 0, 0, 0.2);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .animate-blob,
    .animate-float,
    .animate-pulse-glow,
    .animate-gradient {
        animation: none;
    }
    
    .modern-gradient-button,
    .glass-input {
        transition: none;
    }
}
