<?php

namespace App\Controllers;

use App\Core\Database;

/**
 * Base Controller
 * 
 * Provides common functionality for all controllers
 */
class BaseController
{
    protected $db;
    protected $user;
    protected $company;
    
    public function __construct()
    {
        try {
            $this->db = Database::getInstance();
            $this->loadCurrentUser();
            $this->loadCurrentCompany();
        } catch (\Exception $e) {
            // Database not available - continue without DB for now
            logMessage("Database connection failed: " . $e->getMessage(), 'error');
            $this->db = null;
        }
    }
    
    /**
     * Load current authenticated user
     */
    protected function loadCurrentUser()
    {
        if (isset($_SESSION['user_id']) && $this->db) {
            try {
                $this->user = $this->db->fetch(
                    "SELECT u.*, c.name as company_name
                     FROM users u
                     LEFT JOIN companies c ON u.company_id = c.id
                     WHERE u.id = ? AND u.status = 'active'",
                    [$_SESSION['user_id']]
                );
            } catch (\Exception $e) {
                // Database query failed
                $this->user = null;
            }
        }
    }
    
    /**
     * Load current company
     */
    protected function loadCurrentCompany()
    {
        if ($this->user && $this->user['company_id'] && $this->db) {
            try {
                $this->company = $this->db->fetch(
                    "SELECT * FROM companies WHERE id = ? AND status = 'active'",
                    [$this->user['company_id']]
                );
            } catch (\Exception $e) {
                // Database query failed
                $this->company = null;
            }
        }
    }
    
    /**
     * Check if user is authenticated
     */
    protected function requireAuth()
    {
        if (!$this->user) {
            if (isAjax()) {
                $this->jsonResponse(['error' => 'Authentication required'], 401);
            } else {
                redirect('/login');
            }
            exit;
        }
    }

    /**
     * Check if user is authenticated (non-blocking)
     */
    protected function isAuthenticated()
    {
        return $this->user !== null;
    }

    /**
     * Check if user has specific role
     */
    protected function hasRole($role)
    {
        return $this->user && $this->user['role'] === $role;
    }
    
    /**
     * Check if user has required role
     */
    protected function requireRole($roles)
    {
        $this->requireAuth();
        
        if (!is_array($roles)) {
            $roles = [$roles];
        }
        
        if (!in_array($this->user['role'], $roles)) {
            if (isAjax()) {
                $this->jsonResponse(['error' => 'Insufficient permissions'], 403);
            } else {
                $this->view('errors/403');
            }
            exit;
        }
    }
    
    /**
     * Check if user can access company data
     */
    protected function requireCompanyAccess($companyId = null)
    {
        $this->requireAuth();
        
        // Super admin can access any company
        if ($this->user['role'] === 'super_admin') {
            return;
        }
        
        // Check if user belongs to the company
        $targetCompanyId = $companyId ?? $this->user['company_id'];
        
        if ($this->user['company_id'] != $targetCompanyId) {
            if (isAjax()) {
                $this->jsonResponse(['error' => 'Access denied'], 403);
            } else {
                $this->view('errors/403');
            }
            exit;
        }
    }
    
    /**
     * Render a view
     */
    protected function view($view, $data = [])
    {
        // Add common data
        $data['user'] = $this->user;
        $data['company'] = $this->company;
        $data['config'] = config('app');
        
        $viewFile = APP_ROOT . "/src/views/{$view}.php";
        
        if (file_exists($viewFile)) {
            extract($data);
            require $viewFile;
        } else {
            throw new \Exception("View not found: {$view}");
        }
    }
    
    /**
     * Return JSON response
     */
    protected function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    

    
    /**
     * Get paginated results
     */
    protected function paginate($sql, $params = [], $page = 1, $perPage = 20)
    {
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as count_query";
        $totalResult = $this->db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Calculate pagination
        $page = max(1, (int)$page);
        $perPage = min(100, max(1, (int)$perPage));
        $offset = ($page - 1) * $perPage;
        $totalPages = ceil($total / $perPage);
        
        // Get paginated results
        $paginatedSql = $sql . " LIMIT {$perPage} OFFSET {$offset}";
        $data = $this->db->fetchAll($paginatedSql, $params);
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null
            ]
        ];
    }
    

    
    /**
     * Get request input
     */
    protected function input($key = null, $default = null)
    {
        $input = array_merge($_GET, $_POST);
        
        if ($key === null) {
            return $input;
        }
        
        return $input[$key] ?? $default;
    }
    
    /**
     * Set flash message
     */
    protected function setFlashMessage($message, $type = 'success')
    {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }

    /**
     * Redirect to URL
     */
    protected function redirect($url)
    {
        redirect($url);
    }

    /**
     * Redirect with flash message
     */
    protected function redirectWithMessage($url, $message, $type = 'success')
    {
        $this->setFlashMessage($message, $type);
        $this->redirect($url);
    }

    /**
     * Log activity (simplified version)
     */
    protected function logActivity($action, $entityType, $entityId, $description = null)
    {
        if (!$this->user || !$this->db) {
            return;
        }

        try {
            // Check if activity_logs table exists
            $tableExists = $this->db->fetch("SHOW TABLES LIKE 'activity_logs'");
            if (!$tableExists) {
                return; // Silently skip if table doesn't exist
            }

            $this->db->execute(
                "INSERT INTO activity_logs (user_id, company_id, action, entity_type, entity_id, description, ip_address, created_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, NOW())",
                [
                    $this->user['id'],
                    $this->user['company_id'],
                    $action,
                    $entityType,
                    $entityId,
                    $description,
                    $_SERVER['REMOTE_ADDR'] ?? null
                ]
            );
        } catch (\Exception $e) {
            // Silently fail - don't log errors for activity logging to prevent output contamination
            // Only log to error log if we're not in an AJAX request
            if (empty($_SERVER['HTTP_X_REQUESTED_WITH'])) {
                error_log("Failed to log activity: " . $e->getMessage());
            }
        }
    }

    /**
     * Validate input with error handling
     */
    protected function validate($data, $rules)
    {
        $errors = [];
        $validated = [];

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = explode('|', $rule);

            // Add the field to validated data
            $validated[$field] = $value;

            foreach ($ruleList as $singleRule) {
                $ruleParts = explode(':', $singleRule);
                $ruleName = $ruleParts[0];
                $ruleValue = $ruleParts[1] ?? null;

                switch ($ruleName) {
                    case 'required':
                        if (empty($value) && $value !== '0') {
                            $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
                        }
                        break;

                    case 'email':
                        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . ' must be a valid email';
                        }
                        break;

                    case 'min':
                        if (!empty($value)) {
                            // Check if this is a numeric field (contains 'year', 'age', ends with 'count', etc. or has integer validation)
                            $isNumericField = (strpos($field, 'year') !== false ||
                                             strpos($field, 'age') !== false ||
                                             str_ends_with($field, 'count') ||
                                             in_array('integer', $ruleList));

                            if ($isNumericField) {
                                // Numeric minimum validation
                                if (!is_numeric($value) || (float)$value < (float)$ruleValue) {
                                    $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . " must be at least {$ruleValue}";
                                }
                            } else {
                                // String length minimum validation
                                if (strlen($value) < $ruleValue) {
                                    $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . " must be at least {$ruleValue} characters";
                                }
                            }
                        }
                        break;

                    case 'max':
                        if (!empty($value)) {
                            // Check if this is a numeric field
                            $isNumericField = (strpos($field, 'year') !== false ||
                                             strpos($field, 'age') !== false ||
                                             str_ends_with($field, 'count') ||
                                             in_array('integer', $ruleList));

                            if ($isNumericField) {
                                // Numeric maximum validation
                                if (!is_numeric($value) || (float)$value > (float)$ruleValue) {
                                    $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . " must not exceed {$ruleValue}";
                                }
                            } else {
                                // String length maximum validation
                                if (strlen($value) > $ruleValue) {
                                    $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . " must not exceed {$ruleValue} characters";
                                }
                            }
                        }
                        break;

                    case 'integer':
                        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                            $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . ' must be a valid integer';
                        }
                        break;

                    case 'in':
                        if (!empty($value)) {
                            $allowedValues = explode(',', $ruleValue);
                            if (!in_array($value, $allowedValues)) {
                                $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . ' must be one of: ' . implode(', ', $allowedValues);
                            }
                        }
                        break;

                    case 'unique':
                        if (!empty($value)) {
                            $tableParts = explode(',', $ruleValue);
                            $table = $tableParts[0];
                            $column = $tableParts[1] ?? $field;

                            $existing = $this->db->fetch(
                                "SELECT id FROM {$table} WHERE {$column} = ?",
                                [$value]
                            );

                            if ($existing) {
                                $errors[$field][] = ucfirst(str_replace('_', ' ', $field)) . ' already exists';
                            }
                        }
                        break;
                }
            }
        }

        // If there are validation errors, throw an exception
        if (!empty($errors)) {
            $errorMessages = [];
            foreach ($errors as $field => $fieldErrors) {
                $errorMessages = array_merge($errorMessages, $fieldErrors);
            }
            throw new \Exception(implode(', ', $errorMessages));
        }

        return $validated;
    }
}
