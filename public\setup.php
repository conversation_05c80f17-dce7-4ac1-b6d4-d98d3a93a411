<?php
/**
 * DMS Setup and Database Status Page
 */

// Define constants
define('APP_ROOT', dirname(__DIR__));
define('CONFIG_PATH', APP_ROOT . '/src/config');

require_once '../src/autoload.php';

$status = [
    'php_version' => PHP_VERSION,
    'php_ok' => version_compare(PHP_VERSION, '8.0.0', '>='),
    'extensions' => [],
    'database' => false,
    'database_error' => null,
    'tables' => [],
    'writable_dirs' => []
];

// Check PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl'];
foreach ($required_extensions as $ext) {
    $status['extensions'][$ext] = extension_loaded($ext);
}

// Check writable directories
$writable_dirs = [
    'storage/logs' => APP_ROOT . '/storage/logs',
    'storage/cache' => APP_ROOT . '/storage/cache',
    'public/uploads' => APP_ROOT . '/public/uploads'
];

foreach ($writable_dirs as $name => $path) {
    if (!is_dir($path)) {
        @mkdir($path, 0755, true);
    }
    $status['writable_dirs'][$name] = is_writable($path);
}

// Check database connection
try {
    $db = App\Core\Database::getInstance();
    $status['database'] = true;
    
    // Check if tables exist
    $tables = ['companies', 'users', 'documents', 'warehouses', 'storage_locations'];
    foreach ($tables as $table) {
        $status['tables'][$table] = $db->tableExists($table);
    }
    
} catch (Exception $e) {
    $status['database_error'] = $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS Setup - Document Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Document Management System</h1>
                <p class="mt-2 text-gray-600">Setup and System Status</p>
            </div>

            <!-- PHP Status -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">PHP Environment</h2>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">PHP Version</span>
                            <span class="text-sm <?= $status['php_ok'] ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $status['php_version'] ?> 
                                <?= $status['php_ok'] ? '✓' : '✗ (8.0+ required)' ?>
                            </span>
                        </div>
                        
                        <?php foreach ($status['extensions'] as $ext => $loaded): ?>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700"><?= ucfirst($ext) ?> Extension</span>
                                <span class="text-sm <?= $loaded ? 'text-green-600' : 'text-red-600' ?>">
                                    <?= $loaded ? '✓ Loaded' : '✗ Missing' ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Database Status -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Database Connection</h2>
                </div>
                <div class="px-6 py-4">
                    <?php if ($status['database']): ?>
                        <div class="flex items-center text-green-600 mb-4">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Database connection successful
                        </div>
                        
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Database Tables</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <?php foreach ($status['tables'] as $table => $exists): ?>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600"><?= $table ?></span>
                                    <span class="text-sm <?= $exists ? 'text-green-600' : 'text-yellow-600' ?>">
                                        <?= $exists ? '✓ Exists' : '⚠ Missing' ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (in_array(false, $status['tables'])): ?>
                            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                                <p class="text-sm text-yellow-800">
                                    Some database tables are missing. Run migrations to create them:
                                </p>
                                <code class="block mt-2 text-xs bg-gray-100 p-2 rounded">php scripts/migrate.php</code>
                            </div>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="flex items-center text-red-600 mb-4">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Database connection failed
                        </div>
                        
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <p class="text-sm text-red-800 mb-2">Error:</p>
                            <code class="text-xs text-red-700"><?= htmlspecialchars($status['database_error']) ?></code>
                            
                            <div class="mt-4">
                                <p class="text-sm text-red-800 font-medium">To fix this:</p>
                                <ol class="mt-2 text-sm text-red-700 list-decimal list-inside space-y-1">
                                    <li>Start XAMPP Control Panel</li>
                                    <li>Start the MySQL service</li>
                                    <li>Create the database: <code class="bg-red-100 px-1 rounded">php create_database.php</code></li>
                                    <li>Run migrations: <code class="bg-red-100 px-1 rounded">php scripts/migrate.php</code></li>
                                </ol>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- File Permissions -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">File Permissions</h2>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php foreach ($status['writable_dirs'] as $name => $writable): ?>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-700"><?= $name ?></span>
                                <span class="text-sm <?= $writable ? 'text-green-600' : 'text-red-600' ?>">
                                    <?= $writable ? '✓ Writable' : '✗ Not writable' ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <a href="<?= url('/') ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            View Application
                        </a>

                        <?php if ($status['database']): ?>
                            <a href="<?= url('/login') ?>" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Login
                            </a>
                            <a href="<?= url('/register') ?>" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Register
                            </a>
                        <?php else: ?>
                            <button disabled class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                Login (DB Required)
                            </button>
                            <button disabled class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                Register (DB Required)
                            </button>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mt-4 text-sm text-gray-600">
                        <p><strong>Next steps:</strong></p>
                        <ol class="mt-2 list-decimal list-inside space-y-1">
                            <li>Ensure all requirements are met (green checkmarks above)</li>
                            <li>Start MySQL service in XAMPP</li>
                            <li>Run database setup: <code class="bg-gray-100 px-1 rounded">php create_database.php</code></li>
                            <li>Run migrations: <code class="bg-gray-100 px-1 rounded">php scripts/migrate.php</code></li>
                            <li>Access the application at <a href="/" class="text-blue-600 hover:text-blue-500">http://localhost/dms/public</a></li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
