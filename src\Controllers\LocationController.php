<?php

namespace App\Controllers;

/**
 * Location Controller
 * 
 * Handles storage location management within warehouses
 */
class LocationController extends BaseController
{
    /**
     * List locations for a warehouse
     */
    public function index($warehouseId)
    {
        $this->requireAuth();
        
        try {
            // Get warehouse details
            $warehouse = $this->db->fetch(
                "SELECT * FROM warehouses WHERE id = ? AND company_id = ?",
                [$warehouseId, $this->user['company_id']]
            );
            
            if (!$warehouse) {
                $_SESSION['error'] = 'Warehouse not found.';
                redirect('/app/warehouses');
                return;
            }
            
            // Get locations for this warehouse
            $locations = $this->db->fetchAll(
                "SELECT l.*, COUNT(d.id) as document_count
                 FROM storage_locations l
                 LEFT JOIN documents d ON l.id = d.storage_location_id
                 WHERE l.warehouse_id = ?
                 GROUP BY l.id
                 ORDER BY l.name",
                [$warehouseId]
            );
            
            $this->view('locations/index', [
                'title' => 'Storage Locations - ' . $warehouse['name'],
                'warehouse' => $warehouse,
                'locations' => $locations
            ]);
            
        } catch (\Exception $e) {
            $_SESSION['error'] = 'Failed to load locations: ' . $e->getMessage();
            redirect('/app/warehouses');
        }
    }

    /**
     * Show create location form
     */
    public function create($warehouseId)
    {
        $this->requireAuth();

        try {
            // Get warehouse details
            $warehouse = $this->db->fetch(
                "SELECT * FROM warehouses WHERE id = ? AND company_id = ? AND status = 'active'",
                [$warehouseId, $this->user['company_id']]
            );

            if (!$warehouse) {
                $this->setFlashMessage('Warehouse not found.', 'error');
                $this->redirect('/app/warehouses');
                return;
            }

            $this->view('locations/create', [
                'title' => 'Create Storage Location - ' . $warehouse['name'],
                'warehouse' => $warehouse
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to load create form: ' . $e->getMessage(), 'error');
            $this->redirect('/app/warehouses');
        }
    }

    /**
     * Standalone create location form (with warehouse selection)
     */
    public function createStandalone()
    {
        $this->requireAuth();

        try {
            // Get warehouse from query parameter
            $warehouseId = $_GET['warehouse_id'] ?? null;

            if ($warehouseId) {
                // Redirect to warehouse-specific create form
                $this->redirect("/app/warehouses/{$warehouseId}/locations/create");
                return;
            }

            // Get all warehouses for selection
            $warehouses = $this->db->fetchAll(
                "SELECT id, name, city, state FROM warehouses
                 WHERE company_id = ? AND status = 'active'
                 ORDER BY name",
                [$this->user['company_id']]
            );

            if (empty($warehouses)) {
                $this->setFlashMessage('No warehouses found. Please create a warehouse first.', 'error');
                $this->redirect('/app/warehouses');
                return;
            }

            $this->view('locations/create-standalone', [
                'title' => 'Create Storage Location',
                'warehouses' => $warehouses
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to load create form: ' . $e->getMessage(), 'error');
            $this->redirect('/app/warehouses');
        }
    }

    /**
     * Store a new location
     */
    public function store($warehouseId)
    {
        $this->requireAuth();
        
        try {
            // Verify warehouse exists and belongs to company
            $warehouse = $this->db->fetch(
                "SELECT * FROM warehouses WHERE id = ? AND company_id = ?",
                [$warehouseId, $this->user['company_id']]
            );
            
            if (!$warehouse) {
                throw new \Exception('Warehouse not found.');
            }
            
            $data = [
                'warehouse_id' => $warehouseId,
                'type' => $_POST['location_type'] ?? 'shelf',
                'identifier' => $this->generateLocationIdentifier($_POST['name'] ?? '', $warehouseId),
                'name' => $_POST['name'] ?? '',
                'description' => $_POST['description'] ?? '',
                'capacity' => (float)($_POST['capacity'] ?? 0),
                'status' => $_POST['status'] ?? 'active',
                'created_by' => $this->user['id']
            ];
            
            // Validate required fields
            if (empty($data['name'])) {
                throw new \Exception('Location name is required.');
            }
            
            // Check for duplicate location name in warehouse
            $existing = $this->db->fetch(
                "SELECT id FROM storage_locations WHERE warehouse_id = ? AND name = ?",
                [$warehouseId, $data['name']]
            );

            if ($existing) {
                throw new \Exception('A location with this name already exists in this warehouse.');
            }

            // Insert location
            $locationId = $this->db->execute(
                "INSERT INTO storage_locations (warehouse_id, type, identifier, name, description, capacity, status, created_by, created_at, updated_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
                [
                    $data['warehouse_id'],
                    $data['type'],
                    $data['identifier'],
                    $data['name'],
                    $data['description'],
                    $data['capacity'],
                    $data['status'],
                    $data['created_by']
                ]
            );
            
            $_SESSION['success'] = 'Location created successfully.';
            
            if (isAjax()) {
                $this->jsonResponse(['success' => true, 'location_id' => $locationId]);
            } else {
                redirect("/app/warehouses/{$warehouseId}/locations");
            }
            
        } catch (\Exception $e) {
            if (isAjax()) {
                $this->jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                $_SESSION['error'] = $e->getMessage();
                redirect("/app/warehouses/{$warehouseId}/locations");
            }
        }
    }
    
    /**
     * Show location details
     */
    public function show($locationId)
    {
        $this->requireAuth();
        
        try {
            // Get location with warehouse info
            $location = $this->db->fetch(
                "SELECT l.*, w.name as warehouse_name, w.id as warehouse_id
                 FROM storage_locations l
                 JOIN warehouses w ON l.warehouse_id = w.id
                 WHERE l.id = ? AND w.company_id = ?",
                [$locationId, $this->user['company_id']]
            );

            if (!$location) {
                $this->setFlashMessage('Location not found.', 'error');
                $this->redirect('/app/warehouses');
                return;
            }

            // Get documents in this location
            $documents = $this->db->fetchAll(
                "SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.location_id = ?
                 ORDER BY d.created_at DESC",
                [$locationId]
            );

            $this->view('locations/show', [
                'title' => 'Location: ' . $location['name'],
                'location' => $location,
                'documents' => $documents
            ]);
            
        } catch (\Exception $e) {
            $_SESSION['error'] = 'Failed to load location: ' . $e->getMessage();
            redirect('/app/warehouses');
        }
    }

    /**
     * Show edit location form
     */
    public function edit($locationId)
    {
        $this->requireAuth();

        try {
            // Get location with warehouse info
            $location = $this->db->fetch(
                "SELECT l.*, w.name as warehouse_name, w.id as warehouse_id
                 FROM storage_locations l
                 JOIN warehouses w ON l.warehouse_id = w.id
                 WHERE l.id = ? AND w.company_id = ? AND l.status != 'inactive'",
                [$locationId, $this->user['company_id']]
            );

            if (!$location) {
                $this->setFlashMessage('Location not found.', 'error');
                $this->redirect('/app/warehouses');
                return;
            }

            $this->view('locations/edit', [
                'title' => 'Edit Location: ' . $location['name'],
                'location' => $location
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to load edit form: ' . $e->getMessage(), 'error');
            $this->redirect('/app/warehouses');
        }
    }

    /**
     * Update location
     */
    public function update($locationId)
    {
        $this->requireAuth();
        
        try {
            // Get location with warehouse info
            $location = $this->db->fetch(
                "SELECT l.*, w.company_id
                 FROM locations l
                 JOIN warehouses w ON l.warehouse_id = w.id
                 WHERE l.id = ? AND w.company_id = ?",
                [$locationId, $this->user['company_id']]
            );
            
            if (!$location) {
                throw new \Exception('Location not found.');
            }
            
            $data = [
                'name' => $_POST['name'] ?? '',
                'description' => $_POST['description'] ?? '',
                'location_type' => $_POST['location_type'] ?? 'shelf',
                'capacity' => (int)($_POST['capacity'] ?? 0),
                'status' => $_POST['status'] ?? 'active',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Validate required fields
            if (empty($data['name'])) {
                throw new \Exception('Location name is required.');
            }
            
            // Check for duplicate location name in warehouse (excluding current)
            $existing = $this->db->fetch(
                "SELECT id FROM locations WHERE warehouse_id = ? AND name = ? AND id != ?",
                [$location['warehouse_id'], $data['name'], $locationId]
            );
            
            if ($existing) {
                throw new \Exception('A location with this name already exists in this warehouse.');
            }
            
            // Update location
            $this->db->update('locations', $data, ['id' => $locationId]);
            
            $_SESSION['success'] = 'Location updated successfully.';
            
            if (isAjax()) {
                $this->jsonResponse(['success' => true]);
            } else {
                redirect("/app/locations/{$locationId}");
            }
            
        } catch (\Exception $e) {
            if (isAjax()) {
                $this->jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                $_SESSION['error'] = $e->getMessage();
                redirect("/app/locations/{$locationId}");
            }
        }
    }
    
    /**
     * Delete location
     */
    public function delete($locationId)
    {
        $this->requireAuth();
        
        try {
            // Get location with warehouse info
            $location = $this->db->fetch(
                "SELECT l.*, w.company_id
                 FROM storage_locations l
                 JOIN warehouses w ON l.warehouse_id = w.id
                 WHERE l.id = ? AND w.company_id = ?",
                [$locationId, $this->user['company_id']]
            );

            if (!$location) {
                throw new \Exception('Location not found.');
            }

            // Check if location has documents
            $documentCount = $this->db->fetch(
                "SELECT COUNT(*) as count FROM documents WHERE storage_location_id = ?",
                [$locationId]
            )['count'] ?? 0;
            
            if ($documentCount > 0) {
                throw new \Exception('Cannot delete location that contains documents. Please move or remove all documents first.');
            }
            
            // Delete location (soft delete by setting status to inactive)
            $this->db->execute(
                "UPDATE storage_locations SET status = 'inactive', updated_at = NOW() WHERE id = ?",
                [$locationId]
            );
            
            $_SESSION['success'] = 'Location deleted successfully.';
            
            if (isAjax()) {
                $this->jsonResponse(['success' => true]);
            } else {
                redirect("/app/warehouses/{$location['warehouse_id']}/locations");
            }
            
        } catch (\Exception $e) {
            if (isAjax()) {
                $this->jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                $_SESSION['error'] = $e->getMessage();
                redirect('/app/warehouses');
            }
        }
    }
    
    /**
     * Get documents in location
     */
    public function documents($locationId)
    {
        $this->requireAuth();
        
        try {
            // Verify location access
            $location = $this->db->fetch(
                "SELECT l.*, w.company_id
                 FROM storage_locations l
                 JOIN warehouses w ON l.warehouse_id = w.id
                 WHERE l.id = ? AND w.company_id = ?",
                [$locationId, $this->user['company_id']]
            );

            if (!$location) {
                throw new \Exception('Location not found.');
            }

            // Get documents with pagination
            $page = (int)($_GET['page'] ?? 1);
            $limit = 20;
            $offset = ($page - 1) * $limit;

            $documents = $this->db->fetchAll(
                "SELECT d.*, u.first_name, u.last_name, c.name as category_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 LEFT JOIN categories c ON d.category_id = c.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
                 LIMIT ? OFFSET ?",
                [$locationId, $limit, $offset]
            );

            $totalDocuments = $this->db->fetch(
                "SELECT COUNT(*) as count FROM documents WHERE storage_location_id = ?",
                [$locationId]
            )['count'] ?? 0;
            
            if (isAjax()) {
                $this->jsonResponse([
                    'documents' => $documents,
                    'total' => $totalDocuments,
                    'page' => $page,
                    'pages' => ceil($totalDocuments / $limit)
                ]);
            } else {
                $this->view('locations/documents', [
                    'title' => 'Documents in ' . $location['name'],
                    'location' => $location,
                    'documents' => $documents,
                    'total' => $totalDocuments,
                    'page' => $page,
                    'pages' => ceil($totalDocuments / $limit)
                ]);
            }
            
        } catch (\Exception $e) {
            if (isAjax()) {
                $this->jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                $_SESSION['error'] = $e->getMessage();
                redirect('/app/warehouses');
            }
        }
    }

    /**
     * Generate unique location identifier
     */
    private function generateLocationIdentifier($name, $warehouseId)
    {
        // Create identifier from name (remove spaces, special chars, make uppercase)
        $identifier = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $name));

        // Limit to 20 characters
        $identifier = substr($identifier, 0, 20);

        // Add warehouse prefix
        $identifier = "WH{$warehouseId}-{$identifier}";

        // Check if identifier exists and add suffix if needed
        $counter = 1;
        $originalIdentifier = $identifier;

        while ($this->db->fetch("SELECT id FROM storage_locations WHERE identifier = ?", [$identifier])) {
            $identifier = $originalIdentifier . "-" . $counter;
            $counter++;
        }

        return $identifier;
    }

    /**
     * Show storage location details (standalone route)
     */
    public function showStandalone($locationId)
    {
        return $this->show($locationId);
    }

    /**
     * Show edit storage location form (standalone route)
     */
    public function editStandalone($locationId)
    {
        return $this->edit($locationId);
    }

    /**
     * Update storage location (standalone route)
     */
    public function updateStandalone($locationId)
    {
        return $this->update($locationId);
    }

    /**
     * Delete storage location (standalone route)
     */
    public function deleteStandalone($locationId)
    {
        return $this->delete($locationId);
    }

    /**
     * Get documents in storage location (standalone route)
     */
    public function documentsStandalone($locationId)
    {
        return $this->documents($locationId);
    }
}
