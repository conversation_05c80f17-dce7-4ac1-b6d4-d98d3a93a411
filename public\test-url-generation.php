<?php
/**
 * Test URL Generation
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader (this loads helpers.php)
require_once APP_ROOT . '/src/autoload.php';

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

// Define BASE_PATH constant (this is what the url() function uses)
define('BASE_PATH', $basePath === '/' ? '' : $basePath);

echo "<h1>URL Generation Test</h1>";

echo "<h3>Base Path Detection:</h3>";
echo "<p><strong>Script Name:</strong> " . $scriptName . "</p>";
echo "<p><strong>Detected Base Path:</strong> " . ($basePath ?: '(empty)') . "</p>";
echo "<p><strong>BASE_PATH Constant:</strong> " . (defined('BASE_PATH') ? BASE_PATH : 'Not defined') . "</p>";

echo "<h3>URL Generation Tests:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Input</th><th>Generated URL</th><th>Test Link</th></tr>";

$testUrls = [
    '/app/billing',
    '/app/billing/rates',
    '/app/billing/client/1',
    '/app/billing/invoice/1',
    '/app/billing/generate-invoice'
];

foreach ($testUrls as $testUrl) {
    $generatedUrl = url($testUrl);
    echo "<tr>";
    echo "<td><code>" . htmlspecialchars($testUrl) . "</code></td>";
    echo "<td><code>" . htmlspecialchars($generatedUrl) . "</code></td>";
    echo "<td><a href='" . htmlspecialchars($generatedUrl) . "' target='_blank'>Test</a></td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Current Request Info:</h3>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>HTTP_HOST:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";

echo "<h3>Quick Test:</h3>";
echo "<p>Click these links to test if the URL generation is working:</p>";
echo "<ul>";
echo "<li><a href='" . url('/app/billing') . "' target='_blank'>Billing Dashboard</a></li>";
echo "<li><a href='" . url('/app/billing/rates') . "' target='_blank'>Service Rates</a></li>";
echo "<li><a href='" . url('/app/billing/client/1') . "' target='_blank'>Client Billing (ID: 1)</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>If the links above work, then the URL generation fix is successful!</em></p>";
?>
