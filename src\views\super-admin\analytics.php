<?php
$title = 'System Analytics';
ob_start();
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">System Analytics</h1>
                <p class="text-gray-600 mt-1">Comprehensive system performance and growth metrics</p>
            </div>
            <a href="<?= url('/super-admin/dashboard') ?>" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Growth Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- User Growth -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">User Growth (Last 12 Months)</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <?php if (!empty($userGrowth)): ?>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2"><?= count($userGrowth) ?></div>
                        <p class="text-gray-600">Data Points Available</p>
                        <p class="text-sm text-gray-500 mt-2">Chart visualization would be implemented here</p>
                    </div>
                <?php else: ?>
                    <div class="text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <p>No user growth data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Document Growth -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Document Growth (Last 12 Months)</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <?php if (!empty($documentGrowth)): ?>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2"><?= count($documentGrowth) ?></div>
                        <p class="text-gray-600">Data Points Available</p>
                        <p class="text-sm text-gray-500 mt-2">Chart visualization would be implemented here</p>
                    </div>
                <?php else: ?>
                    <div class="text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p>No document growth data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Storage Growth -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Storage Growth (Last 12 Months)</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <?php if (!empty($storageGrowth)): ?>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600 mb-2"><?= count($storageGrowth) ?></div>
                        <p class="text-gray-600">Data Points Available</p>
                        <p class="text-sm text-gray-500 mt-2">Chart visualization would be implemented here</p>
                    </div>
                <?php else: ?>
                    <div class="text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                        </svg>
                        <p>No storage growth data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Company Growth -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Growth (Last 12 Months)</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <?php if (!empty($companyGrowth)): ?>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-orange-600 mb-2"><?= count($companyGrowth) ?></div>
                        <p class="text-gray-600">Data Points Available</p>
                        <p class="text-sm text-gray-500 mt-2">Chart visualization would be implemented here</p>
                    </div>
                <?php else: ?>
                    <div class="text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <p>No company growth data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Subscription Analytics -->
    <?php if (!empty($subscriptionAnalytics)): ?>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- Plan Distribution -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription Plan Distribution</h3>
            <?php if (!empty($subscriptionAnalytics['plan_distribution'])): ?>
                <div class="space-y-4">
                    <?php foreach ($subscriptionAnalytics['plan_distribution'] as $plan => $count): ?>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700 capitalize"><?= e($plan) ?></span>
                            <span class="text-sm font-bold text-gray-900"><?= number_format($count) ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center">No subscription data available</p>
            <?php endif; ?>
        </div>

        <!-- Storage by Plan -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Storage Usage by Plan</h3>
            <?php if (!empty($subscriptionAnalytics['storage_by_plan'])): ?>
                <div class="space-y-4">
                    <?php foreach ($subscriptionAnalytics['storage_by_plan'] as $planData): ?>
                        <div class="border-b border-gray-200 pb-3">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-gray-700 capitalize"><?= e($planData['subscription_plan']) ?></span>
                                <span class="text-sm text-gray-500"><?= number_format($planData['avg_usage_percentage'] ?? 0, 1) ?>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: <?= min(100, $planData['avg_usage_percentage'] ?? 0) ?>%"></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center">No storage data available</p>
            <?php endif; ?>
        </div>

        <!-- Monthly Trends -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Monthly Subscription Trends</h3>
            <?php if (!empty($subscriptionAnalytics['monthly_trends'])): ?>
                <div class="space-y-2">
                    <?php 
                    $monthlyData = [];
                    foreach ($subscriptionAnalytics['monthly_trends'] as $trend) {
                        $monthlyData[$trend['month']] = ($monthlyData[$trend['month']] ?? 0) + $trend['count'];
                    }
                    $recentMonths = array_slice($monthlyData, -6, 6, true);
                    ?>
                    <?php foreach ($recentMonths as $month => $count): ?>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600"><?= date('M Y', strtotime($month . '-01')) ?></span>
                            <span class="text-sm font-semibold text-gray-900"><?= number_format($count) ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center">No trend data available</p>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Data Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Recent User Growth Data -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent User Growth Data</h3>
            <?php if (!empty($userGrowth)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">New Users</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach (array_slice($userGrowth, -10) as $data): ?>
                            <tr>
                                <td class="px-4 py-3 text-sm text-gray-900"><?= date('M j, Y', strtotime($data['date'])) ?></td>
                                <td class="px-4 py-3 text-sm font-semibold text-gray-900"><?= number_format($data['count']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center py-8">No user growth data available</p>
            <?php endif; ?>
        </div>

        <!-- Recent Document Growth Data -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Document Growth Data</h3>
            <?php if (!empty($documentGrowth)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">New Documents</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach (array_slice($documentGrowth, -10) as $data): ?>
                            <tr>
                                <td class="px-4 py-3 text-sm text-gray-900"><?= date('M j, Y', strtotime($data['date'])) ?></td>
                                <td class="px-4 py-3 text-sm font-semibold text-gray-900"><?= number_format($data['count']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center py-8">No document growth data available</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Note about Chart Implementation -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start">
            <svg class="w-6 h-6 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <h4 class="text-lg font-semibold text-blue-900 mb-2">Chart Implementation Note</h4>
                <p class="text-blue-800">
                    The analytics data is being collected and processed correctly. To display interactive charts, 
                    you can integrate a charting library like Chart.js, D3.js, or ApexCharts. The data structure 
                    is ready for visualization.
                </p>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
