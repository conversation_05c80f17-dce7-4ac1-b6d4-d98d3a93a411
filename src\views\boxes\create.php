<?php
$title = 'Create New Box';
ob_start();
?>

<!-- Create Box Form -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- <PERSON> Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        Create New Box
                    </h1>
                    <p class="text-gray-600 mt-2">Add a new storage container to organize your documents</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/boxes') ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Boxes
                    </a>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
            <form action="<?= url('/app/boxes') ?>" method="POST" class="space-y-6">
                
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Box Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Box Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="e.g., Box A-001">
                    </div>

                    <!-- Box Identifier -->
                    <div>
                        <label for="identifier" class="block text-sm font-medium text-gray-700 mb-2">
                            Box Identifier <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="identifier" 
                               name="identifier" 
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="e.g., BOX-A001">
                    </div>

                </div>

                <!-- Warehouse and Storage Type -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Warehouse -->
                    <div>
                        <label for="warehouse_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Warehouse <span class="text-red-500">*</span>
                        </label>
                        <select id="warehouse_id" 
                                name="warehouse_id" 
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="">Select a warehouse</option>
                            <?php foreach ($warehouses as $warehouse): ?>
                                <option value="<?= $warehouse['id'] ?>">
                                    <?= e($warehouse['name']) ?> - <?= e($warehouse['city']) ?>, <?= e($warehouse['state']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Storage Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-4">
                            Storage Type <span class="text-red-500">*</span>
                        </label>
                        <div class="grid grid-cols-1 gap-3">
                            <!-- Physical Storage Option -->
                            <div class="relative">
                                <input type="radio" name="storage_type" id="storage_physical" value="physical" required
                                       class="peer sr-only">
                                <label for="storage_physical"
                                       class="flex items-center p-3 bg-gradient-to-br from-amber-50 to-orange-100 border-2 border-amber-200 rounded-xl cursor-pointer hover:border-amber-300 peer-checked:border-amber-500 peer-checked:bg-gradient-to-br peer-checked:from-amber-100 peer-checked:to-orange-200 transition-all duration-200">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-900">📦 Physical Storage</h4>
                                            <p class="text-sm text-gray-600">For warehouse storage boxes</p>
                                        </div>
                                    </div>
                                    <div class="ml-auto">
                                        <div class="w-4 h-4 border-2 border-amber-400 rounded-full peer-checked:bg-amber-500 peer-checked:border-amber-500 transition-all duration-200"></div>
                                    </div>
                                </label>
                            </div>

                            <!-- Online Storage Option -->
                            <div class="relative">
                                <input type="radio" name="storage_type" id="storage_online" value="online"
                                       class="peer sr-only">
                                <label for="storage_online"
                                       class="flex items-center p-3 bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-xl cursor-pointer hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-gradient-to-br peer-checked:from-blue-100 peer-checked:to-indigo-200 transition-all duration-200">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-900">☁️ Online Storage</h4>
                                            <p class="text-sm text-gray-600">For digital file organization</p>
                                        </div>
                                    </div>
                                    <div class="ml-auto">
                                        <div class="w-4 h-4 border-2 border-blue-400 rounded-full peer-checked:bg-blue-500 peer-checked:border-blue-500 transition-all duration-200"></div>
                                    </div>
                                </label>
                            </div>

                            <!-- Mixed Storage Option -->
                            <div class="relative">
                                <input type="radio" name="storage_type" id="storage_mixed" value="mixed"
                                       class="peer sr-only">
                                <label for="storage_mixed"
                                       class="flex items-center p-3 bg-gradient-to-br from-purple-50 to-pink-100 border-2 border-purple-200 rounded-xl cursor-pointer hover:border-purple-300 peer-checked:border-purple-500 peer-checked:bg-gradient-to-br peer-checked:from-purple-100 peer-checked:to-pink-200 transition-all duration-200">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-900">🔄 Mixed Storage</h4>
                                            <p class="text-sm text-gray-600">Physical docs + digital references</p>
                                        </div>
                                    </div>
                                    <div class="ml-auto">
                                        <div class="w-4 h-4 border-2 border-purple-400 rounded-full peer-checked:bg-purple-500 peer-checked:border-purple-500 transition-all duration-200"></div>
                                    </div>
                                </label>
                            </div>
                        </div>
                        <p class="text-sm text-gray-500 mt-3">
                            <strong>Mixed storage</strong> allows a box to contain both physical documents and digital references/QR codes linking to online files.
                        </p>
                    </div>

                </div>

                <!-- Capacity and Barcode -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Capacity -->
                    <div>
                        <label for="capacity" class="block text-sm font-medium text-gray-700 mb-2">
                            Capacity (number of documents)
                        </label>
                        <input type="number" 
                               id="capacity" 
                               name="capacity" 
                               min="1"
                               value="100"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="100">
                    </div>

                    <!-- Barcode -->
                    <div>
                        <label for="barcode" class="block text-sm font-medium text-gray-700 mb-2">
                            Barcode (optional)
                        </label>
                        <input type="text" 
                               id="barcode" 
                               name="barcode" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="Leave empty to auto-generate">
                    </div>

                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              placeholder="Optional description for this storage box..."></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="<?= url('/app/boxes') ?>" 
                       class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Box
                    </button>
                </div>

            </form>
        </div>

        <!-- Help Information -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">Box Creation Tips</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                <div>
                    <h4 class="font-medium mb-2">Naming Convention:</h4>
                    <ul class="space-y-1">
                        <li>• Use clear, descriptive names</li>
                        <li>• Include location or purpose</li>
                        <li>• Example: "Box A-001", "Legal Docs Box"</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Storage Types:</h4>
                    <ul class="space-y-1">
                        <li>• <strong>Physical:</strong> For physical document storage</li>
                        <li>• <strong>Online:</strong> For digital file organization</li>
                        <li>• Choose based on your storage needs</li>
                    </ul>
                </div>
            </div>
        </div>

    </div>
</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
