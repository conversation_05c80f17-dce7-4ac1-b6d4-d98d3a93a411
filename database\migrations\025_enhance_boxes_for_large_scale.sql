-- Enhance boxes table for large-scale management
-- Add archive status, improve indexing, and optimize for performance

-- Add archive status column
ALTER TABLE boxes 
ADD COLUMN archive_status ENUM('active', 'archived', 'auto_archived') DEFAULT 'active' AFTER status;

-- Add last_activity_at column for smart archiving
ALTER TABLE boxes 
ADD COLUMN last_activity_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP AFTER updated_at;

-- Add indexes for performance optimization
ALTER TABLE boxes 
ADD INDEX idx_boxes_archive_status (archive_status);

ALTER TABLE boxes 
ADD INDEX idx_boxes_created_at (created_at);

ALTER TABLE boxes 
ADD INDEX idx_boxes_last_activity (last_activity_at);

ALTER TABLE boxes 
ADD INDEX idx_boxes_warehouse_archive_created (warehouse_id, archive_status, created_at);

ALTER TABLE boxes 
ADD INDEX idx_boxes_storage_type_archive (storage_type, archive_status);

-- Update existing boxes with last_activity_at
UPDATE boxes 
SET last_activity_at = updated_at 
WHERE last_activity_at IS NULL;

-- Auto-archive boxes older than 1 year with no recent activity
UPDATE boxes 
SET archive_status = 'auto_archived' 
WHERE archive_status = 'active' 
  AND last_activity_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)
  AND created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- Add comments for documentation
ALTER TABLE boxes 
MODIFY COLUMN archive_status ENUM('active', 'archived', 'auto_archived') DEFAULT 'active' 
COMMENT 'Archive status: active for current boxes, archived for manually archived, auto_archived for system archived';

ALTER TABLE boxes 
MODIFY COLUMN last_activity_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP 
COMMENT 'Last activity timestamp for smart archiving and filtering';
