<?php
/**
 * Database Migration Script
 *
 * Run this script to create/update the database schema
 * Usage: php scripts/migrate.php
 */

// Define constants
define('APP_ROOT', dirname(__DIR__));
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the application bootstrap
require_once dirname(__DIR__) . '/src/autoload.php';

use App\Core\Database;

class MigrationRunner
{
    private $db;
    private $migrationsPath;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->migrationsPath = dirname(__DIR__) . '/database/migrations';
        
        // Create migrations table if it doesn't exist
        $this->createMigrationsTable();
    }
    
    /**
     * Create migrations tracking table
     */
    private function createMigrationsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS migrations (
            id INT PRIMARY KEY AUTO_INCREMENT,
            migration VARCHAR(255) NOT NULL,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_migration (migration)
        )";
        
        $this->db->exec($sql);
        echo "✓ Migrations table ready\n";
    }
    
    /**
     * Run all pending migrations
     */
    public function run()
    {
        echo "Starting database migrations...\n\n";
        
        $migrationFiles = $this->getMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        
        $pendingMigrations = array_diff($migrationFiles, $executedMigrations);
        
        if (empty($pendingMigrations)) {
            echo "✓ No pending migrations found. Database is up to date.\n";
            return;
        }
        
        echo "Found " . count($pendingMigrations) . " pending migration(s):\n";
        
        foreach ($pendingMigrations as $migration) {
            $this->executeMigration($migration);
        }
        
        echo "\n✓ All migrations completed successfully!\n";
    }
    
    /**
     * Get all migration files
     */
    private function getMigrationFiles()
    {
        $files = glob($this->migrationsPath . '/*.sql');
        $migrations = [];
        
        foreach ($files as $file) {
            $migrations[] = basename($file, '.sql');
        }
        
        sort($migrations);
        return $migrations;
    }
    
    /**
     * Get executed migrations from database
     */
    private function getExecutedMigrations()
    {
        try {
            $result = $this->db->fetchAll("SELECT migration FROM migrations ORDER BY migration");
            return array_column($result, 'migration');
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Execute a single migration
     */
    private function executeMigration($migration)
    {
        $filePath = $this->migrationsPath . '/' . $migration . '.sql';
        
        if (!file_exists($filePath)) {
            echo "✗ Migration file not found: {$migration}\n";
            return false;
        }
        
        echo "Running migration: {$migration}... ";
        
        try {
            // Read and execute SQL file
            $sql = file_get_contents($filePath);
            
            // Split by semicolon and execute each statement
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            $this->db->beginTransaction();
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    $this->db->exec($statement);
                }
            }
            
            // Record migration as executed
            $this->db->insert('migrations', ['migration' => $migration]);
            
            $this->db->commit();
            
            echo "✓ Success\n";
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            echo "✗ Failed: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Show migration status
     */
    public function status()
    {
        echo "Migration Status:\n";
        echo "================\n\n";
        
        $migrationFiles = $this->getMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        
        foreach ($migrationFiles as $migration) {
            $status = in_array($migration, $executedMigrations) ? '✓ Executed' : '✗ Pending';
            echo sprintf("%-50s %s\n", $migration, $status);
        }
        
        echo "\nTotal migrations: " . count($migrationFiles) . "\n";
        echo "Executed: " . count($executedMigrations) . "\n";
        echo "Pending: " . (count($migrationFiles) - count($executedMigrations)) . "\n";
    }
    
    /**
     * Reset database (drop all tables)
     */
    public function reset()
    {
        echo "⚠️  WARNING: This will drop all tables and data!\n";
        echo "Are you sure you want to continue? (yes/no): ";
        
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim($line) !== 'yes') {
            echo "Operation cancelled.\n";
            return;
        }
        
        echo "Dropping all tables...\n";
        
        try {
            // Get all tables
            $tables = $this->db->fetchAll("SHOW TABLES");
            $tableColumn = 'Tables_in_' . $this->getDatabaseName();
            
            // Disable foreign key checks
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // Drop all tables
            foreach ($tables as $table) {
                $tableName = $table[$tableColumn];
                $this->db->exec("DROP TABLE IF EXISTS `{$tableName}`");
                echo "✓ Dropped table: {$tableName}\n";
            }
            
            // Re-enable foreign key checks
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            echo "✓ Database reset completed.\n";
            
        } catch (Exception $e) {
            echo "✗ Error resetting database: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Get current database name
     */
    private function getDatabaseName()
    {
        $result = $this->db->fetch("SELECT DATABASE() as db_name");
        return $result['db_name'];
    }
}

// Handle command line arguments
$command = $argv[1] ?? 'run';

try {
    $runner = new MigrationRunner();
    
    switch ($command) {
        case 'run':
            $runner->run();
            break;
            
        case 'status':
            $runner->status();
            break;
            
        case 'reset':
            $runner->reset();
            break;
            
        default:
            echo "Usage: php migrate.php [command]\n";
            echo "Commands:\n";
            echo "  run    - Run pending migrations (default)\n";
            echo "  status - Show migration status\n";
            echo "  reset  - Reset database (drop all tables)\n";
            break;
    }
    
} catch (Exception $e) {
    echo "✗ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
