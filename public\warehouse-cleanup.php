<?php
/**
 * Warehouse Cleanup Tool
 */

// Start session
session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');
define('BASE_PATH', '/dms/public');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

use App\Core\Database;

try {
    echo "<h1>Warehouse Cleanup Tool</h1>";
    
    $db = Database::getInstance();
    
    // Handle deletion request
    if (isset($_POST['delete_warehouse']) && isset($_POST['warehouse_id'])) {
        $warehouseId = (int)$_POST['warehouse_id'];

        // Get warehouse details
        $warehouse = $db->fetch("SELECT name FROM warehouses WHERE id = ?", [$warehouseId]);

        // Check if warehouse has any content
        $hasContent = $db->fetch(
            "SELECT
                COUNT(DISTINCT CASE WHEN b.status != 'archived' AND b.name NOT LIKE '[DELETED]%' THEN b.id END) as box_count,
                COUNT(DISTINCT d.id) as document_count
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             LEFT JOIN box_bundles bb ON b.id = bb.box_id
             LEFT JOIN bundles bun ON bb.bundle_id = bun.id AND bun.name NOT LIKE '[DELETED]%'
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.id = ?",
            [$warehouseId]
        );

        if ($hasContent['box_count'] > 0 || $hasContent['document_count'] > 0) {
            echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>❌ Cannot delete warehouse:</strong> It contains {$hasContent['box_count']} boxes and {$hasContent['document_count']} documents.";
            echo "</div>";
        } else {
            // Safe to delete
            $db->execute(
                "UPDATE warehouses SET
                 name = CONCAT('[DELETED] ', name),
                 status = 'inactive',
                 updated_at = NOW()
                 WHERE id = ?",
                [$warehouseId]
            );

            echo "<div style='background: #e8f5e8; border: 1px solid #4caf50; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>✅ Warehouse '{$warehouse['name']}' deleted successfully!</strong>";
            echo "</div>";
        }
    }

    // Handle force delete for "Main Storage Facility"
    if (isset($_POST['force_delete_main'])) {
        echo "<h3>🔧 Force Deleting 'Main Storage Facility'...</h3>";

        // First, let's see what's in it
        $mainWarehouse = $db->fetch("SELECT id, name FROM warehouses WHERE name LIKE '%Main Storage Facility%' AND status = 'active'");

        if ($mainWarehouse) {
            echo "<p>Found warehouse: " . htmlspecialchars($mainWarehouse['name']) . " (ID: {$mainWarehouse['id']})</p>";

            // Check content
            $content = $db->fetch(
                "SELECT
                    COUNT(DISTINCT b.id) as all_boxes,
                    COUNT(DISTINCT CASE WHEN b.name NOT LIKE '[DELETED]%' THEN b.id END) as active_boxes,
                    COUNT(DISTINCT d.id) as documents
                 FROM warehouses w
                 LEFT JOIN boxes b ON w.id = b.warehouse_id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 WHERE w.id = ?",
                [$mainWarehouse['id']]
            );

            echo "<p>Content: {$content['all_boxes']} total boxes, {$content['active_boxes']} active boxes, {$content['documents']} documents</p>";

            // Force delete regardless of content
            $db->execute(
                "UPDATE warehouses SET
                 name = CONCAT('[DELETED] ', name),
                 status = 'inactive',
                 updated_at = NOW()
                 WHERE id = ?",
                [$mainWarehouse['id']]
            );

            echo "<div style='background: #e8f5e8; border: 1px solid #4caf50; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>✅ 'Main Storage Facility' has been force deleted!</strong>";
            echo "</div>";
        } else {
            echo "<p style='color: orange;'>No 'Main Storage Facility' warehouse found.</p>";
        }
    }
    
    // Get all active warehouses
    echo "<h2>Current Warehouses</h2>";
    $warehouses = $db->fetchAll(
        "SELECT w.*,
                COUNT(DISTINCT CASE WHEN b.status != 'archived' AND b.name NOT LIKE '[DELETED]%' THEN b.id END) as total_boxes,
                COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') AND b.name NOT LIKE '[DELETED]%' THEN b.id END) as occupied_boxes,
                COUNT(DISTINCT d.id) as total_documents
         FROM warehouses w
         LEFT JOIN boxes b ON w.id = b.warehouse_id
         LEFT JOIN box_bundles bb ON b.id = bb.box_id
         LEFT JOIN bundles bun ON bb.bundle_id = bun.id AND bun.name NOT LIKE '[DELETED]%'
         LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
         WHERE w.status = 'active' AND w.name NOT LIKE '[DELETED]%'
         GROUP BY w.id
         ORDER BY w.name"
    );
    
    if (empty($warehouses)) {
        echo "<p>No warehouses found.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #f5f5f5;'>";
        echo "<th>ID</th><th>Name</th><th>Location</th><th>Boxes</th><th>Documents</th><th>Action</th>";
        echo "</tr>";
        
        foreach ($warehouses as $warehouse) {
            $canDelete = ($warehouse['total_boxes'] == 0 && $warehouse['total_documents'] == 0);
            $deleteButtonStyle = $canDelete ? 
                'background: #f44336; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;' :
                'background: #ccc; color: #666; padding: 5px 10px; border: none; border-radius: 3px; cursor: not-allowed;';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($warehouse['id']) . "</td>";
            echo "<td>" . htmlspecialchars($warehouse['name']) . "</td>";
            echo "<td>" . htmlspecialchars($warehouse['city'] . ', ' . $warehouse['state']) . "</td>";
            echo "<td>" . $warehouse['total_boxes'] . "</td>";
            echo "<td>" . $warehouse['total_documents'] . "</td>";
            echo "<td>";
            
            if ($canDelete) {
                echo "<form method='post' style='display: inline;' onsubmit=\"return confirm('Are you sure you want to delete warehouse: {$warehouse['name']}?')\">";
                echo "<input type='hidden' name='warehouse_id' value='{$warehouse['id']}'>";
                echo "<button type='submit' name='delete_warehouse' style='{$deleteButtonStyle}'>Delete</button>";
                echo "</form>";
            } else {
                echo "<button disabled style='{$deleteButtonStyle}' title='Cannot delete: contains {$warehouse['total_boxes']} boxes and {$warehouse['total_documents']} documents'>Cannot Delete</button>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Show deleted warehouses
    echo "<h2>Deleted Warehouses</h2>";
    $deletedWarehouses = $db->fetchAll(
        "SELECT id, name, city, state, updated_at 
         FROM warehouses 
         WHERE status = 'inactive' OR name LIKE '[DELETED]%'
         ORDER BY updated_at DESC"
    );
    
    if (empty($deletedWarehouses)) {
        echo "<p>No deleted warehouses found.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #ffebee;'>";
        echo "<th>ID</th><th>Name</th><th>Location</th><th>Deleted At</th>";
        echo "</tr>";
        
        foreach ($deletedWarehouses as $warehouse) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($warehouse['id']) . "</td>";
            echo "<td>" . htmlspecialchars($warehouse['name']) . "</td>";
            echo "<td>" . htmlspecialchars($warehouse['city'] . ', ' . $warehouse['state']) . "</td>";
            echo "<td>" . htmlspecialchars($warehouse['updated_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<hr>";
    echo "<h2>🔧 Force Delete 'Main Storage Facility'</h2>";
    echo "<p>If the 'Main Storage Facility' warehouse is still showing up and you want to remove it regardless of content:</p>";
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='force_delete_main' style='background: #ff5722; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;' onclick=\"return confirm('Are you sure you want to FORCE DELETE the Main Storage Facility warehouse? This will delete it even if it has content.')\">";
    echo "🗑️ Force Delete 'Main Storage Facility'";
    echo "</button>";
    echo "</form>";

    echo "<hr>";
    echo "<p><a href='/dms/public/app/warehouses' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Warehouses Page</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2 {
    color: #333;
}
table {
    width: 100%;
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}
th {
    background-color: #f5f5f5;
}
</style>
