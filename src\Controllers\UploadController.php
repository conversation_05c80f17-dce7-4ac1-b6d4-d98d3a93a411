<?php

namespace App\Controllers;

/**
 * Upload Controller
 * 
 * Handles file upload functionality including chunked uploads
 */
class UploadController extends BaseController
{
    private $uploadDir;
    private $allowedTypes = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv'
    ];
    private $maxFileSize = 50 * 1024 * 1024; // 50MB
    
    public function __construct()
    {
        parent::__construct();
        $this->uploadDir = APP_ROOT . '/public/uploads/documents';
        
        // Create upload directory if it doesn't exist
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    /**
     * Handle regular file upload
     */
    public function handle()
    {
        $this->requireAuth();
        
        try {
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                throw new \Exception('No file uploaded or upload error.');
            }
            
            $file = $_FILES['file'];
            
            // Validate file
            $this->validateFile($file);
            
            // Generate unique filename
            $filename = $this->generateFilename($file['name']);
            $filepath = $this->uploadDir . '/' . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new \Exception('Failed to save uploaded file.');
            }
            
            // Create document record
            $documentData = [
                'title' => $_POST['title'] ?? pathinfo($file['name'], PATHINFO_FILENAME),
                'description' => $_POST['description'] ?? '',
                'file_name' => $filename,
                'original_name' => $file['name'],
                'file_path' => '/uploads/documents/' . $filename,
                'file_size' => $file['size'],
                'file_type' => $file['type'],
                'category_id' => $_POST['category_id'] ?? null,
                'warehouse_id' => $_POST['warehouse_id'] ?? null,
                'bundle_id' => $_POST['bundle_id'] ?? null,
                'box_id' => $_POST['box_id'] ?? null,
                'location_id' => $_POST['location_id'] ?? null,
                'company_id' => $this->user['company_id'],
                'created_by' => $this->user['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];
            
            $documentId = $this->db->insert('documents', $documentData);
            
            $this->jsonResponse([
                'success' => true,
                'document_id' => $documentId,
                'filename' => $filename,
                'file_path' => $documentData['file_path']
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Handle chunked file upload
     */
    public function chunk()
    {
        $this->requireAuth();
        
        try {
            $chunkIndex = (int)($_POST['chunkIndex'] ?? 0);
            $totalChunks = (int)($_POST['totalChunks'] ?? 1);
            $fileName = $_POST['fileName'] ?? '';
            $fileId = $_POST['fileId'] ?? '';
            
            if (empty($fileName) || empty($fileId)) {
                throw new \Exception('Missing required parameters.');
            }
            
            if (!isset($_FILES['chunk']) || $_FILES['chunk']['error'] !== UPLOAD_ERR_OK) {
                throw new \Exception('Chunk upload failed.');
            }
            
            // Create temp directory for chunks
            $tempDir = $this->uploadDir . '/temp/' . $fileId;
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }
            
            // Save chunk
            $chunkPath = $tempDir . '/chunk_' . $chunkIndex;
            if (!move_uploaded_file($_FILES['chunk']['tmp_name'], $chunkPath)) {
                throw new \Exception('Failed to save chunk.');
            }
            
            $this->jsonResponse([
                'success' => true,
                'chunkIndex' => $chunkIndex,
                'totalChunks' => $totalChunks
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Complete chunked upload
     */
    public function complete()
    {
        $this->requireAuth();
        
        try {
            $fileName = $_POST['fileName'] ?? '';
            $fileId = $_POST['fileId'] ?? '';
            $totalChunks = (int)($_POST['totalChunks'] ?? 1);
            
            if (empty($fileName) || empty($fileId)) {
                throw new \Exception('Missing required parameters.');
            }
            
            $tempDir = $this->uploadDir . '/temp/' . $fileId;
            
            // Verify all chunks exist
            for ($i = 0; $i < $totalChunks; $i++) {
                $chunkPath = $tempDir . '/chunk_' . $i;
                if (!file_exists($chunkPath)) {
                    throw new \Exception('Missing chunk: ' . $i);
                }
            }
            
            // Combine chunks
            $finalFilename = $this->generateFilename($fileName);
            $finalPath = $this->uploadDir . '/' . $finalFilename;
            
            $finalFile = fopen($finalPath, 'wb');
            if (!$finalFile) {
                throw new \Exception('Failed to create final file.');
            }
            
            for ($i = 0; $i < $totalChunks; $i++) {
                $chunkPath = $tempDir . '/chunk_' . $i;
                $chunkData = file_get_contents($chunkPath);
                fwrite($finalFile, $chunkData);
                unlink($chunkPath); // Clean up chunk
            }
            
            fclose($finalFile);
            rmdir($tempDir); // Clean up temp directory
            
            // Get file info
            $fileSize = filesize($finalPath);
            $fileType = mime_content_type($finalPath);
            
            // Validate the completed file
            $this->validateCompletedFile($finalPath, $fileSize, $fileType);
            
            // Create document record
            $documentData = [
                'title' => $_POST['title'] ?? pathinfo($fileName, PATHINFO_FILENAME),
                'description' => $_POST['description'] ?? '',
                'file_name' => $finalFilename,
                'original_name' => $fileName,
                'file_path' => '/uploads/documents/' . $finalFilename,
                'file_size' => $fileSize,
                'file_type' => $fileType,
                'category_id' => $_POST['category_id'] ?? null,
                'warehouse_id' => $_POST['warehouse_id'] ?? null,
                'bundle_id' => $_POST['bundle_id'] ?? null,
                'box_id' => $_POST['box_id'] ?? null,
                'location_id' => $_POST['location_id'] ?? null,
                'company_id' => $this->user['company_id'],
                'created_by' => $this->user['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];
            
            $documentId = $this->db->insert('documents', $documentData);
            
            $this->jsonResponse([
                'success' => true,
                'document_id' => $documentId,
                'filename' => $finalFilename,
                'file_path' => $documentData['file_path']
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Validate uploaded file
     */
    private function validateFile($file)
    {
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            throw new \Exception('File size exceeds maximum allowed size of ' . ($this->maxFileSize / 1024 / 1024) . 'MB.');
        }
        
        // Check file type
        if (!in_array($file['type'], $this->allowedTypes)) {
            throw new \Exception('File type not allowed. Allowed types: PDF, DOC, DOCX, XLS, XLSX, TXT, CSV, JPG, PNG, GIF.');
        }
        
        // Additional security checks
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'csv', 'jpg', 'jpeg', 'png', 'gif'];
        
        if (!in_array($extension, $allowedExtensions)) {
            throw new \Exception('File extension not allowed.');
        }
    }
    
    /**
     * Validate completed file after chunked upload
     */
    private function validateCompletedFile($filePath, $fileSize, $fileType)
    {
        // Check file size
        if ($fileSize > $this->maxFileSize) {
            unlink($filePath); // Clean up
            throw new \Exception('File size exceeds maximum allowed size.');
        }
        
        // Check file type
        if (!in_array($fileType, $this->allowedTypes)) {
            unlink($filePath); // Clean up
            throw new \Exception('File type not allowed.');
        }
    }
    
    /**
     * Generate unique filename
     */
    private function generateFilename($originalName)
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $basename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // Sanitize filename
        $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $basename = substr($basename, 0, 50); // Limit length
        
        // Add timestamp and random string for uniqueness
        $timestamp = time();
        $random = bin2hex(random_bytes(4));
        
        return $basename . '_' . $timestamp . '_' . $random . '.' . $extension;
    }
}
