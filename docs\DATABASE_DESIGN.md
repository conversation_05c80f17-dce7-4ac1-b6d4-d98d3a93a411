# Database Design - Document Management System

## Database Schema

### 1. Companies Table
```sql
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. Users Table
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON>R(100),
    role E<PERSON>M('super_admin', 'company_admin', 'manager', 'editor', 'viewer') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);
```

### 3. Warehouses Table
```sql
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL
);
```

### 4. Storage Locations Table
```sql
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_location (warehouse_id, identifier)
);
```

### 5. Documents Table
```sql
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL, -- For version control
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash
    version VARCHAR(20) DEFAULT '1.0',
    is_latest_version BOOLEAN DEFAULT TRUE,
    document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
    category_id INT,
    tags JSON,
    metadata JSON,
    ocr_text LONGTEXT,
    thumbnail_path VARCHAR(500),
    preview_path VARCHAR(500),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    access_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    retention_date DATE,
    expiry_date DATE,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    INDEX idx_documents_search (title, description),
    FULLTEXT KEY ft_documents_content (title, description, ocr_text)
);
```

### 6. Document Categories Table
```sql
CREATE TABLE document_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES document_categories(id) ON DELETE CASCADE
);
```

### 7. Document Locations Table
```sql
CREATE TABLE document_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    document_id INT NOT NULL,
    location_id INT,
    storage_type ENUM('physical', 'digital', 'hybrid') NOT NULL,
    physical_reference VARCHAR(255), -- Box number, shelf reference, etc.
    digital_path VARCHAR(1000),
    quantity INT DEFAULT 1,
    condition_notes TEXT,
    moved_by INT NOT NULL,
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id)
);
```

### 8. Barcodes Table
```sql
CREATE TABLE barcodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('document', 'location', 'box', 'bundle') NOT NULL,
    entity_id INT NOT NULL,
    barcode_value VARCHAR(255) UNIQUE NOT NULL,
    barcode_type ENUM('qr', 'code128', 'code39', 'ean13', 'datamatrix') DEFAULT 'qr',
    barcode_image_path VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    generated_by INT NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_scanned_at TIMESTAMP NULL,
    scan_count INT DEFAULT 0,
    FOREIGN KEY (generated_by) REFERENCES users(id),
    INDEX idx_barcode_entity (entity_type, entity_id)
);
```

### 9. Bundles Table
```sql
CREATE TABLE bundles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    bundle_type ENUM('intake', 'project', 'department', 'custom') DEFAULT 'custom',
    location_id INT,
    created_by INT NOT NULL,
    status ENUM('open', 'closed', 'archived') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### 10. Bundle Documents Table
```sql
CREATE TABLE bundle_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bundle_id INT NOT NULL,
    document_id INT NOT NULL,
    added_by INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sort_order INT DEFAULT 0,
    FOREIGN KEY (bundle_id) REFERENCES bundles(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id),
    UNIQUE KEY unique_bundle_document (bundle_id, document_id)
);
```

### 11. Audit Logs Table
```sql
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    user_id INT,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    action ENUM('create', 'read', 'update', 'delete', 'login', 'logout', 'download', 'upload') NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON SET NULL,
    INDEX idx_audit_logs_search (company_id, entity_type, action, created_at)
);
```

### 12. System Settings Table
```sql
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT,
    setting_key VARCHAR(255) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    is_system BOOLEAN DEFAULT FALSE,
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(id),
    UNIQUE KEY unique_company_setting (company_id, setting_key)
);
```

## Indexes and Performance Optimization

### Primary Indexes
```sql
-- Search optimization
CREATE INDEX idx_documents_company_search ON documents(company_id, status, created_at);
CREATE INDEX idx_documents_category ON documents(category_id, status);
CREATE INDEX idx_documents_creator ON documents(created_by, created_at);

-- Location hierarchy optimization
CREATE INDEX idx_locations_hierarchy ON storage_locations(warehouse_id, parent_id, type);
CREATE INDEX idx_locations_barcode ON storage_locations(barcode_value);

-- Audit trail optimization
CREATE INDEX idx_audit_company_date ON audit_logs(company_id, created_at);
CREATE INDEX idx_audit_user_action ON audit_logs(user_id, action, created_at);

-- Barcode scanning optimization
CREATE INDEX idx_barcodes_scan ON barcodes(barcode_value, is_active);
CREATE INDEX idx_barcodes_entity ON barcodes(entity_type, entity_id);
```

### Full-Text Search Indexes
```sql
-- Document content search
ALTER TABLE documents ADD FULLTEXT(title, description, ocr_text);

-- Company and user search
ALTER TABLE companies ADD FULLTEXT(name, domain);
ALTER TABLE users ADD FULLTEXT(first_name, last_name, email);
```

## Data Relationships

### Key Relationships
1. **Companies** → **Users** (One-to-Many)
2. **Companies** → **Warehouses** (One-to-Many)
3. **Warehouses** → **Storage Locations** (One-to-Many, Hierarchical)
4. **Documents** → **Document Locations** (One-to-Many, Historical)
5. **Documents** → **Documents** (Self-referencing for versions)
6. **Bundles** → **Documents** (Many-to-Many through bundle_documents)
7. **Barcodes** → **Multiple Entities** (Polymorphic relationship)

### Referential Integrity
- All foreign keys include appropriate CASCADE or SET NULL actions
- Soft deletes implemented for critical data (documents, companies)
- Audit trail maintains data even after entity deletion
