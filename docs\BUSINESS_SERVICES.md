# Business Services Implementation

## Overview

This document outlines the implementation of core business services for the Document Management System. These services provide the essential functionality that your clients (companies) will use to manage their document storage and retrieval operations.

## Core Business Services

### 1. Box Handling Service
**Purpose**: Complete lifecycle management of storage boxes

**Key Features**:
- ✅ Box creation with auto-generated IDs (CLIENT01-BOX001 format)
- ✅ Storage location management (WH-R1-S2-B03 format)
- ✅ Box movement tracking with audit trail
- ✅ Capacity management and status automation
- ✅ Bundle assignment to boxes
- ✅ Barcode generation and management

**API Endpoints**:
```
POST /app/services/boxes/create
POST /app/services/boxes/{id}/move
POST /app/services/boxes/add-bundle
```

**Usage Example**:
```javascript
// Create a new box
const boxData = {
    name: "Legal Documents Box",
    warehouse_id: 1,
    client_prefix: "CLIENT01",
    description: "Box for legal contracts",
    storage_type: "physical",
    capacity: 100
};

const response = await fetch('/app/services/boxes/create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(boxData)
});
```

### 2. Bundle/File Handling Service
**Purpose**: Manage document bundles and file processing workflows

**Key Features**:
- ✅ Bundle creation with reference numbers
- ✅ File upload and validation
- ✅ Document processing and metadata extraction
- ✅ Bundle closure and preparation for boxing
- ✅ Version control and file history
- ✅ Thumbnail generation for images

**API Endpoints**:
```
POST /app/services/bundles/create
POST /app/services/bundles/{id}/close
```

**Usage Example**:
```javascript
// Create a new bundle
const bundleData = {
    name: "Q1 2024 Financial Reports",
    description: "Quarterly financial documents",
    bundle_type: "project",
    category: "financial",
    priority: "high",
    intake_id: 123
};

const response = await fetch('/app/services/bundles/create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(bundleData)
});
```

### 3. Delivery Service
**Purpose**: Handle delivery of boxes, bundles, and files to clients

**Key Features**:
- ✅ Delivery request creation and management
- ✅ Multiple delivery types (physical, digital, both)
- ✅ Tracking system with real-time updates
- ✅ Client notifications
- ✅ Return management
- ✅ Delivery scheduling and routing

**API Endpoints**:
```
POST /app/services/delivery/create
GET /app/services/delivery/track/{reference}
```

**Usage Example**:
```javascript
// Create delivery request
const deliveryData = {
    target_type: "box",
    target_id: 456,
    client_id: 789,
    delivery_type: "physical",
    priority: "urgent",
    delivery_method: "courier",
    requested_delivery_date: "2024-01-15",
    delivery_address: "123 Main St, City, State",
    special_instructions: "Handle with care - fragile documents"
};

const response = await fetch('/app/services/delivery/create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(deliveryData)
});
```

### 4. Enhanced Search Service
**Purpose**: Comprehensive search across all entities with advanced filtering

**Key Features**:
- ✅ Unified search across documents, bundles, and boxes
- ✅ Full-text search with relevance scoring
- ✅ Advanced filtering and sorting options
- ✅ Search suggestions and autocomplete
- ✅ Search analytics and reporting
- ✅ OCR text search for scanned documents

**API Endpoints**:
```
GET /app/services/search?q={query}&filters={filters}
GET /app/services/search/suggestions?q={query}
```

**Usage Example**:
```javascript
// Perform unified search
const searchParams = new URLSearchParams({
    q: "financial reports 2024",
    'filters[search_types]': 'documents,bundles',
    'filters[document_type]': 'report',
    'filters[date_from]': '2024-01-01',
    'filters[sort]': 'relevance'
});

const response = await fetch(`/app/services/search?${searchParams}`);
```

### 5. Enhanced Intake Service
**Purpose**: Streamlined document intake workflow with automation

**Key Features**:
- ✅ Intake request creation with validation
- ✅ Workflow automation (approve, process, complete)
- ✅ Business rule validation
- ✅ Auto-bundle creation
- ✅ Client notifications
- ✅ Intake metrics and analytics

**API Endpoints**:
```
POST /app/services/intake/create
```

**Usage Example**:
```javascript
// Create intake request
const intakeData = {
    client_name: "ABC Corporation",
    client_id: 123,
    source: "Email submission",
    document_type: "contract",
    description: "Legal contracts for Q1 2024",
    priority: "high",
    expected_count: 25,
    sensitivity_level: "confidential",
    auto_create_bundle: true,
    notify_client: true
};

const response = await fetch('/app/services/intake/create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(intakeData)
});
```

## Service Architecture

### Service Factory Pattern
All services are managed through a centralized ServiceFactory that:
- Provides singleton instances
- Manages user context
- Handles dependency injection
- Ensures consistent initialization

### Base Service Class
All services extend BaseService which provides:
- Database access
- User authentication validation
- Activity logging
- Common validation methods
- Notification system integration

### Error Handling
All services implement consistent error handling:
- Input validation with detailed error messages
- Database transaction management
- Graceful failure recovery
- Comprehensive logging

## Database Schema

### New Tables Added:
1. **delivery_requests** - Main delivery tracking
2. **delivery_items** - Detailed item tracking
3. **delivery_tracking** - Status updates and location tracking
4. **delivery_returns** - Return management
5. **box_movements** - Box location history
6. **search_logs** - Search analytics

### Migration File:
```sql
-- Run this migration to add service tables
database/migrations/025_create_delivery_tables.sql
```

## Integration with Existing Controllers

The business services integrate seamlessly with existing controllers:

```php
// In any controller
use App\Services\ServiceFactory;

// Initialize with current user context
ServiceFactory::initialize($this->db, $this->user, $this->company);

// Use services
$boxService = ServiceFactory::getBoxHandlingService();
$result = $boxService->createBox($data);
```

## Service Status Monitoring

Monitor all services through the status endpoint:
```
GET /app/services/status
```

Returns health status of all business services.

## Next Steps

1. **Run Database Migration**:
   ```bash
   # Apply the new service tables
   mysql -u username -p database_name < database/migrations/025_create_delivery_tables.sql
   ```

2. **Test Services**:
   - Use the provided API endpoints
   - Test with sample data
   - Verify error handling

3. **Frontend Integration**:
   - Update existing forms to use new services
   - Add service status monitoring to dashboard
   - Implement real-time notifications

4. **Client Training**:
   - Document service workflows
   - Create user guides
   - Provide API documentation for integrations

## Benefits for Your Clients

1. **Streamlined Operations**: Automated workflows reduce manual work
2. **Better Tracking**: Real-time visibility into document location and status
3. **Improved Search**: Find documents faster with advanced search capabilities
4. **Delivery Management**: Professional delivery tracking and client communication
5. **Scalability**: Services handle growing document volumes efficiently
6. **Audit Trail**: Complete history of all document operations
7. **Integration Ready**: API endpoints for custom integrations

This implementation provides a solid foundation for your document management business services, ensuring professional operations and satisfied clients.
