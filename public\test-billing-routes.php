<?php
/**
 * Test Billing Routes
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

echo "<h1>Billing Routes Test</h1>";

$testRoutes = [
    'GET /app/billing' => 'Billing Dashboard',
    'GET /app/billing/rates' => 'Service Rates',
    'POST /app/billing/rates/save' => 'Save Service Rate',
    'GET /app/billing/rates/toggle/1' => 'Toggle Service Rate',
    'GET /app/billing/client/1' => 'Client Billing Details',
    'POST /app/billing/generate-invoice' => 'Generate Invoice',
    'GET /app/billing/invoice/1' => 'View Invoice',
    'POST /app/billing/invoice/1/mark-paid' => 'Mark Invoice Paid',
    'POST /app/billing/log-event' => 'Log Billing Event'
];

echo "<h3>Available Billing Routes:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Method & Route</th><th>Description</th><th>Test Link</th></tr>";

foreach ($testRoutes as $route => $description) {
    list($method, $path) = explode(' ', $route, 2);
    $testUrl = url($path);
    
    echo "<tr>";
    echo "<td><code>{$route}</code></td>";
    echo "<td>{$description}</td>";
    
    if ($method === 'GET') {
        echo "<td><a href='{$testUrl}' target='_blank'>Test</a></td>";
    } else {
        echo "<td><em>POST route - test via form</em></td>";
    }
    
    echo "</tr>";
}

echo "</table>";

echo "<h3>Quick Tests:</h3>";
echo "<ul>";
echo "<li><a href='" . url('/app/billing') . "' target='_blank'>✅ Billing Dashboard</a></li>";
echo "<li><a href='" . url('/app/billing/rates') . "' target='_blank'>✅ Service Rates (with Add/Edit functionality)</a></li>";
echo "<li><a href='" . url('/app/billing/client/1') . "' target='_blank'>✅ Client Billing Details</a></li>";
echo "</ul>";

echo "<h3>Test Add New Service Rate:</h3>";
echo "<p>Go to <a href='" . url('/app/billing/rates') . "' target='_blank'>Service Rates</a> and click 'Add New Rate' to test the save functionality.</p>";

echo "<h3>Form Test - Add Service Rate:</h3>";
?>

<form method="POST" action="<?= url('/app/billing/rates/save') ?>" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
    <h4>Test Service Rate Form</h4>
    
    <div style="margin-bottom: 10px;">
        <label>Service Code:</label><br>
        <input type="text" name="service_code" value="test.service" required style="width: 200px; padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Service Name:</label><br>
        <input type="text" name="service_name" value="Test Service" required style="width: 300px; padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Description:</label><br>
        <textarea name="service_description" style="width: 300px; height: 60px; padding: 5px;">Test service description</textarea>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Rate ($):</label><br>
        <input type="number" name="rate" value="25.00" step="0.01" required style="width: 100px; padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Unit:</label><br>
        <select name="unit" required style="width: 100px; padding: 5px;">
            <option value="each">Each</option>
            <option value="hour">Hour</option>
            <option value="page">Page</option>
        </select>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Category:</label><br>
        <select name="category" required style="width: 150px; padding: 5px;">
            <option value="processing">Processing</option>
            <option value="handling">Handling</option>
            <option value="search">Search</option>
        </select>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>
            <input type="checkbox" name="is_active" checked> Active
        </label>
    </div>
    
    <button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
        Save Test Rate
    </button>
</form>

<hr>
<p><strong>Note:</strong> Make sure you're logged in to test the billing routes. If you get authentication errors, log in first at <a href="<?= url('/login') ?>">Login Page</a>.</p>

<?php
echo "<h3>Current Session Info:</h3>";
echo "<p><strong>Logged in:</strong> " . (isset($_SESSION['user']) ? 'Yes' : 'No') . "</p>";
if (isset($_SESSION['user'])) {
    echo "<p><strong>User:</strong> " . htmlspecialchars($_SESSION['user']['username'] ?? 'Unknown') . "</p>";
}
?>
