<?php

namespace App\Services;

use App\Core\Database;

/**
 * Base Service Class
 * 
 * Provides common functionality for all business services
 */
abstract class BaseService
{
    protected $db;
    protected $user;
    protected $company;

    public function __construct(Database $db, $user = null, $company = null)
    {
        $this->db = $db;
        $this->user = $user;
        $this->company = $company;
    }

    /**
     * Log activity for audit trail
     */
    protected function logActivity($action, $entity_type, $entity_id, $description)
    {
        if (!$this->user) {
            return;
        }

        $this->db->execute(
            "INSERT INTO activity_logs (
                user_id, company_id, action, entity_type, entity_id, 
                description, ip_address, user_agent, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())",
            [
                $this->user['id'],
                $this->user['company_id'],
                $action,
                $entity_type,
                $entity_id,
                $description,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]
        );
    }

    /**
     * Validate user permissions for company data
     */
    protected function validateCompanyAccess($companyId)
    {
        if (!$this->user) {
            throw new \Exception('User authentication required');
        }

        if ($this->user['company_id'] != $companyId) {
            throw new \Exception('Access denied: Invalid company access');
        }
    }

    /**
     * Generate unique reference number
     */
    protected function generateReferenceNumber($prefix, $table, $column = 'reference_number')
    {
        $year = date('Y');
        $month = date('m');
        
        // Get the last number for this month
        $lastRecord = $this->db->fetch(
            "SELECT {$column} FROM {$table} 
             WHERE company_id = ? AND {$column} LIKE ? 
             ORDER BY {$column} DESC LIMIT 1",
            [$this->user['company_id'], "{$prefix}-{$year}{$month}-%"]
        );

        if ($lastRecord) {
            // Extract the sequence number
            $parts = explode('-', $lastRecord[$column]);
            $sequence = intval(end($parts)) + 1;
        } else {
            $sequence = 1;
        }

        return "{$prefix}-{$year}{$month}-" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Send notification (placeholder for future implementation)
     */
    protected function sendNotification($type, $recipient, $subject, $message, $data = [])
    {
        // TODO: Implement notification system
        // This could send emails, SMS, push notifications, etc.
        
        // For now, just log the notification
        $this->logActivity(
            'notification',
            'system',
            null,
            "Notification sent: {$type} to {$recipient} - {$subject}"
        );
    }

    /**
     * Validate required fields
     */
    protected function validateRequired($data, $requiredFields)
    {
        $missing = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing[] = $field;
            }
        }

        if (!empty($missing)) {
            throw new \Exception('Missing required fields: ' . implode(', ', $missing));
        }
    }

    /**
     * Sanitize input data
     */
    protected function sanitizeData($data)
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = trim(htmlspecialchars($value, ENT_QUOTES, 'UTF-8'));
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }
}
