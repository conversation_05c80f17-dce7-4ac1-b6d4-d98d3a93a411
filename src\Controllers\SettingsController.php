<?php

namespace App\Controllers;

/**
 * Settings Controller
 * 
 * Handles user and company settings management
 */
class SettingsController extends BaseController
{
    /**
     * Show settings page
     */
    public function index()
    {
        $this->requireAuth();
        
        // Get user settings
        $userSettings = $this->getUserSettings();
        
        // Get company settings (if user has permission)
        $companySettings = [];
        if ($this->user['role'] === 'admin' || $this->user['role'] === 'super_admin') {
            $companySettings = $this->getCompanySettings();
        }
        
        $this->view('settings/index', [
            'title' => 'Settings',
            'userSettings' => $userSettings,
            'companySettings' => $companySettings,
            'canManageCompany' => in_array($this->user['role'], ['admin', 'super_admin'])
        ]);
    }
    
    /**
     * Update user profile settings
     */
    public function updateProfile()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/app/settings');
        }
        
        try {
            $data = [
                'first_name' => trim($_POST['first_name'] ?? ''),
                'last_name' => trim($_POST['last_name'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'phone' => trim($_POST['phone'] ?? ''),
                'timezone' => $_POST['timezone'] ?? 'UTC',
                'language' => $_POST['language'] ?? 'en'
            ];
            
            // Validate required fields
            if (empty($data['first_name']) || empty($data['last_name']) || empty($data['email'])) {
                throw new \Exception('First name, last name, and email are required.');
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception('Please enter a valid email address.');
            }
            
            // Check if email is already taken by another user
            if ($this->db) {
                $existingUser = $this->db->fetch(
                    "SELECT id FROM users WHERE email = ? AND id != ?",
                    [$data['email'], $this->user['id']]
                );
                
                if ($existingUser) {
                    throw new \Exception('This email address is already in use.');
                }
                
                // Update user profile
                $this->db->execute(
                    "UPDATE users SET 
                     first_name = ?, last_name = ?, email = ?, phone = ?, 
                     timezone = ?, language = ?, updated_at = NOW()
                     WHERE id = ?",
                    [
                        $data['first_name'], $data['last_name'], $data['email'], 
                        $data['phone'], $data['timezone'], $data['language'], 
                        $this->user['id']
                    ]
                );
                
                // Log activity
                $this->logActivity('update', 'user_profile', $this->user['id'], 'Updated profile information');
            }
            
            $this->setFlashMessage('Profile updated successfully!', 'success');
            
        } catch (\Exception $e) {
            $this->setFlashMessage('Error updating profile: ' . $e->getMessage(), 'error');
        }
        
        $this->redirect('/app/settings');
    }
    
    /**
     * Update password
     */
    public function updatePassword()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/app/settings');
        }
        
        try {
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            // Validate inputs
            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                throw new \Exception('All password fields are required.');
            }
            
            if ($newPassword !== $confirmPassword) {
                throw new \Exception('New passwords do not match.');
            }
            
            if (strlen($newPassword) < 8) {
                throw new \Exception('New password must be at least 8 characters long.');
            }
            
            if ($this->db) {
                // Verify current password
                $user = $this->db->fetch(
                    "SELECT password FROM users WHERE id = ?",
                    [$this->user['id']]
                );
                
                if (!$user || !password_verify($currentPassword, $user['password'])) {
                    throw new \Exception('Current password is incorrect.');
                }
                
                // Update password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $this->db->execute(
                    "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?",
                    [$hashedPassword, $this->user['id']]
                );
                
                // Log activity
                $this->logActivity('update', 'user_password', $this->user['id'], 'Changed password');
            }
            
            $this->setFlashMessage('Password updated successfully!', 'success');
            
        } catch (\Exception $e) {
            $this->setFlashMessage('Error updating password: ' . $e->getMessage(), 'error');
        }
        
        $this->redirect('/app/settings');
    }
    
    /**
     * Update notification preferences
     */
    public function updateNotifications()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/app/settings');
        }
        
        try {
            $preferences = [
                'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0,
                'document_alerts' => isset($_POST['document_alerts']) ? 1 : 0,
                'system_updates' => isset($_POST['system_updates']) ? 1 : 0,
                'weekly_reports' => isset($_POST['weekly_reports']) ? 1 : 0
            ];
            
            if ($this->db) {
                // Update or insert notification preferences
                foreach ($preferences as $key => $value) {
                    $existing = $this->db->fetch(
                        "SELECT id FROM user_preferences WHERE user_id = ? AND preference_key = ?",
                        [$this->user['id'], $key]
                    );
                    
                    if ($existing) {
                        $this->db->execute(
                            "UPDATE user_preferences SET preference_value = ?, updated_at = NOW() 
                             WHERE user_id = ? AND preference_key = ?",
                            [$value, $this->user['id'], $key]
                        );
                    } else {
                        $this->db->execute(
                            "INSERT INTO user_preferences (user_id, preference_key, preference_value, created_at, updated_at)
                             VALUES (?, ?, ?, NOW(), NOW())",
                            [$this->user['id'], $key, $value]
                        );
                    }
                }
                
                // Log activity
                $this->logActivity('update', 'user_preferences', $this->user['id'], 'Updated notification preferences');
            }
            
            $this->setFlashMessage('Notification preferences updated successfully!', 'success');
            
        } catch (\Exception $e) {
            $this->setFlashMessage('Error updating preferences: ' . $e->getMessage(), 'error');
        }
        
        $this->redirect('/app/settings');
    }
    
    /**
     * Get user settings
     */
    private function getUserSettings()
    {
        // Get fresh user data from database to ensure we have all columns
        $userData = [];
        if ($this->db) {
            try {
                $userData = $this->db->fetch(
                    "SELECT first_name, last_name, email, phone, timezone, language FROM users WHERE id = ?",
                    [$this->user['id']]
                );
            } catch (\Exception $e) {
                // Fallback to session data if query fails
                $userData = $this->user;
            }
        } else {
            $userData = $this->user;
        }

        $settings = [
            'first_name' => $userData['first_name'] ?? '',
            'last_name' => $userData['last_name'] ?? '',
            'email' => $userData['email'] ?? '',
            'phone' => $userData['phone'] ?? '',
            'timezone' => $userData['timezone'] ?? 'UTC',
            'language' => $userData['language'] ?? 'en',
            'preferences' => []
        ];
        
        if ($this->db) {
            try {
                // Get user preferences
                $preferences = $this->db->fetchAll(
                    "SELECT preference_key, preference_value FROM user_preferences WHERE user_id = ?",
                    [$this->user['id']]
                );
                
                foreach ($preferences as $pref) {
                    $settings['preferences'][$pref['preference_key']] = $pref['preference_value'];
                }
                
            } catch (\Exception $e) {
                // Continue with default settings
            }
        }
        
        return $settings;
    }
    
    /**
     * Get company settings (for admins)
     */
    private function getCompanySettings()
    {
        $settings = [];
        
        if ($this->db) {
            try {
                $company = $this->db->fetch(
                    "SELECT * FROM companies WHERE id = ?",
                    [$this->user['company_id']]
                );
                
                if ($company) {
                    $settings = $company;
                }
                
            } catch (\Exception $e) {
                // Continue with empty settings
            }
        }
        
        return $settings;
    }
}
