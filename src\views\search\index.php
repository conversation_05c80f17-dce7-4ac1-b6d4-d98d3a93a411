<?php
$title = 'Search';
ob_start();
?>

<div class="min-h-screen bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Simple Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Search</h1>
            <p class="text-gray-600 mt-1">Find documents, bundles, boxes, and warehouses</p>
        </div>

        <!-- Enhanced Search Form -->
        <div class="relative mb-8">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-3xl blur-xl"></div>
            <div class="relative bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md">
                <div class="max-w-4xl mx-auto">
                    <!-- Search Header -->
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">🔍 Refine Your Search</h3>
                        <p class="text-gray-600">Modify your search terms or filters to find exactly what you need</p>
                    </div>

                    <!-- Main Search Bar -->
                    <div class="relative group search-container-wrapper">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="relative flex items-center bg-white border-2 border-gray-200 rounded-2xl shadow-lg hover:border-blue-300 focus-within:border-blue-500 transition-all duration-300">
                            <!-- Search Icon -->
                            <div class="pl-6 pr-3">
                                <svg class="w-6 h-6 text-gray-400 group-focus-within:text-blue-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>

                            <!-- Search Input -->
                            <input
                                type="text"
                                id="search-input"
                                name="q"
                                value="<?= e($query) ?>"
                                placeholder="Search documents, bundles, boxes, or enter keywords..."
                                class="flex-1 py-4 px-2 text-lg bg-transparent border-none outline-none placeholder-gray-400 text-gray-900"
                                autocomplete="off"
                                onkeyup="handleSearchInput(event)"
                                onfocus="showSearchSuggestions()"
                            >

                            <!-- Search Actions -->
                            <div class="flex items-center space-x-2 pr-4">
                                <!-- Advanced Filters Button -->
                                <button
                                    type="button"
                                    onclick="toggleAdvancedFilters()"
                                    class="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-all duration-200"
                                    title="Advanced Filters"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                    </svg>
                                </button>

                                <!-- Search Button -->
                                <button
                                    type="submit"
                                    class="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    <span class="font-medium">Search</span>
                                </button>
                            </div>
                        </div>

                        <!-- Search Suggestions Dropdown -->
                        <div id="search-suggestions" class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-[9999] hidden">
                            <div class="p-4">
                                <div class="text-sm text-gray-500 mb-3">Search suggestions</div>
                                <div class="space-y-2" id="suggestions-list">
                                    <!-- Suggestions will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Filters Panel (Hidden by default) -->
                    <div id="advanced-filters" class="mt-6 p-6 bg-gray-50/50 rounded-xl border border-gray-200 hidden">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Search Type Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Search In</label>
                                <div class="space-y-2">
                                    <?php
                                    $searchTypes = explode(',', $_GET['types'] ?? 'documents,bundles,boxes');
                                    $allTypes = [
                                        'documents' => '📄 Documents',
                                        'bundles' => '📁 Bundles',
                                        'boxes' => '📦 Boxes',
                                        'warehouses' => '🏢 Warehouses'
                                    ];
                                    ?>
                                    <?php foreach ($allTypes as $type => $label): ?>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="search_type" value="<?= $type ?>"
                                                   <?= in_array($type, $searchTypes) ? 'checked' : '' ?>
                                                   class="mr-2 text-blue-500">
                                            <span class="text-sm"><?= $label ?></span>
                                        </label>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Date Range Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                                <div class="space-y-2">
                                    <input type="date" id="date-from" name="from" value="<?= e($_GET['from'] ?? '') ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                    <input type="date" id="date-to" name="to" value="<?= e($_GET['to'] ?? '') ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                </div>
                            </div>

                            <!-- File Type Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">File Type</label>
                                <select id="file-type" name="file_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                    <option value="">All Types</option>
                                    <option value="pdf" <?= ($_GET['file_type'] ?? '') == 'pdf' ? 'selected' : '' ?>>PDF</option>
                                    <option value="doc" <?= ($_GET['file_type'] ?? '') == 'doc' ? 'selected' : '' ?>>Word Document</option>
                                    <option value="xls" <?= ($_GET['file_type'] ?? '') == 'xls' ? 'selected' : '' ?>>Excel</option>
                                    <option value="img" <?= ($_GET['file_type'] ?? '') == 'img' ? 'selected' : '' ?>>Images</option>
                                </select>
                            </div>
                        </div>

                        <!-- Filter Actions -->
                        <div class="flex items-center justify-end space-x-3 mt-4 pt-4 border-t border-gray-200">
                            <a href="<?= url('/app/search') ?>" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                                Clear All
                            </a>
                            <button type="submit" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                Apply Filters
                            </button>
                        </div>
                    </div>

                    <!-- Quick Search Categories -->
                    <div class="mt-6 flex flex-wrap items-center justify-center gap-3">
                        <span class="text-sm text-gray-500">Quick search:</span>
                        <button type="button" onclick="quickSearch('recent')" class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors">
                            Recent Items
                        </button>
                        <button type="button" onclick="quickSearch('physical')" class="px-3 py-1 text-xs bg-amber-100 text-amber-700 rounded-full hover:bg-amber-200 transition-colors">
                            Physical Storage
                        </button>
                        <button type="button" onclick="quickSearch('online')" class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors">
                            Online Storage
                        </button>
                        <button type="button" onclick="quickSearch('pending')" class="px-3 py-1 text-xs bg-yellow-100 text-yellow-700 rounded-full hover:bg-yellow-200 transition-colors">
                            Pending Items
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form for submission (hidden) -->
        <form id="search-form" method="GET" action="<?= url('/app/search') ?>" style="display: none;">
            <input type="hidden" name="q" id="hidden-query">
            <input type="hidden" name="types" id="hidden-types">
            <input type="hidden" name="from" id="hidden-from">
            <input type="hidden" name="to" id="hidden-to">
            <input type="hidden" name="file_type" id="hidden-file-type">
        </form>

        <!-- Search Results -->
        <?php if (!empty($query)): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">

                <!-- Results Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Search Results</h2>
                    <p class="text-sm text-gray-600 mt-1">
                        Found <?= number_format($totalCount) ?> result(s) for "<strong><?= e($query) ?></strong>"
                        <?php if ($searchTime > 0): ?>
                            <span class="text-gray-500">in <?= $searchTime ?>ms</span>
                        <?php endif; ?>
                    </p>
                </div>

                <!-- Results Content -->
                <?php if (empty($results)): ?>
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                        <p class="text-gray-600">Try adjusting your search terms or filters.</p>
                    </div>
                <?php else: ?>
                    <!-- Simple Results List -->
                    <div class="divide-y divide-gray-200">
                        <?php foreach ($results as $result): ?>
                            <div class="p-6 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start space-x-4">
                                    <!-- Result Icon -->
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg"><?= $result['result_icon'] ?? '📄' ?></span>
                                        </div>
                                    </div>

                                    <!-- Result Content -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1 min-w-0">
                                                <h3 class="text-lg font-medium text-gray-900 hover:text-blue-600">
                                                    <a href="<?= $result['result_url'] ?? '#' ?>">
                                                        <?= highlightSearchTerms(e($result['title'] ?? $result['name'] ?? 'Untitled'), $query) ?>
                                                    </a>
                                                </h3>

                                                <!-- Result Type Badge -->
                                                <div class="flex items-center space-x-2 mt-1">
                                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                                                        <?= ucfirst($result['result_type'] ?? 'document') ?>
                                                    </span>

                                                    <!-- Additional Info -->
                                                    <?php if (isset($result['file_name'])): ?>
                                                        <span class="text-sm text-gray-500"><?= e($result['file_name']) ?></span>
                                                    <?php elseif (isset($result['box_id'])): ?>
                                                        <span class="text-sm text-gray-500">ID: <?= e($result['box_id']) ?></span>
                                                    <?php elseif (isset($result['reference_number'])): ?>
                                                        <span class="text-sm text-gray-500">Ref: <?= e($result['reference_number']) ?></span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Description -->
                                                <?php if (!empty($result['description'])): ?>
                                                    <p class="text-sm text-gray-600 mt-2 line-clamp-2">
                                                        <?= highlightSearchTerms(e(substr($result['description'], 0, 150)), $query) ?>
                                                        <?= strlen($result['description']) > 150 ? '...' : '' ?>
                                                    </p>
                                                <?php endif; ?>

                                                <!-- Metadata -->
                                                <div class="flex items-center space-x-4 mt-3 text-xs text-gray-500">
                                                    <?php if (isset($result['file_size'])): ?>
                                                        <span><?= formatBytes($result['file_size']) ?></span>
                                                    <?php endif; ?>

                                                    <?php if (isset($result['warehouse_name'])): ?>
                                                        <span>📍 <?= e($result['warehouse_name']) ?></span>
                                                    <?php endif; ?>

                                                    <?php if (isset($result['created_at'])): ?>
                                                        <span><?= date('M j, Y', strtotime($result['created_at'])) ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <!-- View Button -->
                                            <div class="flex-shrink-0 ml-4">
                                                <a href="<?= $result['result_url'] ?? '#' ?>"
                                                   class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                    View
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Enhanced Search Bar Styles */
#search-input:focus {
    outline: none;
}

#search-suggestions {
    animation: slideDown 0.2s ease-out;
    z-index: 9999 !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(229, 231, 235, 0.8);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#advanced-filters {
    animation: slideDown 0.3s ease-out;
}

.search-suggestion-item:hover {
    background-color: #f3f4f6;
    transform: translateX(4px);
}

/* Search container should have higher z-index than other sections */
.search-container-wrapper {
    position: relative;
    z-index: 100;
}
</style>

<script>
// Enhanced Search Functionality
function handleSearchInput(event) {
    const query = event.target.value.trim();

    // Handle Enter key
    if (event.key === 'Enter' && query.length > 0) {
        performSearch(query);
        return;
    }

    // Show suggestions for queries longer than 2 characters
    if (query.length >= 2) {
        fetchSearchSuggestions(query);
    } else {
        hideSearchSuggestions();
    }
}

function showSearchSuggestions() {
    const suggestions = document.getElementById('search-suggestions');
    suggestions.classList.remove('hidden');
}

function hideSearchSuggestions() {
    const suggestions = document.getElementById('search-suggestions');
    suggestions.classList.add('hidden');
}

function fetchSearchSuggestions(query) {
    fetch(`<?= url('/app/search/suggestions') ?>?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchSuggestions(data.suggestions || []);
        })
        .catch(error => {
            console.error('Error fetching suggestions:', error);
        });
}

function displaySearchSuggestions(suggestions) {
    const suggestionsList = document.getElementById('suggestions-list');

    if (suggestions.length === 0) {
        suggestionsList.innerHTML = '<div class="text-sm text-gray-400 py-2">No suggestions found</div>';
        return;
    }

    suggestionsList.innerHTML = suggestions.map(suggestion => {
        let icon = '📄'; // default document icon
        let typeLabel = suggestion.type;

        switch(suggestion.type) {
            case 'document':
                icon = '📄';
                typeLabel = 'Document';
                break;
            case 'bundle':
                icon = '📁';
                typeLabel = 'Bundle';
                break;
            case 'box':
                icon = '📦';
                typeLabel = 'Box';
                break;
            case 'warehouse':
                icon = '🏢';
                typeLabel = 'Warehouse';
                break;
            case 'category':
                icon = '🏷️';
                typeLabel = 'Category';
                break;
            case 'filename':
                icon = '📄';
                typeLabel = 'File';
                break;
        }

        return `
            <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg cursor-pointer search-suggestion-item" onclick="selectSuggestion('${suggestion.suggestion}')">
                <div class="flex items-center space-x-3">
                    <div class="w-6 h-6 flex items-center justify-center">
                        ${icon}
                    </div>
                    <span class="text-sm text-gray-900">${suggestion.suggestion}</span>
                </div>
                <span class="text-xs text-gray-400">${typeLabel}</span>
            </div>
        `;
    }).join('');

    showSearchSuggestions();
}

function selectSuggestion(suggestion) {
    document.getElementById('search-input').value = suggestion;
    hideSearchSuggestions();
    performSearch(suggestion);
}

function performSearch(query) {
    if (!query || query.trim().length === 0) {
        query = document.getElementById('search-input').value.trim();
    }

    if (!query) return;

    // Get active filters
    const searchTypes = Array.from(document.querySelectorAll('input[name="search_type"]:checked')).map(cb => cb.value);
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    const fileType = document.getElementById('file-type').value;

    // If no search types are selected, default to all types
    const defaultTypes = searchTypes.length > 0 ? searchTypes : ['documents', 'bundles', 'boxes'];

    // Update hidden form fields
    document.getElementById('hidden-query').value = query.trim();
    document.getElementById('hidden-types').value = defaultTypes.join(',');
    document.getElementById('hidden-from').value = dateFrom;
    document.getElementById('hidden-to').value = dateTo;
    document.getElementById('hidden-file-type').value = fileType;

    // Submit the form
    document.getElementById('search-form').submit();
}

function toggleAdvancedFilters() {
    const filtersPanel = document.getElementById('advanced-filters');
    filtersPanel.classList.toggle('hidden');
}

function clearFilters() {
    // Reset checkboxes to default
    document.querySelectorAll('input[name="search_type"]').forEach(cb => {
        cb.checked = ['documents', 'bundles', 'boxes'].includes(cb.value);
    });

    // Clear date inputs
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';
    document.getElementById('file-type').value = '';
}

function quickSearch(type) {
    let query = '';
    switch(type) {
        case 'recent':
            query = 'created:last-week';
            break;
        case 'physical':
            query = 'storage_type:physical';
            break;
        case 'online':
            query = 'storage_type:online';
            break;
        case 'pending':
            query = 'status:pending';
            break;
    }

    document.getElementById('search-input').value = query;
    performSearch(query);
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus search input
    document.getElementById('search-input').focus();

    // Handle form submission
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Enter' && event.target.id === 'search-input') {
            event.preventDefault();
            performSearch();
        }
    });
});

// Close suggestions when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('#search-input') && !event.target.closest('#search-suggestions')) {
        hideSearchSuggestions();
    }
});

// Close suggestions and hide filters with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        hideSearchSuggestions();

        // Hide advanced filters if open
        const filtersPanel = document.getElementById('advanced-filters');
        if (filtersPanel && !filtersPanel.classList.contains('hidden')) {
            filtersPanel.classList.add('hidden');
        }
    }
});
</script>

<?php
// Helper function to highlight search terms
function highlightSearchTerms($text, $query) {
    if (empty($query)) return $text;

    $terms = explode(' ', trim($query));
    foreach ($terms as $term) {
        if (!empty($term)) {
            $text = preg_replace('/(' . preg_quote($term, '/') . ')/i', '<mark class="bg-yellow-200 px-1 rounded">$1</mark>', $text);
        }
    }
    return $text;
}

$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>