<?php
$pageTitle = $title ?? 'Client Portal Dashboard';
include 'src/views/layouts/client-header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    
    <!-- Header -->
    <div class="bg-white/90 backdrop-blur-sm border-b border-white/30 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Welcome, <?= e($user['first_name']) ?></h1>
                    <p class="text-gray-600 mt-1">Client Portal - <?= e($clientCompany['client_company_name']) ?></p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Service Provider</p>
                        <p class="font-medium text-gray-900"><?= e($clientCompany['service_provider_name']) ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Total Documents -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Documents</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total_documents']) ?></p>
                    </div>
                </div>
            </div>

            <!-- Total Bundles -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Bundles</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total_bundles']) ?></p>
                    </div>
                </div>
            </div>

            <!-- Storage Boxes -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Storage Boxes</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total_boxes']) ?></p>
                    </div>
                </div>
            </div>

            <!-- Pending Requests -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Pending Requests</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['pending_requests']) ?></p>
                    </div>
                </div>
            </div>

        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            
            <!-- Quick Actions Card -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="<?= url('/client/documents') ?>" 
                       class="flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors group">
                        <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <span class="text-blue-700 group-hover:text-blue-800 font-medium">Search Documents</span>
                    </a>
                    
                    <a href="<?= url('/client/create-request') ?>" 
                       class="flex items-center p-3 bg-green-50 hover:bg-green-100 rounded-xl transition-colors group">
                        <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span class="text-green-700 group-hover:text-green-800 font-medium">New Request</span>
                    </a>
                    
                    <a href="<?= url('/client/requests') ?>" 
                       class="flex items-center p-3 bg-purple-50 hover:bg-purple-100 rounded-xl transition-colors group">
                        <svg class="w-5 h-5 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <span class="text-purple-700 group-hover:text-purple-800 font-medium">View Requests</span>
                    </a>
                </div>
            </div>

            <!-- Recent Documents -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Documents</h3>
                <div class="space-y-3">
                    <?php if (!empty($recentDocuments)): ?>
                        <?php foreach (array_slice($recentDocuments, 0, 5) as $document): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        <?= e($document['file_name']) ?>
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        <?= e($document['bundle_name']) ?>
                                    </p>
                                </div>
                                <a href="<?= url('/client/documents/' . $document['id']) ?>" 
                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    View
                                </a>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-gray-500 text-sm">No recent documents</p>
                    <?php endif; ?>
                </div>
                <div class="mt-4">
                    <a href="<?= url('/client/documents') ?>" 
                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View all documents →
                    </a>
                </div>
            </div>

            <!-- Pending Requests -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Pending Requests</h3>
                <div class="space-y-3">
                    <?php if (!empty($pendingRequests)): ?>
                        <?php foreach ($pendingRequests as $request): ?>
                            <div class="p-3 bg-orange-50 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900">
                                            <?= ucfirst($request['request_type']) ?> Request
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?= e($request['request_number']) ?>
                                        </p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        <?= ucfirst($request['status']) ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-gray-500 text-sm">No pending requests</p>
                    <?php endif; ?>
                </div>
                <div class="mt-4">
                    <a href="<?= url('/client/requests') ?>" 
                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View all requests →
                    </a>
                </div>
            </div>

        </div>

        <!-- Alerts Section -->
        <?php if (!empty($alerts)): ?>
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Important Notices</h3>
            <div class="space-y-3">
                <?php foreach ($alerts as $alert): ?>
                    <div class="flex items-start p-4 bg-<?= $alert['severity'] === 'critical' ? 'red' : ($alert['severity'] === 'warning' ? 'yellow' : 'blue') ?>-50 rounded-xl">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-<?= $alert['severity'] === 'critical' ? 'red' : ($alert['severity'] === 'warning' ? 'yellow' : 'blue') ?>-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-3 flex-1">
                            <h4 class="text-sm font-medium text-gray-900"><?= e($alert['title']) ?></h4>
                            <p class="text-sm text-gray-600 mt-1"><?= e($alert['message']) ?></p>
                            <?php if ($alert['due_date']): ?>
                                <p class="text-xs text-gray-500 mt-1">Due: <?= date('M j, Y', strtotime($alert['due_date'])) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

    </div>
</div>

<?php include 'src/views/layouts/client-footer.php'; ?>
