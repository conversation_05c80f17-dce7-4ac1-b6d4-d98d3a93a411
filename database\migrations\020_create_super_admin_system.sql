-- Super Admin System Implementation
-- Creates system settings table and enhances companies table for subscription management

-- Create system_settings table for global system configuration
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_settings_key (setting_key)
);

-- Add subscription management fields to companies table if they don't exist
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS monthly_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Monthly subscription fee',
ADD COLUMN IF NOT EXISTS contract_start_date DATE NULL COMMENT 'Contract start date',
ADD COLUMN IF NOT EXISTS contract_end_date DATE NULL COMMENT 'Contract end date',
ADD COLUMN IF NOT EXISTS billing_contact_email VARCHAR(255) NULL COMMENT 'Billing contact email',
ADD COLUMN IF NOT EXISTS billing_address TEXT NULL COMMENT 'Billing address',
ADD COLUMN IF NOT EXISTS payment_status ENUM('active', 'overdue', 'suspended', 'cancelled') DEFAULT 'active' COMMENT 'Payment status',
ADD COLUMN IF NOT EXISTS last_payment_date DATE NULL COMMENT 'Last payment date',
ADD COLUMN IF NOT EXISTS next_payment_date DATE NULL COMMENT 'Next payment due date',
ADD COLUMN IF NOT EXISTS subscription_notes TEXT NULL COMMENT 'Subscription notes and comments';

-- Add indexes for subscription management
CREATE INDEX IF NOT EXISTS idx_companies_payment_status ON companies(payment_status);
CREATE INDEX IF NOT EXISTS idx_companies_next_payment ON companies(next_payment_date);
CREATE INDEX IF NOT EXISTS idx_companies_contract_end ON companies(contract_end_date);

-- Insert default system settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('system_name', 'Document Management System', 'System name displayed in interface'),
('system_email', '<EMAIL>', 'System administrator email'),
('default_storage_limit', '5368709120', 'Default storage limit in bytes (5GB)'),
('max_file_size', '104857600', 'Maximum file upload size in bytes (100MB)'),
('allowed_file_types', 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif', 'Allowed file extensions'),
('maintenance_mode', '0', 'System maintenance mode (0=off, 1=on)'),
('user_registration', '1', 'Allow user registration (0=off, 1=on)'),
('email_verification', '0', 'Require email verification (0=off, 1=on)'),
('backup_enabled', '1', 'Enable automatic backups (0=off, 1=on)'),
('backup_frequency', 'daily', 'Backup frequency (daily, weekly, monthly)'),
('session_timeout', '3600', 'Session timeout in seconds'),
('password_min_length', '8', 'Minimum password length'),
('password_require_special', '0', 'Require special characters in password (0=off, 1=on)'),
('two_factor_auth', '0', 'Enable two-factor authentication (0=off, 1=on)'),
('api_enabled', '1', 'Enable API access (0=off, 1=on)'),
('api_rate_limit', '1000', 'API rate limit per hour'),
('storage_cleanup_enabled', '1', 'Enable automatic storage cleanup (0=off, 1=on)'),
('storage_cleanup_days', '30', 'Days to keep deleted files before permanent removal'),
('audit_log_retention', '365', 'Days to keep audit logs'),
('notification_email', '<EMAIL>', 'Email for system notifications'),
('smtp_host', '', 'SMTP server host'),
('smtp_port', '587', 'SMTP server port'),
('smtp_username', '', 'SMTP username'),
('smtp_password', '', 'SMTP password'),
('smtp_encryption', 'tls', 'SMTP encryption (tls, ssl, none)');

-- Create subscription_history table for tracking subscription changes
CREATE TABLE IF NOT EXISTS subscription_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    old_plan VARCHAR(50),
    new_plan VARCHAR(50) NOT NULL,
    old_storage_limit BIGINT,
    new_storage_limit BIGINT NOT NULL,
    old_monthly_fee DECIMAL(10,2),
    new_monthly_fee DECIMAL(10,2) NOT NULL,
    change_reason TEXT,
    effective_date DATE NOT NULL,
    changed_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_subscription_history_company (company_id),
    INDEX idx_subscription_history_date (effective_date),
    INDEX idx_subscription_history_changed_by (changed_by)
);

-- Create payment_history table for tracking payments
CREATE TABLE IF NOT EXISTS payment_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'manual',
    payment_reference VARCHAR(100),
    payment_date DATE NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed',
    notes TEXT,
    processed_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_payment_history_company (company_id),
    INDEX idx_payment_history_date (payment_date),
    INDEX idx_payment_history_status (status),
    INDEX idx_payment_history_period (period_start, period_end)
);

-- Create system_notifications table for admin notifications
CREATE TABLE IF NOT EXISTS system_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    notification_type ENUM('payment_overdue', 'storage_limit', 'contract_expiry', 'system_alert', 'maintenance') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    severity ENUM('info', 'warning', 'critical') DEFAULT 'info',
    target_type ENUM('system', 'company', 'user') DEFAULT 'system',
    target_id INT NULL,
    status ENUM('pending', 'sent', 'read', 'dismissed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    
    INDEX idx_system_notifications_type (notification_type),
    INDEX idx_system_notifications_status (status),
    INDEX idx_system_notifications_target (target_type, target_id),
    INDEX idx_system_notifications_created (created_at)
);

-- Update existing companies with subscription data
UPDATE companies SET 
    monthly_fee = CASE subscription_plan
        WHEN 'basic' THEN 99.00
        WHEN 'premium' THEN 299.00
        WHEN 'enterprise' THEN 999.00
        ELSE 99.00
    END,
    contract_start_date = DATE_SUB(CURDATE(), INTERVAL 6 MONTH),
    contract_end_date = DATE_ADD(CURDATE(), INTERVAL 6 MONTH),
    billing_contact_email = email,
    payment_status = 'active',
    last_payment_date = DATE_SUB(CURDATE(), INTERVAL 1 MONTH),
    next_payment_date = DATE_ADD(CURDATE(), INTERVAL 1 MONTH)
WHERE monthly_fee IS NULL OR monthly_fee = 0;

-- Insert sample subscription history
INSERT INTO subscription_history (company_id, old_plan, new_plan, old_storage_limit, new_storage_limit, old_monthly_fee, new_monthly_fee, change_reason, effective_date, changed_by)
SELECT 
    c.id,
    'basic',
    c.subscription_plan,
    **********, -- 1GB
    c.storage_limit,
    99.00,
    c.monthly_fee,
    'Initial subscription setup',
    c.created_at,
    1
FROM companies c
WHERE c.id <= 3; -- Only for first 3 companies

-- Insert sample payment history
INSERT INTO payment_history (company_id, amount, payment_method, payment_reference, payment_date, period_start, period_end, status, processed_by)
SELECT 
    c.id,
    c.monthly_fee,
    'bank_transfer',
    CONCAT('PAY-', YEAR(CURDATE()), '-', LPAD(c.id, 4, '0')),
    c.last_payment_date,
    DATE_SUB(c.last_payment_date, INTERVAL 1 MONTH),
    c.last_payment_date,
    'completed',
    1
FROM companies c
WHERE c.last_payment_date IS NOT NULL;

-- Insert sample system notifications
INSERT INTO system_notifications (notification_type, title, message, severity, target_type, target_id, status)
VALUES 
('storage_limit', 'Storage Limit Warning', 'Multiple companies are approaching their storage limits', 'warning', 'system', NULL, 'pending'),
('contract_expiry', 'Contract Expiry Alert', 'Several contracts are expiring within 30 days', 'warning', 'system', NULL, 'pending'),
('payment_overdue', 'Payment Overdue', 'Some companies have overdue payments', 'critical', 'system', NULL, 'pending');

-- Create view for company subscription overview
CREATE OR REPLACE VIEW company_subscription_overview AS
SELECT 
    c.id,
    c.name,
    c.subscription_plan,
    c.storage_limit,
    c.storage_used,
    (c.storage_used / c.storage_limit * 100) as storage_percentage,
    c.monthly_fee,
    c.payment_status,
    c.contract_start_date,
    c.contract_end_date,
    c.next_payment_date,
    DATEDIFF(c.contract_end_date, CURDATE()) as days_until_expiry,
    DATEDIFF(c.next_payment_date, CURDATE()) as days_until_payment,
    COUNT(DISTINCT u.id) as user_count,
    COUNT(DISTINCT d.id) as document_count,
    COUNT(DISTINCT b.id) as bundle_count,
    COUNT(DISTINCT box.id) as box_count
FROM companies c
LEFT JOIN users u ON c.id = u.company_id AND u.status = 'active'
LEFT JOIN documents d ON c.id = d.company_id AND d.status != 'deleted'
LEFT JOIN bundles b ON c.id = b.company_id AND b.status = 'active'
LEFT JOIN boxes box ON c.id = box.company_id
WHERE c.status = 'active'
GROUP BY c.id;
