<?php

namespace App\Services;

/**
 * Intake Service
 * 
 * Manages the document intake workflow including:
 * - Intake request processing
 * - Workflow automation
 * - Validation rules
 * - Integration with bundle and box services
 */
class IntakeService extends BaseService
{
    /**
     * Create a new intake request with validation
     */
    public function createIntakeRequest($data)
    {
        $this->validateRequired($data, ['client_name', 'source', 'document_type', 'description']);
        $data = $this->sanitizeData($data);
        
        $this->validateCompanyAccess($this->user['company_id']);

        try {
            $this->db->beginTransaction();

            // Generate reference number
            $referenceNumber = $this->generateReferenceNumber('INT', 'document_intake');

            // Create intake record
            $intakeId = $this->db->execute(
                "INSERT INTO document_intake (
                    company_id, reference_number, client_name, client_id, source, 
                    document_type, description, priority, expected_count, actual_count,
                    sensitivity_level, department, notes, status, created_by, 
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $referenceNumber,
                    $data['client_name'],
                    $data['client_id'] ?? null,
                    $data['source'],
                    $data['document_type'],
                    $data['description'],
                    $data['priority'] ?? 'medium',
                    $data['expected_count'] ?? 0,
                    $data['actual_count'] ?? 0,
                    $data['sensitivity_level'] ?? 'internal',
                    $data['department'] ?? null,
                    $data['notes'] ?? null,
                    $this->user['id']
                ]
            );

            // Auto-create bundle if specified
            if (!empty($data['auto_create_bundle'])) {
                $bundleId = $this->createBundleForIntake($intakeId, $data);
                
                // Link intake to bundle
                $this->db->execute(
                    "UPDATE document_intake SET bundle_id = ? WHERE id = ?",
                    [$bundleId, $intakeId]
                );
            }

            // Send notifications if required
            if (!empty($data['notify_client'])) {
                $this->notifyClientOfIntake($intakeId, $data['client_id'] ?? null);
            }

            $this->logActivity('create', 'intake', $intakeId, "Created intake request: {$referenceNumber}");

            $this->db->commit();

            return [
                'id' => $intakeId,
                'reference_number' => $referenceNumber,
                'status' => 'pending',
                'bundle_id' => $bundleId ?? null
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to create intake request: ' . $e->getMessage());
        }
    }

    /**
     * Process intake through workflow stages
     */
    public function processIntakeWorkflow($intakeId, $action, $data = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $intake = $this->getIntakeById($intakeId);
        if (!$intake) {
            throw new \Exception('Intake request not found');
        }

        try {
            $this->db->beginTransaction();

            switch ($action) {
                case 'approve':
                    $this->approveIntake($intakeId, $data);
                    break;
                case 'reject':
                    $this->rejectIntake($intakeId, $data);
                    break;
                case 'process':
                    $this->processIntake($intakeId, $data);
                    break;
                case 'complete':
                    $this->completeIntake($intakeId, $data);
                    break;
                case 'archive':
                    $this->archiveIntake($intakeId, $data);
                    break;
                default:
                    throw new \Exception('Invalid workflow action: ' . $action);
            }

            $this->db->commit();

            return ['success' => true, 'action' => $action];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to process intake workflow: ' . $e->getMessage());
        }
    }

    /**
     * Validate intake data against business rules
     */
    public function validateIntakeData($data)
    {
        $errors = [];

        // Required field validation
        $requiredFields = ['client_name', 'source', 'document_type', 'description'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[] = "Field '{$field}' is required";
            }
        }

        // Business rule validation
        if (!empty($data['expected_count']) && $data['expected_count'] < 0) {
            $errors[] = 'Expected count cannot be negative';
        }

        if (!empty($data['actual_count']) && $data['actual_count'] < 0) {
            $errors[] = 'Actual count cannot be negative';
        }

        // Validate priority
        $validPriorities = ['low', 'medium', 'high', 'urgent'];
        if (!empty($data['priority']) && !in_array($data['priority'], $validPriorities)) {
            $errors[] = 'Invalid priority level';
        }

        // Validate sensitivity level
        $validSensitivity = ['public', 'internal', 'confidential', 'restricted'];
        if (!empty($data['sensitivity_level']) && !in_array($data['sensitivity_level'], $validSensitivity)) {
            $errors[] = 'Invalid sensitivity level';
        }

        // Validate document type
        $validDocTypes = ['contract', 'invoice', 'report', 'image', 'video', 'audio', 'other'];
        if (!empty($data['document_type']) && !in_array($data['document_type'], $validDocTypes)) {
            $errors[] = 'Invalid document type';
        }

        return $errors;
    }

    /**
     * Get intake statistics and metrics
     */
    public function getIntakeMetrics($dateRange = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $where = ["company_id = ?"];
        $params = [$this->user['company_id']];

        if (!empty($dateRange['from'])) {
            $where[] = "DATE(created_at) >= ?";
            $params[] = $dateRange['from'];
        }

        if (!empty($dateRange['to'])) {
            $where[] = "DATE(created_at) <= ?";
            $params[] = $dateRange['to'];
        }

        $whereClause = implode(' AND ', $where);

        // Status distribution
        $statusStats = $this->db->fetchAll(
            "SELECT status, COUNT(*) as count
             FROM document_intake
             WHERE {$whereClause}
             GROUP BY status",
            $params
        );

        // Priority distribution
        $priorityStats = $this->db->fetchAll(
            "SELECT priority, COUNT(*) as count
             FROM document_intake
             WHERE {$whereClause}
             GROUP BY priority",
            $params
        );

        // Document type distribution
        $typeStats = $this->db->fetchAll(
            "SELECT document_type, COUNT(*) as count
             FROM document_intake
             WHERE {$whereClause}
             GROUP BY document_type",
            $params
        );

        // Processing time metrics
        $processingTime = $this->db->fetch(
            "SELECT 
                AVG(DATEDIFF(COALESCE(completed_at, NOW()), created_at)) as avg_processing_days,
                MIN(DATEDIFF(COALESCE(completed_at, NOW()), created_at)) as min_processing_days,
                MAX(DATEDIFF(COALESCE(completed_at, NOW()), created_at)) as max_processing_days
             FROM document_intake
             WHERE {$whereClause}",
            $params
        );

        // Volume by date
        $volumeByDate = $this->db->fetchAll(
            "SELECT DATE(created_at) as date, COUNT(*) as count
             FROM document_intake
             WHERE {$whereClause}
             GROUP BY DATE(created_at)
             ORDER BY date DESC
             LIMIT 30",
            $params
        );

        return [
            'status_distribution' => $statusStats,
            'priority_distribution' => $priorityStats,
            'type_distribution' => $typeStats,
            'processing_time' => $processingTime,
            'volume_by_date' => $volumeByDate
        ];
    }

    /**
     * Search intake requests with filters
     */
    public function searchIntakeRequests($filters = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $where = ["di.company_id = ?"];
        $params = [$this->user['company_id']];

        // Apply filters
        if (!empty($filters['search'])) {
            $where[] = "(di.reference_number LIKE ? OR di.client_name LIKE ? OR di.description LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($filters['status'])) {
            $where[] = "di.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['priority'])) {
            $where[] = "di.priority = ?";
            $params[] = $filters['priority'];
        }

        if (!empty($filters['document_type'])) {
            $where[] = "di.document_type = ?";
            $params[] = $filters['document_type'];
        }

        if (!empty($filters['client_name'])) {
            $where[] = "di.client_name LIKE ?";
            $params[] = '%' . $filters['client_name'] . '%';
        }

        if (!empty($filters['date_from'])) {
            $where[] = "DATE(di.created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where[] = "DATE(di.created_at) <= ?";
            $params[] = $filters['date_to'];
        }

        $whereClause = implode(' AND ', $where);
        $orderBy = $filters['sort'] ?? 'di.created_at DESC';

        $sql = "SELECT di.*, u.first_name, u.last_name,
                       b.name as bundle_name, b.reference_number as bundle_reference
                FROM document_intake di
                LEFT JOIN users u ON di.created_by = u.id
                LEFT JOIN bundles b ON di.bundle_id = b.id
                WHERE {$whereClause}
                ORDER BY {$orderBy}";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get intake by ID
     */
    private function getIntakeById($intakeId)
    {
        return $this->db->fetch(
            "SELECT * FROM document_intake WHERE id = ? AND company_id = ?",
            [$intakeId, $this->user['company_id']]
        );
    }

    /**
     * Create bundle for intake
     */
    private function createBundleForIntake($intakeId, $intakeData)
    {
        $bundleName = "Bundle for " . $intakeData['client_name'] . " - " . $intakeData['document_type'];
        $referenceNumber = $this->generateReferenceNumber('BDL', 'bundles');

        $bundleId = $this->db->execute(
            "INSERT INTO bundles (
                company_id, name, description, reference_number, bundle_type,
                category, priority, status, created_by, created_at, updated_at
            ) VALUES (?, ?, ?, ?, 'intake', ?, ?, 'open', ?, NOW(), NOW())",
            [
                $this->user['company_id'],
                $bundleName,
                "Auto-created bundle for intake: " . $intakeData['description'],
                $referenceNumber,
                $intakeData['document_type'],
                $intakeData['priority'] ?? 'medium',
                $this->user['id']
            ]
        );

        $this->logActivity('create', 'bundle', $bundleId, "Auto-created bundle for intake {$intakeId}");

        return $bundleId;
    }

    /**
     * Approve intake request
     */
    private function approveIntake($intakeId, $data)
    {
        $this->db->execute(
            "UPDATE document_intake SET
             status = 'approved',
             approved_by = ?,
             approved_at = NOW(),
             approval_notes = ?,
             updated_at = NOW()
             WHERE id = ?",
            [$this->user['id'], $data['notes'] ?? null, $intakeId]
        );

        // Auto-create bundle if not exists and requested
        if (!empty($data['create_bundle'])) {
            $intake = $this->getIntakeById($intakeId);
            if (!$intake['bundle_id']) {
                $bundleId = $this->createBundleForIntake($intakeId, $intake);
                $this->db->execute(
                    "UPDATE document_intake SET bundle_id = ? WHERE id = ?",
                    [$bundleId, $intakeId]
                );
            }
        }

        $this->logActivity('approve', 'intake', $intakeId, "Approved intake request");
    }

    /**
     * Reject intake request
     */
    private function rejectIntake($intakeId, $data)
    {
        $this->db->execute(
            "UPDATE document_intake SET
             status = 'rejected',
             rejected_by = ?,
             rejected_at = NOW(),
             rejection_reason = ?,
             updated_at = NOW()
             WHERE id = ?",
            [$this->user['id'], $data['reason'] ?? 'Not specified', $intakeId]
        );

        $this->logActivity('reject', 'intake', $intakeId, "Rejected intake request");
    }

    /**
     * Process intake request
     */
    private function processIntake($intakeId, $data)
    {
        $this->db->execute(
            "UPDATE document_intake SET
             status = 'processing',
             processing_started_at = NOW(),
             processing_notes = ?,
             updated_at = NOW()
             WHERE id = ?",
            [$data['notes'] ?? null, $intakeId]
        );

        $this->logActivity('process', 'intake', $intakeId, "Started processing intake request");
    }

    /**
     * Complete intake request
     */
    private function completeIntake($intakeId, $data)
    {
        $this->db->execute(
            "UPDATE document_intake SET
             status = 'completed',
             completed_at = NOW(),
             completion_notes = ?,
             actual_count = ?,
             updated_at = NOW()
             WHERE id = ?",
            [
                $data['notes'] ?? null,
                $data['actual_count'] ?? 0,
                $intakeId
            ]
        );

        // Update bundle status if exists
        $intake = $this->getIntakeById($intakeId);
        if ($intake['bundle_id']) {
            $this->db->execute(
                "UPDATE bundles SET status = 'closed', closed_at = NOW() WHERE id = ?",
                [$intake['bundle_id']]
            );
        }

        $this->logActivity('complete', 'intake', $intakeId, "Completed intake request");
    }

    /**
     * Archive intake request
     */
    private function archiveIntake($intakeId, $data)
    {
        $this->db->execute(
            "UPDATE document_intake SET
             status = 'archived',
             archived_at = NOW(),
             archive_reason = ?,
             updated_at = NOW()
             WHERE id = ?",
            [$data['reason'] ?? 'Manual archive', $intakeId]
        );

        $this->logActivity('archive', 'intake', $intakeId, "Archived intake request");
    }

    /**
     * Notify client of intake creation
     */
    private function notifyClientOfIntake($intakeId, $clientId)
    {
        $intake = $this->getIntakeById($intakeId);

        $this->sendNotification(
            'intake_created',
            $clientId ?? $intake['client_name'],
            'New Intake Request Created',
            "Your document intake request {$intake['reference_number']} has been created and is being processed."
        );
    }
}
