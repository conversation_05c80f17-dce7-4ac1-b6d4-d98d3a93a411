<?php
/**
 * Billing Module Migration Script
 * 
 * This script sets up the billing tables and initial data
 */

require_once __DIR__ . '/src/autoload.php';
require_once __DIR__ . '/src/config/database.php';

use App\Core\Database;

try {
    echo "Starting Billing Module Migration...\n";
    
    $db = Database::getInstance();
    
    // Read and execute the billing migration
    $migrationFile = __DIR__ . '/database/migrations/026_create_billing_tables.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: {$migrationFile}");
    }
    
    $sql = file_get_contents($migrationFile);
    
    // Split the SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "Executing " . count($statements) . " SQL statements...\n";
    
    foreach ($statements as $index => $statement) {
        try {
            if (trim($statement)) {
                $db->execute($statement);
                echo "✓ Statement " . ($index + 1) . " executed successfully\n";
            }
        } catch (Exception $e) {
            // Check if it's a "table already exists" error - we can ignore those
            if (strpos($e->getMessage(), 'already exists') !== false) {
                echo "⚠ Statement " . ($index + 1) . " skipped (already exists)\n";
            } else {
                throw new Exception("Error in statement " . ($index + 1) . ": " . $e->getMessage());
            }
        }
    }
    
    // Verify tables were created
    echo "\nVerifying billing tables...\n";
    
    $requiredTables = [
        'billing_rates',
        'billing_events', 
        'billing_invoices',
        'billing_payments'
    ];
    
    foreach ($requiredTables as $table) {
        $exists = $db->fetch("SHOW TABLES LIKE '{$table}'");
        if ($exists) {
            echo "✓ Table '{$table}' exists\n";
        } else {
            throw new Exception("Table '{$table}' was not created");
        }
    }
    
    // Check if billing rates were inserted
    $rateCount = $db->fetch("SELECT COUNT(*) as count FROM billing_rates");
    echo "✓ Billing rates table has {$rateCount['count']} service rates\n";
    
    // Display some sample rates
    echo "\nSample billing rates:\n";
    $sampleRates = $db->fetchAll(
        "SELECT service_code, service_name, rate, unit, category 
         FROM billing_rates 
         WHERE is_active = 1 
         ORDER BY category, service_name 
         LIMIT 10"
    );
    
    foreach ($sampleRates as $rate) {
        echo sprintf(
            "  - %s: $%s per %s (%s)\n",
            $rate['service_name'],
            number_format($rate['rate'], 2),
            $rate['unit'],
            $rate['category']
        );
    }
    
    echo "\n✅ Billing Module Migration completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Access the billing dashboard at: /app/billing\n";
    echo "2. Review and adjust service rates if needed\n";
    echo "3. Start logging billing events in your application\n";
    echo "4. Generate your first invoice at month-end\n";
    
} catch (Exception $e) {
    echo "\n❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Please check your database connection and try again.\n";
    exit(1);
}
