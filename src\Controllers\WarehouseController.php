<?php

namespace App\Controllers;

/**
 * Warehouse Controller - Physical Storage Facility Management
 * 
 * Manages warehouse facilities for the document storage service provider
 */
class WarehouseController extends BaseController
{
    /**
     * Display warehouses list
     */
    public function index()
    {
        $this->requireAuth();

        try {
            // Get warehouses with statistics following INTAKE → BUNDLE → BOX → STORAGE workflow
            // Filter by company_id to ensure users only see their company's warehouses
            $warehouses = $this->db->fetchAll(
                "SELECT w.*,
                        COUNT(DISTINCT CASE WHEN b.status != 'archived' AND b.name NOT LIKE '[DELETED]%' THEN b.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') AND b.name NOT LIKE '[DELETED]%' THEN b.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN boxes b ON w.id = b.warehouse_id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id AND bun.name NOT LIKE '[DELETED]%'
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.company_id = ? AND w.status = 'active' AND w.name NOT LIKE '[DELETED]%'
                 GROUP BY w.id
                 ORDER BY w.name",
                [$this->user['company_id']]
            );

            $this->view('warehouses/index', [
                'title' => 'Warehouse Management',
                'warehouses' => $warehouses
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading warehouses: ' . $e->getMessage(), 'error');
            $this->redirect('/app/dashboard');
        }
    }

    /**
     * Show create warehouse form
     */
    public function create()
    {
        $this->requireAuth();
        
        $this->view('warehouses/create', [
            'title' => 'Create New Warehouse'
        ]);
    }

    /**
     * Store new warehouse
     */
    public function store()
    {
        $this->requireAuth();

        try {
            // Validate input
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'address' => 'required|max:500',
                'city' => 'required|max:100',
                'state' => 'required|max:100',
                'zip_code' => 'required|max:20',
                'country' => 'required|max:100',
                'phone' => 'max:20',
                'email' => 'email|max:255',
                'capacity' => 'integer|min:1',
                'description' => 'max:1000'
            ]);

            // Create warehouse
            $warehouseId = $this->db->execute(
                "INSERT INTO warehouses (
                    company_id, name, address, city, state, zip_code, country,
                    phone, email, capacity, description, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $data['name'],
                    $data['address'],
                    $data['city'],
                    $data['state'],
                    $data['zip_code'],
                    $data['country'],
                    $data['phone'] ?? null,
                    $data['email'] ?? null,
                    $data['capacity'] ?? 1000,
                    $data['description'] ?? null,
                    $this->user['id']
                ]
            );

            // Log activity
            $this->logActivity('create', 'warehouse', $warehouseId, "Created warehouse: {$data['name']}");

            $this->setFlashMessage('Warehouse created successfully', 'success');
            $this->redirect('/app/warehouses');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create warehouse: ' . $e->getMessage(), 'error');
            $this->redirect('/app/warehouses/create');
        }
    }

    /**
     * Store new warehouse via AJAX (for modal creation)
     */
    public function storeAjax()
    {
        // Debug logging
        error_log("WarehouseController::storeAjax called");
        error_log("POST data: " . print_r($_POST, true));

        $this->requireAuth();

        try {
            // Validate input with required fields for comprehensive warehouse creation
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'address' => 'required|max:500',
                'city' => 'required|max:100',
                'state' => 'required|max:100',
                'zip_code' => 'required|max:20',
                'country' => 'max:100',
                'phone' => 'max:20',
                'email' => 'email|max:255',
                'capacity' => 'integer|min:1',
                'description' => 'max:1000'
            ]);

            // Set default values for optional fields
            $country = $data['country'] ?? 'USA';

            // Generate unique warehouse code
            $warehouseCode = $this->generateWarehouseCode($data['name']);

            // Create warehouse
            $warehouseId = $this->db->execute(
                "INSERT INTO warehouses (
                    company_id, name, code, address, city, state, zip_code, country,
                    phone, email, capacity, description, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $data['name'],
                    $warehouseCode,
                    $data['address'],
                    $data['city'],
                    $data['state'],
                    $data['zip_code'],
                    $country,
                    $data['phone'] ?? null,
                    $data['email'] ?? null,
                    $data['capacity'] ?? 1000,
                    $data['description'] ?? null,
                    $this->user['id']
                ]
            );

            // Get the created warehouse data
            $warehouse = $this->db->fetch(
                "SELECT id, name, city, state FROM warehouses WHERE id = ?",
                [$warehouseId]
            );

            // Log activity
            $this->logActivity('create', 'warehouse', $warehouseId, "Created warehouse: {$data['name']}");

            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Warehouse created successfully',
                'warehouse' => $warehouse
            ]);

        } catch (\Exception $e) {
            // Log the error for debugging
            error_log("Warehouse creation error: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Failed to create warehouse: ' . $e->getMessage(),
                'debug' => [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ]);
        }
        exit;
    }

    /**
     * Show warehouse details
     */
    public function show($id)
    {
        $this->requireAuth();

        try {
            $warehouse = $this->getWarehouseById($id);
            if (!$warehouse) {
                $this->setFlashMessage('Warehouse not found', 'error');
                $this->redirect('/app/warehouses');
                return;
            }

            // Get storage locations in this warehouse
            $storageLocations = $this->getWarehouseStorageLocations($id);

            // Get warehouse statistics
            $stats = $this->getWarehouseStats($id);

            // Get detailed hierarchy for debugging (INTAKE → BUNDLE → BOX → STORAGE)
            $hierarchy = $this->getWarehouseHierarchy($id);

            // Add debug information if requested
            $debug = isset($_GET['debug']) ? [
                'total_boxes_in_warehouse' => count(array_filter($storageLocations, function($item) { return $item['item_type'] === 'box'; })),
                'total_storage_locations' => count(array_filter($storageLocations, function($item) { return $item['item_type'] === 'storage_location'; })),
                'hierarchy_entries' => count($hierarchy),
                'raw_hierarchy' => $hierarchy
            ] : null;

            $this->view('warehouses/show', [
                'title' => $warehouse['name'],
                'warehouse' => $warehouse,
                'storageLocations' => $storageLocations,
                'stats' => $stats,
                'hierarchy' => $hierarchy,
                'debug' => $debug
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading warehouse: ' . $e->getMessage(), 'error');
            $this->redirect('/app/warehouses');
        }
    }

    /**
     * Show edit warehouse form
     */
    public function edit($id)
    {
        $this->requireAuth();
        
        try {
            $warehouse = $this->getWarehouseById($id);
            if (!$warehouse) {
                $this->setFlashMessage('Warehouse not found', 'error');
                $this->redirect('/app/warehouses');
                return;
            }

            $this->view('warehouses/edit', [
                'title' => 'Edit Warehouse',
                'warehouse' => $warehouse
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading warehouse: ' . $e->getMessage(), 'error');
            $this->redirect('/app/warehouses');
        }
    }

    /**
     * Update warehouse
     */
    public function update($id)
    {
        $this->requireAuth();
        
        try {
            $warehouse = $this->getWarehouseById($id);
            if (!$warehouse) {
                throw new \Exception('Warehouse not found');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'address' => 'required|max:500',
                'city' => 'required|max:100',
                'state' => 'required|max:100',
                'zip_code' => 'required|max:20',
                'country' => 'required|max:100',
                'phone' => 'max:20',
                'email' => 'email|max:255',
                'capacity' => 'integer|min:1',
                'description' => 'max:1000'
            ]);

            // Update warehouse
            $this->db->execute(
                "UPDATE warehouses SET 
                 name = ?, address = ?, city = ?, state = ?, zip_code = ?, country = ?,
                 phone = ?, email = ?, capacity = ?, description = ?, updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [
                    $data['name'],
                    $data['address'],
                    $data['city'],
                    $data['state'],
                    $data['zip_code'],
                    $data['country'],
                    $data['phone'] ?? null,
                    $data['email'] ?? null,
                    $data['capacity'] ?? 1000,
                    $data['description'] ?? null,
                    $id,
                    $this->user['company_id']
                ]
            );

            // Log activity
            $this->logActivity('update', 'warehouse', $id, "Updated warehouse: {$data['name']}");

            $this->setFlashMessage('Warehouse updated successfully', 'success');
            $this->redirect("/app/warehouses/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Update failed: ' . $e->getMessage(), 'error');
            $this->redirect("/app/warehouses/{$id}/edit");
        }
    }

    /**
     * Delete warehouse (soft delete)
     */
    public function delete($id)
    {
        $this->requireAuth();

        try {
            // First, verify the warehouse exists and belongs to the user's company
            $warehouse = $this->db->fetch(
                "SELECT id, name FROM warehouses WHERE id = ? AND company_id = ? AND status = 'active'",
                [$id, $this->user['company_id']]
            );

            if (!$warehouse) {
                $this->setFlashMessage('Warehouse not found or access denied', 'error');
                $this->redirect('/app/warehouses');
                return;
            }

            // Check if warehouse has any boxes or documents
            $hasContent = $this->db->fetch(
                "SELECT
                    COUNT(DISTINCT CASE WHEN b.status != 'archived' AND b.name NOT LIKE '[DELETED]%' THEN b.id END) as box_count,
                    COUNT(DISTINCT d.id) as document_count
                 FROM warehouses w
                 LEFT JOIN boxes b ON w.id = b.warehouse_id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id AND bun.name NOT LIKE '[DELETED]%'
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 WHERE w.id = ?",
                [$id]
            );

            // Prevent deletion if warehouse contains data
            if ($hasContent['box_count'] > 0 || $hasContent['document_count'] > 0) {
                $this->setFlashMessage(
                    'Cannot delete warehouse: It contains ' .
                    $hasContent['box_count'] . ' boxes and ' .
                    $hasContent['document_count'] . ' documents. Please move or delete the contents first.',
                    'error'
                );
                $this->redirect('/app/warehouses');
                return;
            }

            // Perform soft delete by marking as deleted
            $this->db->execute(
                "UPDATE warehouses SET
                 name = CONCAT('[DELETED] ', name),
                 status = 'inactive',
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Log the deletion activity
            $this->logActivity('delete', 'warehouse', $id, "Deleted warehouse: {$warehouse['name']}");

            $this->setFlashMessage('Warehouse deleted successfully', 'success');
            $this->redirect('/app/warehouses');

        } catch (\Exception $e) {
            error_log("Warehouse deletion error: " . $e->getMessage());
            $this->setFlashMessage('Delete failed: ' . $e->getMessage(), 'error');
            $this->redirect('/app/warehouses');
        }
    }

    /**
     * Get warehouse by ID
     */
    private function getWarehouseById($id)
    {
        return $this->db->fetch(
            "SELECT w.*, u.first_name, u.last_name
             FROM warehouses w
             LEFT JOIN users u ON w.created_by = u.id
             WHERE w.id = ? AND w.company_id = ? AND w.status = 'active' AND w.name NOT LIKE '[DELETED]%'",
            [$id, $this->user['company_id']]
        );
    }

    /**
     * Get storage locations in warehouse - Enhanced for Box Storage Process
     */
    private function getWarehouseStorageLocations($warehouseId)
    {
        // Get boxes with their storage information for Box Storage Process
        // Following INTAKE → BUNDLE → BOX → STORAGE workflow
        $boxes = $this->db->fetchAll(
            "SELECT
                b.id,
                b.box_id,
                b.name,
                b.description,
                b.storage_location_code,
                b.row_number,
                b.shelf_number,
                b.position_number,
                b.status,
                b.capacity,
                b.current_count,
                b.barcode_value,
                b.created_at,
                COUNT(DISTINCT bb.bundle_id) as bundle_count,
                COUNT(DISTINCT d.id) as document_count,
                COUNT(DISTINCT di.id) as intake_count,
                SUM(d.file_size) as total_size,
                GROUP_CONCAT(DISTINCT bun.name ORDER BY bun.name SEPARATOR ', ') as bundle_names,
                GROUP_CONCAT(DISTINCT di.reference_number ORDER BY di.reference_number SEPARATOR ', ') as intake_references,
                CASE
                    WHEN b.current_count >= b.capacity THEN 'full'
                    WHEN b.current_count > 0 THEN 'partial'
                    ELSE 'empty'
                END as occupancy_status
             FROM boxes b
             LEFT JOIN box_bundles bb ON b.id = bb.box_id
             LEFT JOIN bundles bun ON bb.bundle_id = bun.id AND bun.status != 'deleted' AND bun.name NOT LIKE '[DELETED]%'
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             LEFT JOIN document_intake di ON bun.id = di.bundle_id AND di.status != 'deleted'
             WHERE b.warehouse_id = ? AND b.status != 'archived' AND b.name NOT LIKE '[DELETED]%'
             GROUP BY b.id
             ORDER BY b.row_number, b.shelf_number, b.position_number",
            [$warehouseId]
        );

        // Also get traditional storage locations for backward compatibility
        // Following INTAKE → BUNDLE → BOX → STORAGE workflow
        $storageLocations = $this->db->fetchAll(
            "SELECT sl.*,
                    COUNT(d.id) as document_count,
                    SUM(d.file_size) as total_size,
                    'storage_location' as item_type
             FROM storage_locations sl
             LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
             WHERE sl.warehouse_id = ? AND sl.status = 'active'
             GROUP BY sl.id
             ORDER BY sl.type, sl.name",
            [$warehouseId]
        );

        // Mark boxes with item type for template differentiation
        foreach ($boxes as &$box) {
            $box['item_type'] = 'box';
            $box['type'] = 'box'; // For consistency with storage locations
        }

        // Combine and return both types
        return array_merge($boxes, $storageLocations);
    }

    /**
     * Get detailed hierarchy for warehouse (for debugging)
     */
    private function getWarehouseHierarchy($warehouseId)
    {
        // Get all intake entries that led to bundles and boxes in this warehouse
        return $this->db->fetchAll(
            "SELECT
                di.id as intake_id,
                di.reference_number as intake_ref,
                di.client_name,
                di.status as intake_status,
                bun.id as bundle_id,
                bun.name as bundle_name,
                bun.reference_number as bundle_ref,
                b.id as box_id,
                b.box_id as box_identifier,
                b.name as box_name,
                b.storage_location_code,
                w.name as warehouse_name,
                COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN bundles bun ON di.bundle_id = bun.id
             LEFT JOIN box_bundles bb ON bun.id = bb.bundle_id
             LEFT JOIN boxes b ON bb.box_id = b.id
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.id = ? AND di.company_id = ?
             GROUP BY di.id, bun.id, b.id
             ORDER BY di.created_at DESC",
            [$warehouseId, $this->user['company_id']]
        );
    }

    /**
     * Debug endpoint to check warehouse data
     */
    public function debug($id)
    {
        $this->requireAuth();

        if (!isset($_GET['debug'])) {
            $this->redirect("/app/warehouses/{$id}");
            return;
        }

        try {
            // Get all relevant data for debugging
            $warehouse = $this->getWarehouseById($id);

            // Get all boxes in this warehouse
            $boxes = $this->db->fetchAll(
                "SELECT * FROM boxes WHERE warehouse_id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Get all bundles linked to these boxes
            $bundles = $this->db->fetchAll(
                "SELECT bun.*, bb.box_id, b.box_id as box_identifier
                 FROM bundles bun
                 LEFT JOIN box_bundles bb ON bun.id = bb.bundle_id
                 LEFT JOIN boxes b ON bb.box_id = b.id
                 WHERE b.warehouse_id = ? AND bun.company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Get all intake entries
            $intakes = $this->db->fetchAll(
                "SELECT * FROM document_intake WHERE company_id = ? ORDER BY created_at DESC LIMIT 10",
                [$this->user['company_id']]
            );

            // Get box_bundles relationships
            $boxBundles = $this->db->fetchAll(
                "SELECT bb.*, b.box_id as box_identifier, bun.name as bundle_name
                 FROM box_bundles bb
                 JOIN boxes b ON bb.box_id = b.id
                 JOIN bundles bun ON bb.bundle_id = bun.id
                 WHERE b.warehouse_id = ?",
                [$id]
            );

            header('Content-Type: application/json');
            echo json_encode([
                'warehouse' => $warehouse,
                'boxes' => $boxes,
                'bundles' => $bundles,
                'intakes' => $intakes,
                'box_bundles' => $boxBundles
            ], JSON_PRETTY_PRINT);
            exit;

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['error' => $e->getMessage()]);
            exit;
        }
    }

    /**
     * Get warehouse statistics
     */
    private function getWarehouseStats($warehouseId)
    {
        $stats = [];

        try {
            // Total boxes (excluding deleted ones)
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM boxes WHERE warehouse_id = ? AND status != 'archived' AND name NOT LIKE '[DELETED]%'",
                [$warehouseId]
            );
            $stats['total_boxes'] = $result['count'] ?? 0;

            // Occupied boxes (partial or full, excluding deleted ones)
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM boxes WHERE warehouse_id = ? AND status IN ('partial', 'full') AND status != 'archived' AND name NOT LIKE '[DELETED]%'",
                [$warehouseId]
            );
            $stats['occupied_boxes'] = $result['count'] ?? 0;

            // Total documents in this warehouse following INTAKE → BUNDLE → BOX → STORAGE workflow
            $result = $this->db->fetch(
                "SELECT COUNT(d.id) as count
                 FROM documents d
                 JOIN bundles bun ON d.bundle_id = bun.id AND bun.name NOT LIKE '[DELETED]%'
                 JOIN box_bundles bb ON bun.id = bb.bundle_id
                 JOIN boxes b ON bb.box_id = b.id AND b.name NOT LIKE '[DELETED]%'
                 WHERE b.warehouse_id = ? AND d.status != 'deleted'",
                [$warehouseId]
            );
            $stats['total_documents'] = $result['count'] ?? 0;

            // Total storage locations (if any exist)
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM storage_locations WHERE warehouse_id = ? AND status = 'active'",
                [$warehouseId]
            );
            $stats['total_locations'] = $result['count'] ?? 0;

            // Utilization percentage
            if ($stats['total_boxes'] > 0) {
                $stats['utilization_percentage'] = round(($stats['occupied_boxes'] / $stats['total_boxes']) * 100, 1);
            } else {
                $stats['utilization_percentage'] = 0;
            }

        } catch (\Exception $e) {
            // Return default stats on error
            $stats = [
                'total_boxes' => 0,
                'occupied_boxes' => 0,
                'total_documents' => 0,
                'total_locations' => 0,
                'utilization_percentage' => 0
            ];
        }

        return $stats;
    }

    /**
     * Generate unique warehouse code
     */
    private function generateWarehouseCode($warehouseName)
    {
        // Create base code from warehouse name
        $baseCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $warehouseName), 0, 3));
        if (strlen($baseCode) < 3) {
            $baseCode = str_pad($baseCode, 3, 'W', STR_PAD_RIGHT);
        }

        // Add timestamp suffix to ensure uniqueness
        $timestamp = substr(time(), -4);
        $code = $baseCode . '-' . $timestamp;

        // Check if code already exists and modify if needed
        $counter = 1;
        $originalCode = $code;
        while ($this->codeExists($code)) {
            $code = $originalCode . '-' . $counter;
            $counter++;
        }

        return $code;
    }

    /**
     * Check if warehouse code exists
     */
    private function codeExists($code)
    {
        $result = $this->db->fetch(
            "SELECT id FROM warehouses WHERE code = ? AND company_id = ?",
            [$code, $this->user['company_id']]
        );
        return !empty($result);
    }
}
