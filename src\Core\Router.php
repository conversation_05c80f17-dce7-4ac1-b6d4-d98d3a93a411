<?php

namespace App\Core;

/**
 * Router Class
 * 
 * Handles HTTP routing for the Document Management System
 */
class Router
{
    private $routes = [];
    private $middlewares = [];
    private $groupPrefix = '';
    
    /**
     * Add a GET route
     */
    public function get($path, $handler, $middlewares = [])
    {
        $this->addRoute('GET', $path, $handler, $middlewares);
    }
    
    /**
     * Add a POST route
     */
    public function post($path, $handler, $middlewares = [])
    {
        $this->addRoute('POST', $path, $handler, $middlewares);
    }
    
    /**
     * Add a PUT route
     */
    public function put($path, $handler, $middlewares = [])
    {
        $this->addRoute('PUT', $path, $handler, $middlewares);
    }
    
    /**
     * Add a DELETE route
     */
    public function delete($path, $handler, $middlewares = [])
    {
        $this->addRoute('DELETE', $path, $handler, $middlewares);
    }
    
    /**
     * Add a route for any HTTP method
     */
    public function any($path, $handler, $middlewares = [])
    {
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
        foreach ($methods as $method) {
            $this->addRoute($method, $path, $handler, $middlewares);
        }
    }
    
    /**
     * Add a route group with common middleware
     */
    public function group($prefix, $middlewares, $callback)
    {
        $originalMiddlewares = $this->middlewares;
        $originalPrefix = $this->groupPrefix;

        $this->middlewares = array_merge($this->middlewares, $middlewares);
        $this->groupPrefix = $originalPrefix . $prefix;

        $callback($this, $prefix);

        $this->middlewares = $originalMiddlewares;
        $this->groupPrefix = $originalPrefix;
    }
    
    /**
     * Add a route
     */
    private function addRoute($method, $path, $handler, $middlewares = [])
    {
        $middlewares = array_merge($this->middlewares, $middlewares);

        // Apply group prefix
        $fullPath = $this->groupPrefix . $path;

        $this->routes[] = [
            'method' => $method,
            'path' => $fullPath,
            'handler' => $handler,
            'middlewares' => $middlewares,
            'pattern' => $this->convertToPattern($fullPath)
        ];
    }
    
    /**
     * Convert route path to regex pattern
     */
    private function convertToPattern($path)
    {
        // Escape forward slashes
        $pattern = str_replace('/', '\/', $path);
        
        // Convert parameters like {id} to regex groups
        $pattern = preg_replace('/\{([a-zA-Z0-9_]+)\}/', '([^\/]+)', $pattern);
        
        // Add start and end anchors
        return '/^' . $pattern . '$/';
    }
    
    /**
     * Handle incoming request
     */
    public function handleRequest($method, $uri)
    {
        // Debug logging
        if (defined('DEBUG_ROUTING') && DEBUG_ROUTING) {
            error_log("Router: Handling {$method} {$uri}");
        }

        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            if (preg_match($route['pattern'], $uri, $matches)) {
                // Debug logging
                if (defined('DEBUG_ROUTING') && DEBUG_ROUTING) {
                    $handlerStr = is_callable($route['handler']) ? 'Closure' : $route['handler'];
                    error_log("Router: Route matched - {$route['path']} → {$handlerStr}");
                }

                // Remove the full match from the beginning
                array_shift($matches);

                try {
                    // Execute middlewares
                    foreach ($route['middlewares'] as $middleware) {
                        if (class_exists($middleware)) {
                            $middlewareInstance = new $middleware();
                            if (method_exists($middlewareInstance, 'handle')) {
                                $result = $middlewareInstance->handle();

                                if ($result === false) {
                                    return; // Middleware blocked the request
                                }
                            }
                        }
                    }

                    // Execute the handler
                    $this->executeHandler($route['handler'], $matches);
                    return;

                } catch (Exception $e) {
                    // Log the error
                    error_log("Router Error: " . $e->getMessage());

                    // Show error page
                    http_response_code(500);
                    echo "<h1>Application Error</h1>";
                    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                    return;
                }
            }
        }

        // No route found
        $this->handleNotFound();
    }
    
    /**
     * Execute route handler
     */
    private function executeHandler($handler, $params = [])
    {
        if (is_string($handler)) {
            // Handle "Controller@method" format
            if (strpos($handler, '@') !== false) {
                list($controllerName, $method) = explode('@', $handler);
                
                $controllerClass = "App\\Controllers\\{$controllerName}";
                
                if (!class_exists($controllerClass)) {
                    throw new \Exception("Controller {$controllerClass} not found");
                }
                
                $controller = new $controllerClass();
                
                if (!method_exists($controller, $method)) {
                    throw new \Exception("Method {$method} not found in {$controllerClass}");
                }
                
                call_user_func_array([$controller, $method], $params);
            } else {
                // Handle function name
                if (function_exists($handler)) {
                    call_user_func_array($handler, $params);
                } else {
                    throw new \Exception("Function {$handler} not found");
                }
            }
        } elseif (is_callable($handler)) {
            // Handle closure
            call_user_func_array($handler, $params);
        } else {
            throw new \Exception("Invalid route handler");
        }
    }
    
    /**
     * Handle 404 Not Found
     */
    private function handleNotFound()
    {
        http_response_code(404);

        if (isAjax()) {
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => 'Route not found'
            ]);
        } else {
            // Load 404 page
            $this->loadView('errors/404');
        }
    }

    /**
     * Debug routes
     */
    public function debugRoutes($uri, $method)
    {
        echo "<h3>Debug: Route matching for {$method} {$uri}</h3>";
        foreach ($this->routes as $route) {
            if ($route['method'] === $method) {
                echo "<p>Testing: {$route['path']} → {$route['handler']}</p>";
                if (preg_match($route['pattern'], $uri, $matches)) {
                    echo "<p style='color: green;'>✓ MATCH FOUND!</p>";
                    return;
                }
            }
        }
        echo "<p style='color: red;'>✗ No matching route found</p>";
    }
    
    /**
     * Load a view file
     */
    private function loadView($view, $data = [])
    {
        $viewFile = APP_ROOT . "/src/views/{$view}.php";
        
        if (file_exists($viewFile)) {
            extract($data);
            require $viewFile;
        } else {
            echo "<h1>404 - Page Not Found</h1>";
            echo "<p>The requested page could not be found.</p>";
        }
    }
    
    /**
     * Get all registered routes
     */
    public function getRoutes()
    {
        return $this->routes;
    }
}
