<?php
/**
 * Application Constants
 */

// File upload settings
if (!defined('UPLOAD_PATH')) {
    define('UPLOAD_PATH', APP_ROOT . '/storage/uploads');
}
if (!defined('MAX_FILE_SIZE')) {
    define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
}

// Thumbnail settings
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 200);

// Pagination settings
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// Document settings
define('ALLOWED_FILE_EXTENSIONS', [
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
    'txt', 'csv', 'jpg', 'jpeg', 'png', 'gif', 'webp',
    'mp4', 'avi', 'mov', 'mp3', 'wav', 'zip', 'rar'
]);

define('ALLOWED_MIME_TYPES', [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'video/quicktime',
    'audio/mpeg',
    'audio/wav',
    'application/zip',
    'application/x-rar-compressed'
]);

// Storage settings
define('STORAGE_TYPES', ['physical', 'online']);
define('DOCUMENT_STATUSES', ['draft', 'pending', 'approved', 'rejected', 'archived', 'deleted']);

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Application settings
define('APP_NAME', 'Document Management System');
define('APP_VERSION', '1.0.0');
define('COMPANY_NAME', 'SecureDoc Storage Services');

// Email settings
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'DMS System');

// Logging settings
define('LOG_LEVEL', 'INFO');
define('LOG_PATH', APP_ROOT . '/storage/logs');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('LOG_MAX_FILES', 5);

// Cache settings
define('CACHE_ENABLED', false);
define('CACHE_TTL', 3600); // 1 hour
define('CACHE_PATH', APP_ROOT . '/storage/cache');

// API settings
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000); // requests per hour
define('API_TOKEN_EXPIRY', 86400); // 24 hours
?>
