<?php
/**
 * Direct API Test - Bypass routing
 */

// Include the application bootstrap
require_once __DIR__ . '/../src/autoload.php';
require_once __DIR__ . '/../src/config/database.php';

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

use App\Core\Database;
use App\Services\ServiceFactory;
use App\Controllers\BusinessServiceController;

try {
    // Initialize database
    $db = Database::getInstance();
    
    // Get current user
    $user = $db->fetch(
        "SELECT u.*, c.name as company_name
         FROM users u
         LEFT JOIN companies c ON u.company_id = c.id
         WHERE u.id = ? AND u.status = 'active'",
        [$_SESSION['user_id']]
    );
    
    if (!$user) {
        http_response_code(401);
        echo json_encode(['error' => 'User not found']);
        exit;
    }
    
    // Initialize service factory
    ServiceFactory::initialize($db, $user, null);
    
    // Get the action from URL parameter
    $action = $_GET['action'] ?? 'status';
    
    // Set up the controller
    $controller = new BusinessServiceController();
    
    // Handle different actions
    switch ($action) {
        case 'status':
            $controller->getServiceStatus();
            break;
            
        case 'create_box':
            // Simulate POST data for box creation
            $_POST = [
                'name' => 'Test Box ' . time(),
                'warehouse_id' => '1',
                'client_prefix' => 'TEST',
                'description' => 'Test box created via direct API'
            ];
            $controller->createBox();
            break;
            
        case 'create_bundle':
            // Simulate POST data for bundle creation
            $_POST = [
                'name' => 'Test Bundle ' . time(),
                'description' => 'Test bundle created via direct API',
                'bundle_type' => 'custom',
                'priority' => 'medium'
            ];
            $controller->createBundle();
            break;
            
        case 'search':
            // Simulate GET data for search
            $_GET['q'] = 'test';
            $_GET['filters'] = ['search_types' => 'documents,bundles,boxes'];

            try {
                $searchService = ServiceFactory::getSearchService();
                $result = $searchService->unifiedSearch('test', ['search_types' => ['documents', 'bundles', 'boxes']]);

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'data' => $result
                ]);
            } catch (Exception $e) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
            break;
            
        case 'create_intake':
            // Simulate POST data for intake creation
            $_POST = [
                'client_name' => 'Test Client ' . time(),
                'source' => 'Direct API Test',
                'document_type' => 'contract',
                'description' => 'Test intake created via direct API',
                'expected_count' => '5'
            ];
            $controller->createIntakeRequest();
            break;
            
        case 'create_delivery':
            // First, let's create a bundle to deliver
            $bundleService = ServiceFactory::getBundleHandlingService();
            $bundle = $bundleService->createBundle([
                'name' => 'Delivery Test Bundle ' . time(),
                'description' => 'Bundle for delivery test'
            ]);
            
            // Simulate POST data for delivery creation
            $_POST = [
                'target_type' => 'bundle',
                'target_id' => (string)$bundle['id'],
                'client_id' => '1',
                'delivery_type' => 'physical',
                'delivery_method' => 'courier',
                'delivery_address' => '123 Test Street, Test City'
            ];
            $controller->createDeliveryRequest();
            break;
            
        default:
            echo json_encode([
                'error' => 'Unknown action',
                'available_actions' => [
                    'status', 'create_box', 'create_bundle', 
                    'search', 'create_intake', 'create_delivery'
                ]
            ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
