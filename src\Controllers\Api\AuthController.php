<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;

/**
 * API Auth Controller
 * 
 * Handles API authentication endpoints
 */
class AuthController extends BaseController
{
    /**
     * API Login
     */
    public function login()
    {
        try {
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            
            if (empty($email) || empty($password)) {
                $this->jsonResponse(['error' => 'Email and password are required'], 400);
                return;
            }
            
            // Find user
            $user = $this->db->fetch(
                "SELECT u.*, c.name as company_name 
                 FROM users u 
                 LEFT JOIN companies c ON u.company_id = c.id 
                 WHERE u.email = ? AND u.status = 'active'",
                [$email]
            );
            
            if (!$user || !password_verify($password, $user['password'])) {
                $this->jsonResponse(['error' => 'Invalid credentials'], 401);
                return;
            }
            
            // Generate API token
            $token = $this->generateApiToken($user['id']);
            
            // Update last login
            $this->db->execute(
                "UPDATE users SET last_login = ? WHERE id = ?",
                [date('Y-m-d H:i:s'), $user['id']]
            );
            
            // Remove sensitive data
            unset($user['password']);
            
            $this->jsonResponse([
                'success' => true,
                'token' => $token,
                'user' => $user,
                'expires_in' => 3600 // 1 hour
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Login failed: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * API Logout
     */
    public function logout()
    {
        try {
            $token = $this->getAuthToken();
            
            if ($token) {
                // Invalidate token (if using token storage)
                $this->invalidateApiToken($token);
            }
            
            $this->jsonResponse(['success' => true, 'message' => 'Logged out successfully']);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Logout failed: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Refresh API token
     */
    public function refresh()
    {
        try {
            $token = $this->getAuthToken();
            
            if (!$token) {
                $this->jsonResponse(['error' => 'No token provided'], 401);
                return;
            }
            
            // Verify and decode token
            $userId = $this->verifyApiToken($token);
            
            if (!$userId) {
                $this->jsonResponse(['error' => 'Invalid token'], 401);
                return;
            }
            
            // Get user
            $user = $this->db->fetch(
                "SELECT u.*, c.name as company_name 
                 FROM users u 
                 LEFT JOIN companies c ON u.company_id = c.id 
                 WHERE u.id = ? AND u.status = 'active'",
                [$userId]
            );
            
            if (!$user) {
                $this->jsonResponse(['error' => 'User not found'], 401);
                return;
            }
            
            // Generate new token
            $newToken = $this->generateApiToken($user['id']);
            
            // Remove sensitive data
            unset($user['password']);
            
            $this->jsonResponse([
                'success' => true,
                'token' => $newToken,
                'user' => $user,
                'expires_in' => 3600
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Token refresh failed: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Generate API token
     */
    private function generateApiToken($userId)
    {
        $payload = [
            'user_id' => $userId,
            'issued_at' => time(),
            'expires_at' => time() + 3600 // 1 hour
        ];
        
        // Simple token generation (in production, use JWT or similar)
        $token = base64_encode(json_encode($payload));
        
        // Store token in database (optional)
        $this->storeApiToken($userId, $token, $payload['expires_at']);
        
        return $token;
    }
    
    /**
     * Verify API token
     */
    private function verifyApiToken($token)
    {
        try {
            $payload = json_decode(base64_decode($token), true);
            
            if (!$payload || !isset($payload['user_id']) || !isset($payload['expires_at'])) {
                return false;
            }
            
            // Check if token is expired
            if (time() > $payload['expires_at']) {
                return false;
            }
            
            // Verify token exists in database (if using token storage)
            $storedToken = $this->db->fetch(
                "SELECT user_id FROM api_tokens WHERE token = ? AND expires_at > NOW()",
                [$token]
            );
            
            if (!$storedToken) {
                return false;
            }
            
            return $payload['user_id'];
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get auth token from request
     */
    private function getAuthToken()
    {
        $headers = getallheaders();
        
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return $matches[1];
            }
        }
        
        return $_POST['token'] ?? $_GET['token'] ?? null;
    }
    
    /**
     * Store API token in database
     */
    private function storeApiToken($userId, $token, $expiresAt)
    {
        try {
            // Clean up expired tokens first
            $this->db->execute("DELETE FROM api_tokens WHERE expires_at < NOW()");
            
            // Store new token
            $this->db->insert('api_tokens', [
                'user_id' => $userId,
                'token' => $token,
                'expires_at' => date('Y-m-d H:i:s', $expiresAt),
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Exception $e) {
            // Token storage failed, but continue (token will still work without storage)
            error_log('Failed to store API token: ' . $e->getMessage());
        }
    }
    
    /**
     * Invalidate API token
     */
    private function invalidateApiToken($token)
    {
        try {
            $this->db->execute("DELETE FROM api_tokens WHERE token = ?", [$token]);
        } catch (\Exception $e) {
            // Token invalidation failed, but continue
            error_log('Failed to invalidate API token: ' . $e->getMessage());
        }
    }
}
