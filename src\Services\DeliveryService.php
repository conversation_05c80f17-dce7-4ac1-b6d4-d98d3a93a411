<?php

namespace App\Services;

/**
 * Delivery Service
 * 
 * Manages delivery of boxes, bundles, and files to clients including:
 * - Delivery request management
 * - Tracking system
 * - Client notifications
 * - Return management
 * - Delivery scheduling
 */
class DeliveryService extends BaseService
{
    /**
     * Create a new delivery request
     */
    public function createDeliveryRequest($data)
    {
        $this->validateRequired($data, ['target_type', 'target_id', 'delivery_type', 'client_id']);
        $data = $this->sanitizeData($data);
        
        $this->validateCompanyAccess($this->user['company_id']);

        try {
            $this->db->beginTransaction();

            // Validate target exists and belongs to company
            $target = $this->validateDeliveryTarget($data['target_type'], $data['target_id']);

            // Generate delivery reference
            $deliveryReference = $this->generateReferenceNumber('DEL', 'delivery_requests', 'delivery_reference');

            // Create delivery request
            $deliveryId = $this->db->execute(
                "INSERT INTO delivery_requests (
                    company_id, delivery_reference, target_type, target_id,
                    client_id, delivery_type, priority, delivery_method,
                    requested_delivery_date, delivery_address, special_instructions,
                    status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $deliveryReference,
                    $data['target_type'],
                    $data['target_id'],
                    $data['client_id'],
                    $data['delivery_type'], // 'physical', 'digital', 'both'
                    $data['priority'] ?? 'medium',
                    $data['delivery_method'] ?? 'courier',
                    $data['requested_delivery_date'] ?? null,
                    $data['delivery_address'] ?? null,
                    $data['special_instructions'] ?? null,
                    $this->user['id']
                ]
            );

            // Create delivery items based on target
            $this->createDeliveryItems($deliveryId, $data['target_type'], $data['target_id']);

            // Send notification to client
            $this->notifyClientOfDeliveryRequest($deliveryId, $data['client_id']);

            $this->logActivity('create', 'delivery_request', $deliveryId, "Created delivery request: {$deliveryReference}");

            $this->db->commit();

            return [
                'id' => $deliveryId,
                'delivery_reference' => $deliveryReference,
                'status' => 'pending',
                'target' => $target
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to create delivery request: ' . $e->getMessage());
        }
    }

    /**
     * Process delivery request (approve/reject)
     */
    public function processDeliveryRequest($deliveryId, $action, $data = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $delivery = $this->getDeliveryById($deliveryId);
        if (!$delivery) {
            throw new \Exception('Delivery request not found');
        }

        try {
            $this->db->beginTransaction();

            switch ($action) {
                case 'approve':
                    $this->approveDelivery($deliveryId, $data);
                    break;
                case 'reject':
                    $this->rejectDelivery($deliveryId, $data);
                    break;
                case 'schedule':
                    $this->scheduleDelivery($deliveryId, $data);
                    break;
                case 'dispatch':
                    $this->dispatchDelivery($deliveryId, $data);
                    break;
                case 'complete':
                    $this->completeDelivery($deliveryId, $data);
                    break;
                default:
                    throw new \Exception('Invalid action: ' . $action);
            }

            $this->db->commit();

            return ['success' => true, 'action' => $action];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to process delivery request: ' . $e->getMessage());
        }
    }

    /**
     * Track delivery status
     */
    public function trackDelivery($deliveryReference)
    {
        $delivery = $this->db->fetch(
            "SELECT dr.*, c.name as client_name, u.first_name, u.last_name
             FROM delivery_requests dr
             LEFT JOIN companies c ON dr.client_id = c.id
             LEFT JOIN users u ON dr.created_by = u.id
             WHERE dr.delivery_reference = ? AND dr.company_id = ?",
            [$deliveryReference, $this->user['company_id']]
        );

        if (!$delivery) {
            throw new \Exception('Delivery not found');
        }

        // Get delivery items
        $items = $this->getDeliveryItems($delivery['id']);

        // Get tracking history
        $tracking = $this->getDeliveryTracking($delivery['id']);

        return [
            'delivery' => $delivery,
            'items' => $items,
            'tracking' => $tracking,
            'estimated_delivery' => $this->calculateEstimatedDelivery($delivery)
        ];
    }

    /**
     * Update delivery tracking
     */
    public function updateDeliveryTracking($deliveryId, $status, $location = null, $notes = null)
    {
        $this->validateCompanyAccess($this->user['company_id']);

        try {
            $this->db->beginTransaction();

            // Add tracking entry
            $this->db->execute(
                "INSERT INTO delivery_tracking (
                    delivery_id, status, location, notes, updated_by, updated_at
                ) VALUES (?, ?, ?, ?, ?, NOW())",
                [$deliveryId, $status, $location, $notes, $this->user['id']]
            );

            // Update delivery status
            $this->db->execute(
                "UPDATE delivery_requests SET 
                 status = ?, 
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [$status, $deliveryId, $this->user['company_id']]
            );

            // Send notification if status is significant
            if (in_array($status, ['dispatched', 'delivered', 'failed'])) {
                $this->notifyDeliveryStatusChange($deliveryId, $status);
            }

            $this->logActivity('update_tracking', 'delivery_request', $deliveryId, "Updated delivery status: {$status}");

            $this->db->commit();

            return ['success' => true];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to update delivery tracking: ' . $e->getMessage());
        }
    }

    /**
     * Handle delivery return
     */
    public function processDeliveryReturn($deliveryId, $returnData)
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $delivery = $this->getDeliveryById($deliveryId);
        if (!$delivery) {
            throw new \Exception('Delivery not found');
        }

        try {
            $this->db->beginTransaction();

            // Create return request
            $returnId = $this->db->execute(
                "INSERT INTO delivery_returns (
                    delivery_id, return_reason, return_condition, 
                    return_date, processed_by, notes, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())",
                [
                    $deliveryId,
                    $returnData['reason'] ?? 'client_request',
                    $returnData['condition'] ?? 'good',
                    $returnData['return_date'] ?? date('Y-m-d'),
                    $this->user['id'],
                    $returnData['notes'] ?? null
                ]
            );

            // Update delivery status
            $this->db->execute(
                "UPDATE delivery_requests SET 
                 status = 'returned', 
                 returned_at = NOW(),
                 updated_at = NOW()
                 WHERE id = ?",
                [$deliveryId]
            );

            // Update item locations back to storage
            $this->returnItemsToStorage($deliveryId);

            $this->logActivity('return', 'delivery_request', $deliveryId, "Processed delivery return");

            $this->db->commit();

            return ['success' => true, 'return_id' => $returnId];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to process delivery return: ' . $e->getMessage());
        }
    }

    /**
     * Search delivery requests
     */
    public function searchDeliveries($filters = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $where = ["dr.company_id = ?"];
        $params = [$this->user['company_id']];

        // Apply filters
        if (!empty($filters['search'])) {
            $where[] = "(dr.delivery_reference LIKE ? OR c.name LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($filters['status'])) {
            $where[] = "dr.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['delivery_type'])) {
            $where[] = "dr.delivery_type = ?";
            $params[] = $filters['delivery_type'];
        }

        if (!empty($filters['client_id'])) {
            $where[] = "dr.client_id = ?";
            $params[] = $filters['client_id'];
        }

        if (!empty($filters['date_from'])) {
            $where[] = "DATE(dr.created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where[] = "DATE(dr.created_at) <= ?";
            $params[] = $filters['date_to'];
        }

        $whereClause = implode(' AND ', $where);
        $orderBy = $filters['sort'] ?? 'dr.created_at DESC';

        $sql = "SELECT dr.*, c.name as client_name, u.first_name, u.last_name,
                       COUNT(di.id) as item_count
                FROM delivery_requests dr
                LEFT JOIN companies c ON dr.client_id = c.id
                LEFT JOIN users u ON dr.created_by = u.id
                LEFT JOIN delivery_items di ON dr.id = di.delivery_id
                WHERE {$whereClause}
                GROUP BY dr.id
                ORDER BY {$orderBy}";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Validate delivery target exists and belongs to company
     */
    private function validateDeliveryTarget($targetType, $targetId)
    {
        $table = '';
        $nameField = '';

        switch ($targetType) {
            case 'box':
                $table = 'boxes';
                $nameField = 'box_id';
                break;
            case 'bundle':
                $table = 'bundles';
                $nameField = 'name';
                break;
            case 'document':
                $table = 'documents';
                $nameField = 'title';
                break;
            default:
                throw new \Exception('Invalid target type: ' . $targetType);
        }

        $target = $this->db->fetch(
            "SELECT id, {$nameField} as name FROM {$table}
             WHERE id = ? AND company_id = ?",
            [$targetId, $this->user['company_id']]
        );

        if (!$target) {
            throw new \Exception(ucfirst($targetType) . ' not found or access denied');
        }

        return $target;
    }

    /**
     * Create delivery items based on target
     */
    private function createDeliveryItems($deliveryId, $targetType, $targetId)
    {
        switch ($targetType) {
            case 'box':
                $this->createBoxDeliveryItems($deliveryId, $targetId);
                break;
            case 'bundle':
                $this->createBundleDeliveryItems($deliveryId, $targetId);
                break;
            case 'document':
                $this->createDocumentDeliveryItems($deliveryId, $targetId);
                break;
        }
    }

    /**
     * Create delivery items for box
     */
    private function createBoxDeliveryItems($deliveryId, $boxId)
    {
        // Get all bundles in the box
        $bundles = $this->db->fetchAll(
            "SELECT b.id, b.name FROM box_bundles bb
             JOIN bundles b ON bb.bundle_id = b.id
             WHERE bb.box_id = ?",
            [$boxId]
        );

        foreach ($bundles as $bundle) {
            $this->db->execute(
                "INSERT INTO delivery_items (
                    delivery_id, item_type, item_id, item_name, quantity
                ) VALUES (?, 'bundle', ?, ?, 1)",
                [$deliveryId, $bundle['id'], $bundle['name']]
            );
        }

        // Add the box itself
        $box = $this->db->fetch("SELECT box_id FROM boxes WHERE id = ?", [$boxId]);
        $this->db->execute(
            "INSERT INTO delivery_items (
                delivery_id, item_type, item_id, item_name, quantity
            ) VALUES (?, 'box', ?, ?, 1)",
            [$deliveryId, $boxId, $box['box_id']]
        );
    }

    /**
     * Create delivery items for bundle
     */
    private function createBundleDeliveryItems($deliveryId, $bundleId)
    {
        $bundle = $this->db->fetch(
            "SELECT name FROM bundles WHERE id = ?",
            [$bundleId]
        );

        $this->db->execute(
            "INSERT INTO delivery_items (
                delivery_id, item_type, item_id, item_name, quantity
            ) VALUES (?, 'bundle', ?, ?, 1)",
            [$deliveryId, $bundleId, $bundle['name']]
        );
    }

    /**
     * Create delivery items for document
     */
    private function createDocumentDeliveryItems($deliveryId, $documentId)
    {
        $document = $this->db->fetch(
            "SELECT title FROM documents WHERE id = ?",
            [$documentId]
        );

        $this->db->execute(
            "INSERT INTO delivery_items (
                delivery_id, item_type, item_id, item_name, quantity
            ) VALUES (?, 'document', ?, ?, 1)",
            [$deliveryId, $documentId, $document['title']]
        );
    }

    /**
     * Get delivery by ID
     */
    private function getDeliveryById($deliveryId)
    {
        return $this->db->fetch(
            "SELECT * FROM delivery_requests WHERE id = ? AND company_id = ?",
            [$deliveryId, $this->user['company_id']]
        );
    }

    /**
     * Get delivery items
     */
    private function getDeliveryItems($deliveryId)
    {
        return $this->db->fetchAll(
            "SELECT * FROM delivery_items WHERE delivery_id = ?",
            [$deliveryId]
        );
    }

    /**
     * Get delivery tracking history
     */
    private function getDeliveryTracking($deliveryId)
    {
        return $this->db->fetchAll(
            "SELECT dt.*, u.first_name, u.last_name
             FROM delivery_tracking dt
             LEFT JOIN users u ON dt.updated_by = u.id
             WHERE dt.delivery_id = ?
             ORDER BY dt.updated_at DESC",
            [$deliveryId]
        );
    }

    /**
     * Approve delivery
     */
    private function approveDelivery($deliveryId, $data)
    {
        $this->db->execute(
            "UPDATE delivery_requests SET
             status = 'approved',
             approved_by = ?,
             approved_at = NOW(),
             updated_at = NOW()
             WHERE id = ?",
            [$this->user['id'], $deliveryId]
        );

        $this->logActivity('approve', 'delivery_request', $deliveryId, "Approved delivery request");
    }

    /**
     * Reject delivery
     */
    private function rejectDelivery($deliveryId, $data)
    {
        $this->db->execute(
            "UPDATE delivery_requests SET
             status = 'rejected',
             rejection_reason = ?,
             rejected_by = ?,
             rejected_at = NOW(),
             updated_at = NOW()
             WHERE id = ?",
            [$data['reason'] ?? 'Not specified', $this->user['id'], $deliveryId]
        );

        $this->logActivity('reject', 'delivery_request', $deliveryId, "Rejected delivery request");
    }

    /**
     * Schedule delivery
     */
    private function scheduleDelivery($deliveryId, $data)
    {
        $this->db->execute(
            "UPDATE delivery_requests SET
             status = 'scheduled',
             scheduled_delivery_date = ?,
             courier_id = ?,
             updated_at = NOW()
             WHERE id = ?",
            [$data['delivery_date'], $data['courier_id'] ?? null, $deliveryId]
        );

        $this->logActivity('schedule', 'delivery_request', $deliveryId, "Scheduled delivery");
    }

    /**
     * Dispatch delivery
     */
    private function dispatchDelivery($deliveryId, $data)
    {
        $this->db->execute(
            "UPDATE delivery_requests SET
             status = 'dispatched',
             dispatched_at = NOW(),
             tracking_number = ?,
             updated_at = NOW()
             WHERE id = ?",
            [$data['tracking_number'] ?? null, $deliveryId]
        );

        $this->logActivity('dispatch', 'delivery_request', $deliveryId, "Dispatched delivery");
    }

    /**
     * Complete delivery
     */
    private function completeDelivery($deliveryId, $data)
    {
        $this->db->execute(
            "UPDATE delivery_requests SET
             status = 'delivered',
             delivered_at = NOW(),
             recipient_name = ?,
             recipient_signature = ?,
             updated_at = NOW()
             WHERE id = ?",
            [$data['recipient_name'] ?? null, $data['signature'] ?? null, $deliveryId]
        );

        $this->logActivity('complete', 'delivery_request', $deliveryId, "Completed delivery");
    }

    /**
     * Calculate estimated delivery date
     */
    private function calculateEstimatedDelivery($delivery)
    {
        // Simple estimation based on delivery method
        $days = 1; // Default same day

        switch ($delivery['delivery_method']) {
            case 'courier':
                $days = 1;
                break;
            case 'postal':
                $days = 3;
                break;
            case 'express':
                $days = 0; // Same day
                break;
        }

        return date('Y-m-d', strtotime("+{$days} days"));
    }

    /**
     * Notify client of delivery request
     */
    private function notifyClientOfDeliveryRequest($deliveryId, $clientId)
    {
        // TODO: Implement client notification
        $this->sendNotification(
            'delivery_request',
            $clientId,
            'New Delivery Request',
            'A new delivery request has been created for your items.'
        );
    }

    /**
     * Notify delivery status change
     */
    private function notifyDeliveryStatusChange($deliveryId, $status)
    {
        // TODO: Implement status change notification
        $delivery = $this->getDeliveryById($deliveryId);

        $this->sendNotification(
            'delivery_status',
            $delivery['client_id'],
            'Delivery Status Update',
            "Your delivery status has been updated to: {$status}"
        );
    }

    /**
     * Return items to storage after delivery return
     */
    private function returnItemsToStorage($deliveryId)
    {
        // TODO: Implement logic to return items to their original storage locations
        // This would update box/bundle/document locations back to warehouse

        $this->logActivity('return_to_storage', 'delivery_request', $deliveryId, "Returned items to storage");
    }
}
