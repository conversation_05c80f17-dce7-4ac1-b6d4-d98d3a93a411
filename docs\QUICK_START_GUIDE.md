# Quick Start Guide - Document Management System

## Prerequisites

### System Requirements
- **XAMPP** with PHP 8.0+ and MySQL 8.0+
- **Node.js** 16+ (for build tools)
- **Git** for version control
- **Modern web browser** with camera support
- **Minimum 4GB RAM**, 20GB free disk space

### Development Tools (Recommended)
- **VS Code** with PHP and JavaScript extensions
- **<PERSON>man** for API testing
- **MySQL Workbench** for database management
- **Chrome DevTools** for debugging

## Installation Steps

### 1. Environment Setup
```bash
# Clone the repository
git clone <repository-url>
cd dms

# Start XAMPP services
# - Apache
# - MySQL

# Verify PHP version
php --version  # Should be 8.0+
```

### 2. Database Setup
```sql
-- Create database
CREATE DATABASE dms_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional)
CREATE USER 'dms_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON dms_system.* TO 'dms_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Project Configuration
```bash
# Copy environment configuration
cp config/config.example.php config/config.php

# Edit database configuration
# Update database credentials in config/config.php
```

### 4. Install Dependencies
```bash
# Install Composer (if not installed)
# Download from https://getcomposer.org/

# Install PHP dependencies
composer install

# Install Node.js dependencies (for build tools)
npm install

# Build CSS and JavaScript assets
npm run build
```

### 5. Database Migration
```bash
# Run database migrations
php scripts/migrate.php

# Seed initial data (optional)
php scripts/seed.php
```

### 6. File Permissions
```bash
# Set proper permissions for upload directories
chmod 755 public/uploads/
chmod 755 storage/logs/
chmod 755 storage/cache/

# On Windows (using PowerShell as Administrator)
# icacls "public\uploads" /grant Everyone:F
```

## Project Structure

```
dms/
├── public/                 # Web root directory
│   ├── index.php          # Main entry point
│   ├── assets/            # CSS, JS, images
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── uploads/           # User uploaded files
├── src/                   # Application source code
│   ├── config/            # Configuration files
│   ├── controllers/       # Request handlers
│   ├── models/           # Data models
│   ├── views/            # HTML templates
│   ├── middleware/       # Request middleware
│   └── utils/            # Utility functions
├── database/             # Database related files
│   ├── migrations/       # Database schema changes
│   └── seeds/           # Initial data
├── storage/             # Application storage
│   ├── logs/           # Application logs
│   └── cache/          # Cached files
├── tests/              # Test files
├── docs/               # Documentation
└── scripts/            # Utility scripts
```

## Configuration

### Database Configuration (config/database.php)
```php
<?php
return [
    'host' => 'localhost',
    'database' => 'dms_system',
    'username' => 'dms_user',
    'password' => 'secure_password',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];
```

### Application Configuration (config/app.php)
```php
<?php
return [
    'name' => 'Document Management System',
    'url' => 'http://localhost/dms',
    'timezone' => 'UTC',
    'debug' => true, // Set to false in production
    'upload_max_size' => 50 * 1024 * 1024, // 50MB
    'allowed_file_types' => [
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 
        'jpg', 'jpeg', 'png', 'gif', 'mp4'
    ],
    'jwt_secret' => 'your-secret-key-here',
    'session_lifetime' => 3600, // 1 hour
];
```

## Development Workflow

### 1. Starting Development
```bash
# Start XAMPP services
# Navigate to http://localhost/dms

# Watch for file changes (if using build tools)
npm run watch

# Tail application logs
tail -f storage/logs/app.log
```

### 2. Making Changes

#### Frontend Changes
```bash
# Edit files in public/assets/
# CSS: public/assets/css/
# JS: public/assets/js/

# Rebuild assets
npm run build

# Or watch for changes
npm run watch
```

#### Backend Changes
```bash
# Edit PHP files in src/
# Controllers: src/controllers/
# Models: src/models/
# Views: src/views/

# No build step required for PHP
# Changes are reflected immediately
```

### 3. Database Changes
```bash
# Create new migration
php scripts/create_migration.php "add_new_table"

# Run migrations
php scripts/migrate.php

# Rollback last migration
php scripts/migrate.php --rollback
```

## Testing

### Running Tests
```bash
# Run all tests
./vendor/bin/phpunit

# Run specific test file
./vendor/bin/phpunit tests/UserTest.php

# Run with coverage
./vendor/bin/phpunit --coverage-html coverage/
```

### API Testing with Postman
```bash
# Import Postman collection
# File: docs/postman/DMS_API.postman_collection.json

# Test endpoints:
POST http://localhost/dms/api/auth/login
GET  http://localhost/dms/api/documents
POST http://localhost/dms/api/documents
```

## Common Development Tasks

### 1. Adding a New API Endpoint
```php
// 1. Create controller method (src/controllers/DocumentController.php)
public function create($request) {
    // Handle document creation
    return $this->json(['success' => true]);
}

// 2. Add route (src/routes.php)
$router->post('/api/documents', 'DocumentController@create');

// 3. Test the endpoint
// POST http://localhost/dms/api/documents
```

### 2. Adding a New Database Table
```php
// 1. Create migration (scripts/create_migration.php)
php scripts/create_migration.php "create_categories_table"

// 2. Edit migration file (database/migrations/xxx_create_categories_table.php)
public function up() {
    $sql = "CREATE TABLE categories (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $this->execute($sql);
}

// 3. Run migration
php scripts/migrate.php
```

### 3. Adding a New Frontend Component
```html
<!-- 1. Create HTML template (src/views/components/document-card.php) -->
<div class="document-card">
    <div class="document-thumbnail">
        <img src="<?= $document['thumbnail'] ?>" alt="<?= $document['title'] ?>">
    </div>
    <div class="document-info">
        <h3><?= $document['title'] ?></h3>
        <p><?= $document['description'] ?></p>
    </div>
</div>

<!-- 2. Add CSS (public/assets/css/components.css) -->
.document-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: all 0.2s;
}

.document-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

<!-- 3. Add JavaScript (public/assets/js/components.js) -->
class DocumentCard {
    constructor(element) {
        this.element = element;
        this.bindEvents();
    }
    
    bindEvents() {
        this.element.addEventListener('click', this.handleClick.bind(this));
    }
    
    handleClick() {
        // Handle card click
    }
}
```

## Debugging

### Common Issues

#### 1. Database Connection Error
```bash
# Check MySQL service is running
# Verify database credentials in config/database.php
# Check if database exists
mysql -u root -p -e "SHOW DATABASES;"
```

#### 2. File Upload Issues
```bash
# Check PHP upload settings
php -i | grep upload

# Verify directory permissions
ls -la public/uploads/

# Check error logs
tail -f storage/logs/app.log
```

#### 3. JavaScript Errors
```bash
# Open browser developer tools (F12)
# Check Console tab for errors
# Verify file paths in Network tab
```

### Logging
```php
// Add debug logging in PHP
error_log("Debug: " . print_r($data, true));

// Check logs
tail -f storage/logs/app.log
```

## Deployment Checklist

### Pre-deployment
- [ ] Run all tests
- [ ] Update configuration for production
- [ ] Build optimized assets
- [ ] Backup database
- [ ] Check file permissions

### Production Configuration
```php
// config/app.php
'debug' => false,
'url' => 'https://yourdomain.com',

// Enable error logging
'log_errors' => true,
'error_log' => '/path/to/error.log',
```

## Getting Help

### Documentation
- **Technical Specs**: `docs/TECHNICAL_SPECIFICATIONS.md`
- **Database Design**: `docs/DATABASE_DESIGN.md`
- **UI/UX Guidelines**: `docs/UI_UX_DESIGN.md`
- **Feature List**: `docs/RECOMMENDED_FEATURES.md`

### Support Resources
- **GitHub Issues**: Report bugs and feature requests
- **Stack Overflow**: Technical questions
- **PHP Documentation**: https://php.net/docs
- **MySQL Documentation**: https://dev.mysql.com/doc/

### Development Best Practices
1. **Follow PSR standards** for PHP code
2. **Write tests** for new features
3. **Use meaningful commit messages**
4. **Document your code** with comments
5. **Test on multiple browsers**
6. **Keep security in mind** for all features
