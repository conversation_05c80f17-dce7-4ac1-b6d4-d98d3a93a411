<?php

namespace App\Middleware;

use App\Core\Database;

/**
 * Authentication Middleware
 * 
 * Ensures user is authenticated before accessing protected routes
 */
class AuthMiddleware
{
    private $db;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    /**
     * Handle the middleware
     */
    public function handle()
    {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->redirectToLogin();
            return false;
        }
        
        // Verify user exists and is active
        $user = $this->db->fetch(
            "SELECT u.*, c.status as company_status 
             FROM users u 
             LEFT JOIN companies c ON u.company_id = c.id 
             WHERE u.id = ? AND u.status = 'active'",
            [$_SESSION['user_id']]
        );
        
        if (!$user) {
            $this->clearSession();
            $this->redirectToLogin();
            return false;
        }
        
        // Check if company is active (skip for super admin)
        if ($user['role'] !== 'super_admin' && $user['company_status'] !== 'active') {
            $this->clearSession();
            $this->redirectToLogin('Your company account is not active');
            return false;
        }
        
        // Update session data if needed
        if ($_SESSION['company_id'] != $user['company_id'] || $_SESSION['user_role'] != $user['role']) {
            $_SESSION['company_id'] = $user['company_id'];
            $_SESSION['user_role'] = $user['role'];
        }
        
        return true;
    }
    
    /**
     * Clear session data
     */
    private function clearSession()
    {
        unset($_SESSION['user_id']);
        unset($_SESSION['company_id']);
        unset($_SESSION['user_role']);
    }
    
    /**
     * Redirect to login page
     */
    private function redirectToLogin($message = null)
    {
        if ($message) {
            $_SESSION['flash_message'] = $message;
            $_SESSION['flash_type'] = 'error';
        }
        
        if (isAjax()) {
            header('Content-Type: application/json');
            echo json_encode([
                'error' => 'Authentication required',
                'redirect' => '/login'
            ]);
        } else {
            header('Location: /login');
        }
        exit;
    }
}
