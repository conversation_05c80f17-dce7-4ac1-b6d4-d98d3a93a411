# Business Services Testing Guide

## 🧪 How to Test Your New Business Services

### **Prerequisites**
1. ✅ Database migration completed
2. ✅ XAMPP/Apache running
3. ✅ User logged into the system

### **Testing Methods**

## **Method 1: Web Interface Testing (Recommended)**

### **Step 1: Access the Test Page**
1. Start your XAMPP server
2. Log into your DMS application
3. Navigate to: `http://localhost/dms/app/test/business-services`

### **Step 2: Test Each Service**

#### **📊 Service Status Check**
- Click "Check Service Status" button
- Should show all 5 services as "active"
- Confirms all services are properly loaded

#### **📦 Box Handling Service**
**Test Case 1: Create Box**
```
Name: Test Legal Box
Warehouse ID: 1
Client Prefix: TEST
Description: Test box for legal documents
```
**Expected Result:**
- Success message with box ID (TEST-BOX001)
- Storage location code generated
- Box status set to "empty"

#### **📁 Bundle Handling Service**
**Test Case 1: Create Bundle**
```
Name: Q1 2024 Reports
Description: Quarterly financial reports
Bundle Type: project
Priority: high
```
**Expected Result:**
- Success message with bundle ID
- Reference number generated (BDL-YYYYMM-0001)
- Bundle status set to "open"

#### **🔍 Search Service**
**Test Case 1: Search**
```
Query: test
Search Types: documents, bundles, boxes (all checked)
```
**Expected Result:**
- Search results with timing information
- Results grouped by type
- Relevance scoring applied

**Test Case 2: Search Suggestions**
```
Query: test
```
**Expected Result:**
- List of suggestions from existing data
- Suggestions categorized by type

#### **📥 Intake Service**
**Test Case 1: Create Intake**
```
Client Name: ABC Corporation
Source: Email submission
Document Type: contract
Description: Legal contracts for review
Expected Count: 10
Auto-create Bundle: checked
```
**Expected Result:**
- Success message with intake reference (INT-YYYYMM-0001)
- Bundle automatically created if checked
- Status set to "pending"

#### **🚚 Delivery Service**
**Test Case 1: Create Delivery Request**
```
Target Type: bundle
Target ID: 1 (use ID from bundle creation)
Client ID: 1
Delivery Type: physical
Delivery Method: courier
Delivery Address: 123 Main St, City, State
```
**Expected Result:**
- Success message with delivery reference (DEL-YYYYMM-0001)
- Delivery items created automatically
- Status set to "pending"

## **Method 2: Command Line Testing**

### **Run Basic Service Tests**
```bash
cd c:\xampp\htdocs\dms
php tests/test_business_services.php
```

**Expected Output:**
```
🧪 Starting Business Services Tests...

📦 Testing Box Handling Service...
  ✓ Box service instantiated successfully
  ✓ Box search functionality working (found X results)

📁 Testing Bundle Handling Service...
  ✓ Bundle service instantiated successfully
  ✓ Bundle search functionality working (found X results)

🔍 Testing Search Service...
  ✓ Search service instantiated successfully
  ✓ Search suggestions working (found X suggestions)

📥 Testing Intake Service...
  ✓ Intake service instantiated successfully
  ✓ Intake validation working correctly
  ✓ Intake search functionality working (found X results)

🚚 Testing Delivery Service...
  ✓ Delivery service instantiated successfully
  ✓ Delivery search functionality working (found X results)

✅ All tests completed successfully!
```

## **Method 3: API Testing with Postman/Curl**

### **Service Status Endpoint**
```bash
curl -X GET "http://localhost/dms/app/services/status" \
  -H "Content-Type: application/json"
```

### **Create Box**
```bash
curl -X POST "http://localhost/dms/app/services/boxes/create" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Box",
    "warehouse_id": 1,
    "client_prefix": "TEST",
    "description": "Test box creation"
  }'
```

### **Create Bundle**
```bash
curl -X POST "http://localhost/dms/app/services/bundles/create" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Bundle",
    "description": "Test bundle creation",
    "bundle_type": "custom",
    "priority": "medium"
  }'
```

### **Search**
```bash
curl -X GET "http://localhost/dms/app/services/search?q=test&filters[search_types]=documents,bundles" \
  -H "Content-Type: application/json"
```

## **Method 4: Database Verification**

### **Check Tables Created**
```sql
USE dms_system;
SHOW TABLES LIKE 'delivery_%';
SHOW TABLES LIKE 'box_movements';
SHOW TABLES LIKE 'search_logs';
```

### **Verify Data Creation**
After creating test data through the web interface:

```sql
-- Check boxes
SELECT * FROM boxes WHERE client_prefix = 'TEST';

-- Check bundles
SELECT * FROM bundles WHERE name LIKE '%Test%';

-- Check delivery requests
SELECT * FROM delivery_requests;

-- Check search logs
SELECT * FROM search_logs ORDER BY created_at DESC LIMIT 5;
```

## **Expected Business Workflow Testing**

### **Complete Workflow Test**
1. **Create Intake Request** → Should generate reference number
2. **Create Bundle** (linked to intake) → Should auto-link to intake
3. **Add Documents to Bundle** → Use existing document upload
4. **Close Bundle** → Should prepare for boxing
5. **Create Box** → Should generate box ID and location
6. **Add Bundle to Box** → Should update box status
7. **Create Delivery Request** → Should create delivery tracking
8. **Search for Items** → Should find all created items

## **Troubleshooting**

### **Common Issues**

#### **Service Not Found Error**
- Check if routes are properly loaded
- Verify user is logged in
- Check database connection

#### **Database Table Not Found**
- Run migration: `C:\xampp\mysql\bin\mysql.exe -u root -e "USE dms_system; SOURCE database/migrations/025_create_delivery_tables.sql;"`
- Check database name in config

#### **Permission Denied**
- Ensure user has proper company access
- Check user authentication

#### **Foreign Key Constraint Errors**
- Ensure referenced records exist (warehouses, users, companies)
- Check data integrity

### **Debug Mode**
Add this to test any service:
```php
try {
    // Service call
    $result = $service->methodName($data);
    echo "Success: " . json_encode($result);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
    echo "Trace: " . $e->getTraceAsString();
}
```

## **Performance Testing**

### **Load Testing**
- Create 100+ boxes, bundles, deliveries
- Test search performance with large datasets
- Monitor database query performance

### **Concurrent User Testing**
- Test multiple users creating items simultaneously
- Verify data integrity under load

## **Success Criteria**

✅ All services instantiate without errors
✅ All CRUD operations work correctly
✅ Search returns relevant results
✅ Workflow progression works (intake → bundle → box → delivery)
✅ Data integrity maintained
✅ Proper error handling and validation
✅ Performance acceptable for business use

## **Next Steps After Testing**

1. **Production Deployment**
   - Run migration on production database
   - Test with real client data
   - Monitor performance

2. **User Training**
   - Create user guides
   - Train staff on new workflows
   - Document business processes

3. **Integration**
   - Connect with existing forms
   - Add to main navigation
   - Implement notifications

4. **Monitoring**
   - Set up logging
   - Monitor service performance
   - Track usage analytics
