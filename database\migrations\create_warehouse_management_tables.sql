-- Advanced Warehouse Management Tables
-- These tables support the enhanced warehouse management features for super admin

-- Warehouse Layouts Table
-- Stores 3D layout data for warehouse visualization
CREATE TABLE IF NOT EXISTS warehouse_layouts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    layout_data JSON NOT NULL COMMENT 'JSON data for 3D warehouse layout',
    layout_version VARCHAR(20) DEFAULT '1.0',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_warehouse_layouts_warehouse_id (warehouse_id),
    INDEX idx_warehouse_layouts_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Box Movements Table
-- Tracks all box movements for audit trail and analytics
CREATE TABLE IF NOT EXISTS box_movements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    box_id INT NOT NULL,
    from_location VARCHAR(255) NULL COMMENT 'Previous location code',
    to_location VARCHAR(255) NOT NULL COMMENT 'New location code',
    movement_type ENUM('intake', 'relocation', 'retrieval', 'return', 'disposal') DEFAULT 'relocation',
    moved_by INT NOT NULL,
    reason TEXT NULL COMMENT 'Reason for movement',
    notes TEXT NULL COMMENT 'Additional notes',
    movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
    FOREIGN KEY (moved_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_box_movements_box_id (box_id),
    INDEX idx_box_movements_movement_date (movement_date),
    INDEX idx_box_movements_moved_by (moved_by),
    INDEX idx_box_movements_type (movement_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Warehouse Zones Table
-- Defines zones within warehouses for better organization
CREATE TABLE IF NOT EXISTS warehouse_zones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    zone_name VARCHAR(100) NOT NULL,
    zone_code VARCHAR(20) NOT NULL,
    zone_type ENUM('storage', 'processing', 'staging', 'restricted') DEFAULT 'storage',
    capacity INT DEFAULT 0 COMMENT 'Maximum boxes in this zone',
    current_usage INT DEFAULT 0 COMMENT 'Current boxes in this zone',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high', 'maximum') DEFAULT 'medium',
    description TEXT NULL,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_zone_code_per_warehouse (warehouse_id, zone_code),
    INDEX idx_warehouse_zones_warehouse_id (warehouse_id),
    INDEX idx_warehouse_zones_type (zone_type),
    INDEX idx_warehouse_zones_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Capacity Analytics Table
-- Stores historical capacity data for analytics and forecasting
CREATE TABLE IF NOT EXISTS capacity_analytics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    zone_id INT NULL,
    total_capacity INT NOT NULL,
    used_capacity INT NOT NULL,
    available_capacity INT NOT NULL,
    utilization_percentage DECIMAL(5,2) NOT NULL,
    efficiency_score DECIMAL(5,2) NULL COMMENT 'Calculated efficiency score',
    recorded_date DATE NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (zone_id) REFERENCES warehouse_zones(id) ON DELETE SET NULL,
    UNIQUE KEY unique_daily_record (warehouse_id, zone_id, recorded_date),
    INDEX idx_capacity_analytics_warehouse_id (warehouse_id),
    INDEX idx_capacity_analytics_recorded_date (recorded_date),
    INDEX idx_capacity_analytics_utilization (utilization_percentage)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Barcode Audit Trail Table
-- Enhanced barcode tracking for comprehensive audit trail
CREATE TABLE IF NOT EXISTS barcode_audit_trail (
    id INT PRIMARY KEY AUTO_INCREMENT,
    barcode_value VARCHAR(255) NOT NULL,
    barcode_type ENUM('box', 'bundle', 'document') NOT NULL,
    entity_id INT NOT NULL COMMENT 'ID of the box, bundle, or document',
    action ENUM('generated', 'scanned', 'printed', 'verified', 'invalidated') NOT NULL,
    scanned_by INT NULL,
    scanner_device VARCHAR(100) NULL COMMENT 'Device used for scanning',
    location VARCHAR(255) NULL COMMENT 'Location where scan occurred',
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    additional_data JSON NULL COMMENT 'Additional metadata',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (scanned_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_barcode_audit_barcode (barcode_value),
    INDEX idx_barcode_audit_type_entity (barcode_type, entity_id),
    INDEX idx_barcode_audit_action (action),
    INDEX idx_barcode_audit_created_at (created_at),
    INDEX idx_barcode_audit_scanned_by (scanned_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System Performance Metrics Table
-- Track system performance for monitoring and optimization
CREATE TABLE IF NOT EXISTS system_performance_metrics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(100) NOT NULL,
    metric_category ENUM('database', 'storage', 'network', 'application', 'warehouse') NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(20) NOT NULL COMMENT 'Unit of measurement (ms, MB, %, etc.)',
    threshold_warning DECIMAL(15,4) NULL,
    threshold_critical DECIMAL(15,4) NULL,
    status ENUM('normal', 'warning', 'critical') DEFAULT 'normal',
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_performance_metrics_name (metric_name),
    INDEX idx_performance_metrics_category (metric_category),
    INDEX idx_performance_metrics_recorded_at (recorded_at),
    INDEX idx_performance_metrics_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Warehouse Efficiency Reports Table
-- Store calculated efficiency reports for historical tracking
CREATE TABLE IF NOT EXISTS warehouse_efficiency_reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    report_date DATE NOT NULL,
    total_movements INT DEFAULT 0,
    avg_movement_time DECIMAL(8,2) DEFAULT 0 COMMENT 'Average time in minutes',
    staff_productivity_score DECIMAL(5,2) DEFAULT 0,
    space_utilization_score DECIMAL(5,2) DEFAULT 0,
    overall_efficiency_score DECIMAL(5,2) DEFAULT 0,
    bottlenecks_identified JSON NULL COMMENT 'Identified bottlenecks and issues',
    recommendations JSON NULL COMMENT 'System recommendations',
    generated_by INT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_daily_report (warehouse_id, report_date),
    INDEX idx_efficiency_reports_warehouse_id (warehouse_id),
    INDEX idx_efficiency_reports_date (report_date),
    INDEX idx_efficiency_reports_efficiency_score (overall_efficiency_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing
INSERT INTO warehouse_zones (warehouse_id, zone_name, zone_code, zone_type, capacity, description) VALUES
(1, 'Main Storage Area', 'MSA', 'storage', 1000, 'Primary storage zone for regular documents'),
(1, 'High Security Zone', 'HSZ', 'restricted', 200, 'Restricted access zone for confidential documents'),
(1, 'Processing Area', 'PA', 'processing', 50, 'Document intake and processing area'),
(1, 'Staging Area', 'SA', 'staging', 100, 'Temporary staging for document movements');

-- Insert sample capacity analytics data
INSERT INTO capacity_analytics (warehouse_id, total_capacity, used_capacity, available_capacity, utilization_percentage, recorded_date) VALUES
(1, 1000, 750, 250, 75.00, CURDATE()),
(1, 1000, 720, 280, 72.00, DATE_SUB(CURDATE(), INTERVAL 1 DAY)),
(1, 1000, 680, 320, 68.00, DATE_SUB(CURDATE(), INTERVAL 2 DAY)),
(1, 1000, 650, 350, 65.00, DATE_SUB(CURDATE(), INTERVAL 3 DAY));

-- Insert sample performance metrics
INSERT INTO system_performance_metrics (metric_name, metric_category, metric_value, metric_unit, threshold_warning, threshold_critical) VALUES
('Database Response Time', 'database', 45.50, 'ms', 100.00, 200.00),
('Storage Usage', 'storage', 68.75, '%', 80.00, 90.00),
('Active Connections', 'database', 25, 'connections', 50, 80),
('Memory Usage', 'application', 512.75, 'MB', 1024.00, 1536.00),
('Warehouse Utilization', 'warehouse', 75.25, '%', 85.00, 95.00);

-- Insert sample efficiency report
INSERT INTO warehouse_efficiency_reports (warehouse_id, report_date, total_movements, avg_movement_time, staff_productivity_score, space_utilization_score, overall_efficiency_score) VALUES
(1, CURDATE(), 45, 12.5, 85.5, 75.0, 80.25);

-- Add indexes for better performance
CREATE INDEX idx_boxes_warehouse_location ON boxes(warehouse_id, storage_location_code);
CREATE INDEX idx_bundles_box_status ON bundles(box_id, status);
CREATE INDEX idx_documents_bundle_status ON documents(bundle_id, status);

-- Update existing warehouses table to add capacity tracking fields if they don't exist
ALTER TABLE warehouses 
ADD COLUMN IF NOT EXISTS capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage',
ADD COLUMN IF NOT EXISTS total_capacity INT DEFAULT 1000 COMMENT 'Total storage capacity',
ADD COLUMN IF NOT EXISTS used_capacity INT DEFAULT 0 COMMENT 'Currently used capacity',
ADD COLUMN IF NOT EXISTS efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score',
ADD COLUMN IF NOT EXISTS last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated';

-- Compliance & Retention Management Tables
-- Legal Holds Table
CREATE TABLE IF NOT EXISTS legal_holds (
    id INT PRIMARY KEY AUTO_INCREMENT,
    hold_name VARCHAR(255) NOT NULL,
    case_number VARCHAR(100) NOT NULL,
    description TEXT NULL,
    hold_reason TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NULL,
    status ENUM('active', 'released', 'expired') DEFAULT 'active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_legal_holds_status (status),
    INDEX idx_legal_holds_case_number (case_number),
    INDEX idx_legal_holds_start_date (start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Legal Hold Documents Table
CREATE TABLE IF NOT EXISTS legal_hold_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    legal_hold_id INT NOT NULL,
    document_id INT NOT NULL,
    status ENUM('active', 'released') DEFAULT 'active',
    applied_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    released_date TIMESTAMP NULL,
    notes TEXT NULL,
    FOREIGN KEY (legal_hold_id) REFERENCES legal_holds(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    UNIQUE KEY unique_hold_document (legal_hold_id, document_id),
    INDEX idx_legal_hold_documents_status (status),
    INDEX idx_legal_hold_documents_applied_date (applied_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Destruction Schedules Table (if not exists)
CREATE TABLE IF NOT EXISTS destruction_schedules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    document_id INT NOT NULL,
    scheduled_date DATE NOT NULL,
    destruction_method ENUM('shredding', 'incineration', 'digital_deletion', 'secure_disposal') DEFAULT 'shredding',
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'overdue') DEFAULT 'scheduled',
    reason TEXT NULL,
    completed_date TIMESTAMP NULL,
    completed_by INT NULL,
    certificate_number VARCHAR(100) NULL,
    notes TEXT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (completed_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_destruction_schedules_date (scheduled_date),
    INDEX idx_destruction_schedules_status (status),
    INDEX idx_destruction_schedules_document_id (document_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Retention Policies Table (already created above, but ensuring it exists)
-- This table was already defined above in the warehouse management section

-- Compliance Audit Log Table
CREATE TABLE IF NOT EXISTS compliance_audit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    audit_type ENUM('retention_policy', 'destruction', 'legal_hold', 'access', 'modification') NOT NULL,
    entity_type ENUM('document', 'bundle', 'box', 'policy', 'legal_hold') NOT NULL,
    entity_id INT NOT NULL,
    action ENUM('created', 'updated', 'deleted', 'accessed', 'applied', 'released') NOT NULL,
    old_values JSON NULL COMMENT 'Previous values before change',
    new_values JSON NULL COMMENT 'New values after change',
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    compliance_regulation VARCHAR(100) NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_compliance_audit_type (audit_type),
    INDEX idx_compliance_audit_entity (entity_type, entity_id),
    INDEX idx_compliance_audit_action (action),
    INDEX idx_compliance_audit_user (user_id),
    INDEX idx_compliance_audit_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System Alerts Table for Real-time Monitoring
CREATE TABLE IF NOT EXISTS system_alerts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    alert_type VARCHAR(50) NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('active', 'acknowledged', 'resolved') DEFAULT 'active',
    source_system VARCHAR(100) NULL,
    affected_component VARCHAR(100) NULL,
    threshold_value DECIMAL(10,2) NULL,
    current_value DECIMAL(10,2) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    acknowledged_at TIMESTAMP NULL,
    acknowledged_by INT NULL,
    resolved_at TIMESTAMP NULL,
    resolved_by INT NULL,
    FOREIGN KEY (acknowledged_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_system_alerts_status (status),
    INDEX idx_system_alerts_severity (severity),
    INDEX idx_system_alerts_type (alert_type),
    INDEX idx_system_alerts_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Business Intelligence Metrics Table
CREATE TABLE IF NOT EXISTS business_metrics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    metric_category ENUM('revenue', 'clients', 'operations', 'growth', 'efficiency') NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(20) NOT NULL,
    period_type ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly') NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    company_id INT NULL COMMENT 'NULL for system-wide metrics',
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_business_metrics_category (metric_category),
    INDEX idx_business_metrics_name (metric_name),
    INDEX idx_business_metrics_period (period_start, period_end),
    INDEX idx_business_metrics_company (company_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Predictive Analytics Table
CREATE TABLE IF NOT EXISTS predictive_analytics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prediction_type ENUM('growth', 'capacity', 'revenue', 'churn', 'demand') NOT NULL,
    prediction_name VARCHAR(100) NOT NULL,
    current_value DECIMAL(15,4) NOT NULL,
    predicted_value DECIMAL(15,4) NOT NULL,
    confidence_score DECIMAL(5,2) NOT NULL COMMENT 'Confidence percentage (0-100)',
    prediction_horizon_days INT NOT NULL,
    prediction_date DATE NOT NULL,
    model_version VARCHAR(20) DEFAULT '1.0',
    input_parameters JSON NULL COMMENT 'Parameters used for prediction',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_predictive_analytics_type (prediction_type),
    INDEX idx_predictive_analytics_date (prediction_date),
    INDEX idx_predictive_analytics_horizon (prediction_horizon_days)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Client Analytics Table
CREATE TABLE IF NOT EXISTS client_analytics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    analysis_date DATE NOT NULL,
    lifetime_value DECIMAL(10,2) DEFAULT 0,
    churn_probability DECIMAL(5,2) DEFAULT 0 COMMENT 'Probability percentage (0-100)',
    satisfaction_score DECIMAL(3,2) DEFAULT 0 COMMENT 'Score from 0-5',
    engagement_score DECIMAL(5,2) DEFAULT 0 COMMENT 'Calculated engagement score',
    storage_growth_rate DECIMAL(5,2) DEFAULT 0,
    document_upload_frequency DECIMAL(8,2) DEFAULT 0,
    last_activity_date DATE NULL,
    risk_category ENUM('low', 'medium', 'high') DEFAULT 'low',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE KEY unique_company_date (company_id, analysis_date),
    INDEX idx_client_analytics_date (analysis_date),
    INDEX idx_client_analytics_churn (churn_probability),
    INDEX idx_client_analytics_value (lifetime_value)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample compliance data
INSERT INTO legal_holds (hold_name, case_number, description, hold_reason, start_date, created_by) VALUES
('Litigation Hold - ABC Corp', 'CASE-2024-001', 'Legal hold for pending litigation', 'Pending lawsuit regarding contract dispute', CURDATE(), 1),
('Regulatory Investigation', 'REG-2024-002', 'Hold for regulatory compliance investigation', 'SEC investigation into financial reporting', CURDATE(), 1);

INSERT INTO destruction_schedules (document_id, scheduled_date, destruction_method, reason, created_by) VALUES
(1, DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'shredding', 'Retention period expired', 1),
(2, DATE_ADD(CURDATE(), INTERVAL 60 DAY), 'secure_disposal', 'Policy-based destruction', 1);

-- Insert sample system alerts
INSERT INTO system_alerts (alert_type, severity, title, message, source_system) VALUES
('performance', 'medium', 'Database Response Time High', 'Database response time exceeded 500ms threshold', 'database'),
('capacity', 'high', 'Storage Capacity Warning', 'Storage usage has reached 85% capacity', 'storage'),
('security', 'low', 'Multiple Login Attempts', 'User attempted login 5 times in 10 minutes', 'authentication');

-- Insert sample business metrics
INSERT INTO business_metrics (metric_category, metric_name, metric_value, metric_unit, period_type, period_start, period_end) VALUES
('revenue', 'Monthly Recurring Revenue', 15750.00, 'USD', 'monthly', DATE_FORMAT(CURDATE(), '%Y-%m-01'), LAST_DAY(CURDATE())),
('clients', 'New Client Acquisitions', 12, 'count', 'monthly', DATE_FORMAT(CURDATE(), '%Y-%m-01'), LAST_DAY(CURDATE())),
('operations', 'Documents Processed', 2847, 'count', 'monthly', DATE_FORMAT(CURDATE(), '%Y-%m-01'), LAST_DAY(CURDATE())),
('efficiency', 'Average Processing Time', 2.5, 'hours', 'monthly', DATE_FORMAT(CURDATE(), '%Y-%m-01'), LAST_DAY(CURDATE()));

-- Insert sample predictive analytics
INSERT INTO predictive_analytics (prediction_type, prediction_name, current_value, predicted_value, confidence_score, prediction_horizon_days, prediction_date) VALUES
('growth', 'Document Volume Growth', 2847, 3420, 85.5, 90, CURDATE()),
('capacity', 'Storage Capacity Utilization', 68.5, 82.3, 78.2, 180, CURDATE()),
('revenue', 'Monthly Revenue Projection', 15750.00, 18900.00, 82.1, 180, CURDATE());

-- Insert sample client analytics
INSERT INTO client_analytics (company_id, analysis_date, lifetime_value, churn_probability, satisfaction_score, engagement_score, storage_growth_rate) VALUES
(1, CURDATE(), 2400.00, 15.5, 4.2, 78.5, 12.3),
(2, CURDATE(), 1800.00, 8.2, 4.7, 85.2, 18.7),
(3, CURDATE(), 3200.00, 25.8, 3.8, 65.4, 5.2);

-- Create a view for warehouse dashboard summary
CREATE OR REPLACE VIEW warehouse_dashboard_summary AS
SELECT
    w.id,
    w.name,
    w.location,
    w.capacity_percentage,
    w.total_capacity,
    w.used_capacity,
    w.efficiency_score,
    COUNT(DISTINCT b.id) as total_boxes,
    COUNT(DISTINCT CASE WHEN b.status = 'occupied' THEN b.id END) as occupied_boxes,
    COUNT(DISTINCT CASE WHEN b.status = 'empty' THEN b.id END) as empty_boxes,
    COUNT(DISTINCT bun.id) as total_bundles,
    COUNT(DISTINCT d.id) as total_documents,
    COUNT(DISTINCT wz.id) as total_zones,
    MAX(bm.movement_date) as last_movement_date
FROM warehouses w
LEFT JOIN boxes b ON w.id = b.warehouse_id
LEFT JOIN bundles bun ON b.id = bun.box_id AND bun.status = 'active'
LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
LEFT JOIN warehouse_zones wz ON w.id = wz.warehouse_id AND wz.status = 'active'
LEFT JOIN box_movements bm ON b.id = bm.box_id
WHERE w.status = 'active'
GROUP BY w.id;
