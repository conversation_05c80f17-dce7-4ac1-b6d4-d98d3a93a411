<?php

namespace App\Controllers;

/**
 * Profile Controller
 * 
 * Handles user profile management functionality
 */
class ProfileController extends BaseController
{
    /**
     * Show user profile
     */
    public function show()
    {
        $this->requireAuth();
        
        $this->view('profile/show', [
            'title' => 'My Profile',
            'user' => $this->user,
            'company' => $this->company
        ]);
    }
    
    /**
     * Show edit profile form
     */
    public function edit()
    {
        $this->requireAuth();
        
        $this->view('profile/edit', [
            'title' => 'Edit Profile',
            'user' => $this->user,
            'company' => $this->company
        ]);
    }
    
    /**
     * Update user profile
     */
    public function update()
    {
        $this->requireAuth();
        
        try {
            $data = [
                'first_name' => $_POST['first_name'] ?? '',
                'last_name' => $_POST['last_name'] ?? '',
                'email' => $_POST['email'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Validate email uniqueness (excluding current user)
            $existingUser = $this->db->fetch(
                "SELECT id FROM users WHERE email = ? AND id != ?",
                [$data['email'], $this->user['id']]
            );
            
            if ($existingUser) {
                $_SESSION['error'] = 'Email address is already in use.';
                redirect('/app/profile/edit');
                return;
            }
            
            // Update user
            $this->db->execute(
                "UPDATE users SET first_name = ?, last_name = ?, email = ?, phone = ?, updated_at = ? WHERE id = ?",
                [
                    $data['first_name'],
                    $data['last_name'], 
                    $data['email'],
                    $data['phone'],
                    $data['updated_at'],
                    $this->user['id']
                ]
            );
            
            $_SESSION['success'] = 'Profile updated successfully.';
            redirect('/app/profile');
            
        } catch (\Exception $e) {
            $_SESSION['error'] = 'Failed to update profile: ' . $e->getMessage();
            redirect('/app/profile/edit');
        }
    }
    
    /**
     * Upload user avatar
     */
    public function uploadAvatar()
    {
        $this->requireAuth();
        
        try {
            if (!isset($_FILES['avatar']) || $_FILES['avatar']['error'] !== UPLOAD_ERR_OK) {
                throw new \Exception('No file uploaded or upload error.');
            }
            
            $file = $_FILES['avatar'];
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            $maxSize = 2 * 1024 * 1024; // 2MB
            
            // Validate file type
            if (!in_array($file['type'], $allowedTypes)) {
                throw new \Exception('Invalid file type. Only JPEG, PNG, and GIF are allowed.');
            }
            
            // Validate file size
            if ($file['size'] > $maxSize) {
                throw new \Exception('File size too large. Maximum 2MB allowed.');
            }
            
            // Create upload directory if it doesn't exist
            $uploadDir = APP_ROOT . '/public/uploads/avatars';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'avatar_' . $this->user['id'] . '_' . time() . '.' . $extension;
            $filepath = $uploadDir . '/' . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new \Exception('Failed to save uploaded file.');
            }
            
            // Update user avatar in database
            $avatarUrl = '/uploads/avatars/' . $filename;
            $this->db->execute(
                "UPDATE users SET avatar = ?, updated_at = ? WHERE id = ?",
                [$avatarUrl, date('Y-m-d H:i:s'), $this->user['id']]
            );
            
            $this->jsonResponse(['success' => true, 'avatar_url' => $avatarUrl]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Change user password
     */
    public function changePassword()
    {
        $this->requireAuth();
        
        try {
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            // Validate current password
            if (!password_verify($currentPassword, $this->user['password'])) {
                throw new \Exception('Current password is incorrect.');
            }
            
            // Validate new password
            if (strlen($newPassword) < 8) {
                throw new \Exception('New password must be at least 8 characters long.');
            }
            
            if ($newPassword !== $confirmPassword) {
                throw new \Exception('New password and confirmation do not match.');
            }
            
            // Update password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $this->db->execute(
                "UPDATE users SET password = ?, updated_at = ? WHERE id = ?",
                [$hashedPassword, date('Y-m-d H:i:s'), $this->user['id']]
            );
            
            $_SESSION['success'] = 'Password changed successfully.';
            redirect('/app/profile');
            
        } catch (\Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            redirect('/app/profile');
        }
    }
}
