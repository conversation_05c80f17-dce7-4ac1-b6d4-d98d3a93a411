<?php

namespace App\Services;

/**
 * Bundle and File Handling Service
 * 
 * Manages all bundle and file-related business operations including:
 * - Bundle lifecycle management
 * - File processing workflows
 * - Bundle consolidation
 * - Version control
 * - File validation and processing
 */
class BundleHandlingService extends BaseService
{
    /**
     * Create a new bundle with proper validation
     */
    public function createBundle($data)
    {
        $this->validateRequired($data, ['name']);
        $data = $this->sanitizeData($data);
        
        $this->validateCompanyAccess($this->user['company_id']);

        try {
            $this->db->beginTransaction();

            // Generate reference number
            $referenceNumber = $this->generateReferenceNumber('BDL', 'bundles');

            // Create bundle
            $bundleId = $this->db->execute(
                "INSERT INTO bundles (
                    company_id, name, description, reference_number, bundle_type,
                    category, priority, retention_period, access_level,
                    status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'open', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $data['name'],
                    $data['description'] ?? null,
                    $referenceNumber,
                    $data['bundle_type'] ?? 'custom',
                    $data['category'] ?? 'general',
                    $data['priority'] ?? 'medium',
                    $data['retention_period'] ?? 7,
                    $data['access_level'] ?? 'private',
                    $this->user['id']
                ]
            );

            // Link to intake if provided
            if (!empty($data['intake_id'])) {
                $this->linkBundleToIntake($bundleId, $data['intake_id']);
            }

            $this->logActivity('create', 'bundle', $bundleId, "Created bundle: {$data['name']}");

            $this->db->commit();

            return [
                'id' => $bundleId,
                'reference_number' => $referenceNumber,
                'status' => 'open'
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to create bundle: ' . $e->getMessage());
        }
    }

    /**
     * Add files to bundle with validation and processing
     */
    public function addFilesToBundle($bundleId, $files, $options = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $bundle = $this->getBundleById($bundleId);
        if (!$bundle) {
            throw new \Exception('Bundle not found');
        }

        if ($bundle['status'] === 'closed') {
            throw new \Exception('Cannot add files to closed bundle');
        }

        $results = [];
        $errors = [];

        try {
            $this->db->beginTransaction();

            foreach ($files as $file) {
                try {
                    $result = $this->processFileForBundle($bundleId, $file, $options);
                    $results[] = $result;
                } catch (\Exception $e) {
                    $errors[] = [
                        'file' => $file['name'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ];
                }
            }

            // Update bundle statistics
            $this->updateBundleStats($bundleId);

            $this->db->commit();

            return [
                'success' => true,
                'processed' => count($results),
                'errors' => $errors,
                'files' => $results
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to add files to bundle: ' . $e->getMessage());
        }
    }

    /**
     * Process individual file for bundle
     */
    private function processFileForBundle($bundleId, $file, $options = [])
    {
        // Validate file
        $this->validateFile($file);

        // Determine storage type
        $storageType = $options['storage_type'] ?? 'physical';
        
        // Process file based on type
        $fileData = $this->extractFileData($file);
        
        // Store file
        $filePath = $this->storeFile($file, $bundleId, $storageType);
        
        // Create document record
        $documentId = $this->db->execute(
            "INSERT INTO documents (
                company_id, bundle_id, title, description, file_name, file_path,
                file_size, mime_type, document_type, storage_type, tags,
                status, created_by, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'approved', ?, NOW(), NOW())",
            [
                $this->user['company_id'],
                $bundleId,
                $fileData['title'],
                $fileData['description'] ?? null,
                $fileData['filename'],
                $filePath,
                $fileData['size'],
                $fileData['mime_type'],
                $fileData['document_type'],
                $storageType,
                json_encode($fileData['tags'] ?? []),
                $this->user['id']
            ]
        );

        // Generate thumbnail for images
        if (strpos($fileData['mime_type'], 'image/') === 0) {
            $this->generateThumbnail($filePath, $documentId);
        }

        // Extract text for searchability (OCR for images, text extraction for PDFs)
        $this->extractTextContent($documentId, $filePath, $fileData['mime_type']);

        $this->logActivity('add_file', 'bundle', $bundleId, "Added file: {$fileData['filename']}");

        return [
            'document_id' => $documentId,
            'filename' => $fileData['filename'],
            'size' => $fileData['size'],
            'type' => $fileData['document_type']
        ];
    }

    /**
     * Close bundle and prepare for boxing
     */
    public function closeBundle($bundleId, $options = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $bundle = $this->getBundleById($bundleId);
        if (!$bundle) {
            throw new \Exception('Bundle not found');
        }

        try {
            $this->db->beginTransaction();

            // Validate bundle can be closed
            $documentCount = $this->getBundleDocumentCount($bundleId);
            if ($documentCount === 0 && !($options['allow_empty'] ?? false)) {
                throw new \Exception('Cannot close empty bundle');
            }

            // Update bundle status
            $this->db->execute(
                "UPDATE bundles SET 
                 status = 'closed', 
                 closed_at = NOW(), 
                 closed_by = ?,
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [$this->user['id'], $bundleId, $this->user['company_id']]
            );

            // Generate bundle summary
            $summary = $this->generateBundleSummary($bundleId);

            // Update final statistics
            $this->updateBundleStats($bundleId);

            $this->logActivity('close', 'bundle', $bundleId, "Closed bundle: {$bundle['name']}");

            $this->db->commit();

            return [
                'success' => true,
                'summary' => $summary,
                'ready_for_boxing' => true
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception('Failed to close bundle: ' . $e->getMessage());
        }
    }

    /**
     * Search bundles with advanced filters
     */
    public function searchBundles($filters = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $where = ["b.company_id = ?"];
        $params = [$this->user['company_id']];

        // Apply filters
        if (!empty($filters['search'])) {
            $where[] = "(b.name LIKE ? OR b.reference_number LIKE ? OR b.description LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($filters['status'])) {
            $where[] = "b.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['bundle_type'])) {
            $where[] = "b.bundle_type = ?";
            $params[] = $filters['bundle_type'];
        }

        if (!empty($filters['category'])) {
            $where[] = "b.category = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['priority'])) {
            $where[] = "b.priority = ?";
            $params[] = $filters['priority'];
        }

        if (!empty($filters['date_from'])) {
            $where[] = "DATE(b.created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where[] = "DATE(b.created_at) <= ?";
            $params[] = $filters['date_to'];
        }

        $whereClause = implode(' AND ', $where);
        $orderBy = $filters['sort'] ?? 'b.created_at DESC';

        $sql = "SELECT b.*, 
                       COUNT(d.id) as document_count,
                       SUM(d.file_size) as total_size,
                       u.first_name, u.last_name,
                       di.client_name
                FROM bundles b
                LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
                LEFT JOIN users u ON b.created_by = u.id
                LEFT JOIN document_intake di ON b.id = di.bundle_id
                WHERE {$whereClause}
                GROUP BY b.id
                ORDER BY {$orderBy}";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get bundle details with all related information
     */
    public function getBundleDetails($bundleId)
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $bundle = $this->getBundleById($bundleId);
        if (!$bundle) {
            throw new \Exception('Bundle not found');
        }

        // Get documents
        $documents = $this->getBundleDocuments($bundleId);

        // Get statistics
        $stats = $this->getBundleStats($bundleId);

        // Get boxes containing this bundle
        $boxes = $this->getBundleBoxes($bundleId);

        // Get intake information
        $intake = $this->getBundleIntake($bundleId);

        // Get activity history
        $activities = $this->getBundleActivities($bundleId);

        return [
            'bundle' => $bundle,
            'documents' => $documents,
            'stats' => $stats,
            'boxes' => $boxes,
            'intake' => $intake,
            'activities' => $activities
        ];
    }

    /**
     * Get bundle by ID
     */
    private function getBundleById($bundleId)
    {
        return $this->db->fetch(
            "SELECT * FROM bundles WHERE id = ? AND company_id = ?",
            [$bundleId, $this->user['company_id']]
        );
    }

    /**
     * Link bundle to intake
     */
    private function linkBundleToIntake($bundleId, $intakeId)
    {
        $this->db->execute(
            "UPDATE document_intake SET bundle_id = ? WHERE id = ? AND company_id = ?",
            [$bundleId, $intakeId, $this->user['company_id']]
        );
    }

    /**
     * Validate file for upload
     */
    private function validateFile($file)
    {
        $allowedTypes = [
            'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'text/plain', 'text/csv'
        ];

        $maxSize = 100 * 1024 * 1024; // 100MB

        if (!in_array($file['type'], $allowedTypes)) {
            throw new \Exception('File type not allowed: ' . $file['type']);
        }

        if ($file['size'] > $maxSize) {
            throw new \Exception('File size exceeds maximum allowed size');
        }

        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new \Exception('File upload error: ' . $file['error']);
        }
    }

    /**
     * Extract file data and metadata
     */
    private function extractFileData($file)
    {
        $pathInfo = pathinfo($file['name']);

        return [
            'title' => $pathInfo['filename'],
            'filename' => $file['name'],
            'size' => $file['size'],
            'mime_type' => $file['type'],
            'extension' => $pathInfo['extension'] ?? '',
            'document_type' => $this->determineDocumentType($file['type']),
            'tags' => []
        ];
    }

    /**
     * Determine document type from MIME type
     */
    private function determineDocumentType($mimeType)
    {
        $typeMap = [
            'application/pdf' => 'contract',
            'application/msword' => 'contract',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'contract',
            'application/vnd.ms-excel' => 'report',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'report',
            'image/jpeg' => 'image',
            'image/png' => 'image',
            'image/gif' => 'image',
            'text/plain' => 'other',
            'text/csv' => 'report'
        ];

        return $typeMap[$mimeType] ?? 'other';
    }

    /**
     * Store file in appropriate location
     */
    private function storeFile($file, $bundleId, $storageType)
    {
        $uploadDir = APP_ROOT . '/storage/documents/' . $this->user['company_id'] . '/' . $bundleId;

        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $filename = time() . '_' . $file['name'];
        $filePath = $uploadDir . '/' . $filename;

        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new \Exception('Failed to store file');
        }

        return 'documents/' . $this->user['company_id'] . '/' . $bundleId . '/' . $filename;
    }

    /**
     * Generate thumbnail for images
     */
    private function generateThumbnail($filePath, $documentId)
    {
        // TODO: Implement thumbnail generation
        // This would use image processing libraries like GD or ImageMagick
    }

    /**
     * Extract text content for search
     */
    private function extractTextContent($documentId, $filePath, $mimeType)
    {
        // TODO: Implement text extraction
        // This would use OCR for images, PDF text extraction, etc.
    }

    /**
     * Update bundle statistics
     */
    private function updateBundleStats($bundleId)
    {
        $stats = $this->db->fetch(
            "SELECT COUNT(*) as document_count,
                    COALESCE(SUM(file_size), 0) as total_size
             FROM documents
             WHERE bundle_id = ? AND status != 'deleted'",
            [$bundleId]
        );

        $this->db->execute(
            "UPDATE bundles SET
             document_count = ?,
             total_size = ?,
             updated_at = NOW()
             WHERE id = ?",
            [$stats['document_count'], $stats['total_size'], $bundleId]
        );
    }

    /**
     * Get bundle document count
     */
    private function getBundleDocumentCount($bundleId)
    {
        $result = $this->db->fetch(
            "SELECT COUNT(*) as count FROM documents WHERE bundle_id = ? AND status != 'deleted'",
            [$bundleId]
        );
        return $result['count'];
    }

    /**
     * Generate bundle summary
     */
    private function generateBundleSummary($bundleId)
    {
        $stats = $this->db->fetch(
            "SELECT COUNT(*) as document_count,
                    SUM(file_size) as total_size,
                    COUNT(DISTINCT document_type) as type_count
             FROM documents
             WHERE bundle_id = ? AND status != 'deleted'",
            [$bundleId]
        );

        return [
            'document_count' => $stats['document_count'],
            'total_size' => $stats['total_size'],
            'type_count' => $stats['type_count'],
            'closed_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Get bundle documents
     */
    private function getBundleDocuments($bundleId)
    {
        return $this->db->fetchAll(
            "SELECT d.*, u.first_name, u.last_name
             FROM documents d
             LEFT JOIN users u ON d.created_by = u.id
             WHERE d.bundle_id = ? AND d.status != 'deleted'
             ORDER BY d.created_at DESC",
            [$bundleId]
        );
    }

    /**
     * Get bundle statistics
     */
    private function getBundleStats($bundleId)
    {
        return $this->db->fetch(
            "SELECT COUNT(*) as document_count,
                    SUM(file_size) as total_size,
                    MIN(created_at) as first_document,
                    MAX(created_at) as last_document
             FROM documents
             WHERE bundle_id = ? AND status != 'deleted'",
            [$bundleId]
        );
    }

    /**
     * Get boxes containing this bundle
     */
    private function getBundleBoxes($bundleId)
    {
        return $this->db->fetchAll(
            "SELECT b.*, bb.added_at
             FROM box_bundles bb
             JOIN boxes b ON bb.box_id = b.id
             WHERE bb.bundle_id = ?
             ORDER BY bb.added_at DESC",
            [$bundleId]
        );
    }

    /**
     * Get bundle intake information
     */
    private function getBundleIntake($bundleId)
    {
        return $this->db->fetch(
            "SELECT * FROM document_intake WHERE bundle_id = ?",
            [$bundleId]
        );
    }

    /**
     * Get bundle activities
     */
    private function getBundleActivities($bundleId)
    {
        return $this->db->fetchAll(
            "SELECT al.*, u.first_name, u.last_name
             FROM activity_logs al
             LEFT JOIN users u ON al.user_id = u.id
             WHERE al.entity_type = 'bundle' AND al.entity_id = ?
             ORDER BY al.created_at DESC
             LIMIT 50",
            [$bundleId]
        );
    }
}
