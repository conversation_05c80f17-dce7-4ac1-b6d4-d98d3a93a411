<?php
$title = 'Dashboard';
ob_start();
?>

<!-- Enhanced Background with Gradient -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Enhanced Page Header with User Welcome -->
        <div class="mb-12">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <!-- User Avatar -->
                    <div class="relative">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
                            <?php if ($user && isset($user['first_name']) && isset($user['last_name'])): ?>
                                <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                            <?php else: ?>
                                U
                            <?php endif; ?>
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-3 border-white flex items-center justify-center">
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                    </div>

                    <!-- Welcome Text -->
                    <div>
                        <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                            Good <?php
                                $hour = date('H');
                                if ($hour < 12) echo 'Morning';
                                elseif ($hour < 17) echo 'Afternoon';
                                else echo 'Evening';
                            ?>!
                        </h1>
                        <p class="text-xl text-gray-600 font-medium">
                            <?php if ($user && isset($user['first_name'])): ?>
                                Welcome back, <?= e($user['first_name']) ?>
                            <?php else: ?>
                                Welcome to your dashboard
                            <?php endif; ?>
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                            Third-party document storage service provider • <?= date('l, F j, Y') ?>
                        </p>
                    </div>
                </div>

                <!-- Quick Actions Button -->
                <div class="hidden md:flex items-center space-x-3">
                    <a href="<?= url('/app/settings') ?>" class="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                        </svg>
                        Settings
                    </a>
                    <a href="<?= url('/app/intake/create') ?>" class="inline-flex items-center px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Intake
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Search Bar with QR Scanner -->
        <div class="relative mb-12 search-container-wrapper">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-3xl blur-xl"></div>
            <div class="relative bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md">
                <div class="max-w-4xl mx-auto">
                    <!-- Search Header -->
                    <div class="text-center mb-6">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">🔍 Search Everything</h3>
                        <p class="text-gray-600">Find documents, bundles, boxes, and storage locations instantly</p>
                    </div>

                    <!-- Main Search Bar -->
                    <div class="relative group z-50">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="relative flex items-center bg-white border-2 border-gray-200 rounded-2xl shadow-lg hover:border-blue-300 focus-within:border-blue-500 transition-all duration-300">
                            <!-- Search Icon -->
                            <div class="pl-6 pr-3">
                                <svg class="w-6 h-6 text-gray-400 group-focus-within:text-blue-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>

                            <!-- Search Input -->
                            <input
                                type="text"
                                id="main-search-input"
                                placeholder="Search documents, bundles, boxes, or enter keywords..."
                                class="flex-1 py-4 px-2 text-lg bg-transparent border-none outline-none placeholder-gray-400 text-gray-900"
                                autocomplete="off"
                                onkeyup="handleSearchInput(event)"
                                onfocus="showSearchSuggestions()"
                            >

                            <!-- Search Actions -->
                            <div class="flex items-center space-x-2 pr-4">
                                <!-- Advanced Filters Button -->
                                <button
                                    onclick="toggleAdvancedFilters()"
                                    class="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-all duration-200"
                                    title="Advanced Filters"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                    </svg>
                                </button>

                                <!-- QR Scanner Button -->
                                <button
                                    onclick="openBarcodeScanner()"
                                    class="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
                                    title="Scan QR Code"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                                    </svg>
                                    <span class="hidden sm:inline font-medium">Scan</span>
                                </button>
                            </div>
                        </div>

                        <!-- Search Suggestions Dropdown -->
                        <div id="search-suggestions" class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-[9999] hidden">
                            <div class="p-4">
                                <div class="text-sm text-gray-500 mb-3">Recent searches</div>
                                <div class="space-y-2" id="suggestions-list">
                                    <!-- Suggestions will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Filters Panel (Hidden by default) -->
                    <div id="advanced-filters" class="mt-6 p-6 bg-gray-50/50 rounded-xl border border-gray-200 hidden">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Search Type Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Search In</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="search_type" value="documents" checked class="mr-2 text-blue-500">
                                        <span class="text-sm">📄 Documents</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="search_type" value="bundles" checked class="mr-2 text-blue-500">
                                        <span class="text-sm">📁 Bundles</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="search_type" value="boxes" checked class="mr-2 text-blue-500">
                                        <span class="text-sm">📦 Boxes</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Date Range Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                                <div class="space-y-2">
                                    <input type="date" id="date-from" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                    <input type="date" id="date-to" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                </div>
                            </div>

                            <!-- File Type Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">File Type</label>
                                <select id="file-type" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                    <option value="">All Types</option>
                                    <option value="pdf">PDF</option>
                                    <option value="doc">Word Document</option>
                                    <option value="xls">Excel</option>
                                    <option value="img">Images</option>
                                </select>
                            </div>
                        </div>

                        <!-- Filter Actions -->
                        <div class="flex items-center justify-end space-x-3 mt-4 pt-4 border-t border-gray-200">
                            <button onclick="clearFilters()" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                                Clear Filters
                            </button>
                            <button onclick="applyFilters()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                Apply Filters
                            </button>
                        </div>
                    </div>

                    <!-- Quick Search Categories -->
                    <div class="mt-6 flex flex-wrap items-center justify-center gap-3">
                        <span class="text-sm text-gray-500">Quick search:</span>
                        <button onclick="quickSearch('recent')" class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors">
                            Recent Items
                        </button>
                        <button onclick="quickSearch('physical')" class="px-3 py-1 text-xs bg-amber-100 text-amber-700 rounded-full hover:bg-amber-200 transition-colors">
                            Physical Storage
                        </button>
                        <button onclick="quickSearch('online')" class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors">
                            Online Storage
                        </button>
                        <button onclick="quickSearch('pending')" class="px-3 py-1 text-xs bg-yellow-100 text-yellow-700 rounded-full hover:bg-yellow-200 transition-colors">
                            Pending Items
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 stats-grid">
            <!-- Total Storage Items Card -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-green-200 text-sm font-medium">+12%</span>
                            <svg class="w-4 h-4 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-blue-100 mb-1">Storage Items</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['total_documents']) ?></p>
                    <p class="text-xs text-blue-100 mt-1">vs last month</p>
                </div>
            </div>

            <!-- Total Warehouses Card -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-green-200 text-sm font-medium">+5%</span>
                            <svg class="w-4 h-4 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-purple-100 mb-1">Warehouses</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['total_warehouses']) ?></p>
                    <p class="text-xs text-purple-100 mt-1">active locations</p>
                </div>
            </div>

            <!-- Total Bundles Card -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl p-6 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-indigo-400 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-green-200 text-sm font-medium">+8%</span>
                            <svg class="w-4 h-4 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-indigo-100 mb-1">Bundles</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['total_bundles']) ?></p>
                    <p class="text-xs text-indigo-100 mt-1">organized collections</p>
                </div>
            </div>

            <!-- Total Boxes Card -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl p-6 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-emerald-400 to-emerald-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-green-200 text-sm font-medium">+6%</span>
                            <svg class="w-4 h-4 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-emerald-100 mb-1">Total Boxes</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['total_boxes']) ?></p>
                    <p class="text-xs text-emerald-100 mt-1">storage containers</p>
                </div>
            </div>
        </div>

        <!-- Storage Type Breakdown -->
        <div class="relative mb-12">
            <div class="absolute inset-0 bg-gradient-to-r from-green-600/10 to-blue-600/10 rounded-3xl blur-xl"></div>
            <div class="relative bg-white/80 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md">
                <div class="flex items-center justify-between mb-8">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Storage Type Distribution</h3>
                        <p class="text-gray-600">Physical vs Online storage breakdown</p>
                    </div>
                    <div class="hidden md:block">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Physical Storage -->
                    <div class="bg-gradient-to-br from-amber-50 to-orange-100 border-2 border-amber-200 rounded-3xl p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <h4 class="text-xl font-bold text-gray-900">📦 Physical Storage</h4>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800">
                                Warehouse
                            </span>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 font-medium">Documents</span>
                                <span class="text-2xl font-bold text-amber-700"><?= number_format($stats['physical_documents'] ?? 0) ?></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 font-medium">Storage Boxes</span>
                                <span class="text-2xl font-bold text-amber-700"><?= number_format($stats['physical_boxes'] ?? 0) ?></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 font-medium">Storage Used</span>
                                <span class="text-lg font-bold text-amber-700"><?= formatFileSize($stats['physical_storage_size'] ?? 0) ?></span>
                            </div>
                        </div>

                        <div class="mt-6 pt-4 border-t border-amber-200">
                            <a href="<?= url('/app/boxes?storage_type=physical') ?>" class="inline-flex items-center text-sm font-medium text-amber-700 hover:text-amber-800 transition-colors">
                                View Physical Storage
                                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Online Storage -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-3xl p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                    </svg>
                                </div>
                                <h4 class="text-xl font-bold text-gray-900">☁️ Online Storage</h4>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                Digital
                            </span>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 font-medium">Documents</span>
                                <span class="text-2xl font-bold text-blue-700"><?= number_format($stats['online_documents'] ?? 0) ?></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 font-medium">Digital Boxes</span>
                                <span class="text-2xl font-bold text-blue-700"><?= number_format($stats['online_boxes'] ?? 0) ?></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 font-medium">Storage Used</span>
                                <span class="text-lg font-bold text-blue-700"><?= formatFileSize($stats['online_storage_size'] ?? 0) ?></span>
                            </div>
                        </div>

                        <div class="mt-6 pt-4 border-t border-blue-200">
                            <a href="<?= url('/app/boxes?storage_type=online') ?>" class="inline-flex items-center text-sm font-medium text-blue-700 hover:text-blue-800 transition-colors">
                                View Online Storage
                                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Recent Storage Items and Activities -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Storage Items -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div class="relative bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900">Recent Storage Items</h3>
                        </div>
                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Latest</span>
                    </div>

                    <?php if (empty($stats['recent_documents'])): ?>
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500 font-medium">No documents processed yet</p>
                            <p class="text-sm text-gray-400 mt-1">Create your first intake entry to get started</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($stats['recent_documents'] as $doc): ?>
                                <div class="group/item flex items-center justify-between p-4 bg-gray-50/50 hover:bg-white rounded-2xl border border-gray-100 hover:border-blue-200 transition-all duration-200 hover:shadow-md">
                                    <div class="flex items-center space-x-4 flex-1 min-w-0">
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center flex-shrink-0">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-semibold text-gray-900 truncate group-hover/item:text-blue-600 transition-colors"><?= e($doc['title']) ?></p>
                                            <p class="text-xs text-gray-500 mt-1">
                                                <span class="inline-flex items-center space-x-1">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2M9 12h6"></path>
                                                    </svg>
                                                    <span><?= formatFileSize($doc['file_size']) ?></span>
                                                </span>
                                                <span class="mx-1">•</span>
                                                <span class="inline-flex items-center space-x-1">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <span><?= date('M j, Y', strtotime($doc['created_at'])) ?></span>
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                    <a href="/app/documents/<?= $doc['id'] ?>" class="flex-shrink-0 w-8 h-8 bg-blue-50 hover:bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 hover:text-blue-700 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="mt-6 pt-4 border-t border-gray-100">
                            <a href="/app/documents" class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
                                View all documents
                                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div class="relative bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900">Recent Activities</h3>
                        </div>
                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Live</span>
                    </div>

                    <?php if (empty($stats['recent_activities'])): ?>
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500 font-medium">No recent activities</p>
                            <p class="text-sm text-gray-400 mt-1">Activity will appear here as you use the system</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach (array_slice($stats['recent_activities'], 0, 5) as $activity): ?>
                                <div class="flex items-start space-x-4 p-4 bg-gray-50/50 hover:bg-white rounded-2xl border border-gray-100 hover:border-purple-200 transition-all duration-200 hover:shadow-md">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center">
                                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-900">
                                            <span class="font-semibold text-purple-600"><?= e($activity['first_name'] . ' ' . $activity['last_name']) ?></span>
                                            <span class="text-gray-600"><?= e($activity['action']) ?>d a</span>
                                            <span class="font-medium text-gray-900"><?= e($activity['entity_type']) ?></span>
                                        </p>
                                        <p class="text-xs text-gray-500 mt-1 flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <?= date('M j, Y g:i A', strtotime($activity['created_at'])) ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="mt-6 pt-4 border-t border-gray-100">
                            <a href="/app/reports/audit" class="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-700 transition-colors">
                                View all activities
                                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>


    </div>
</div>

<!-- Add custom styles for animations -->
<style>
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.5); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.5);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #7c3aed);
}

/* Smooth transitions for all interactive elements */
* {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Scanner Modal Styles */
.scanner-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.scanner-container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    position: relative;
}

.scanner-video {
    width: 100%;
    max-width: 400px;
    border-radius: 15px;
    margin: 20px 0;
}

.search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.search-container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

/* Enhanced Search Bar Styles */
#main-search-input:focus {
    outline: none;
}

#search-suggestions {
    animation: slideDown 0.2s ease-out;
    z-index: 9999 !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(229, 231, 235, 0.8);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#advanced-filters {
    animation: slideDown 0.3s ease-out;
}

.search-suggestion-item:hover {
    background-color: #f3f4f6;
    transform: translateX(4px);
}

/* Quick search buttons hover effects */
.quick-search-btn {
    transition: all 0.2s ease;
}

.quick-search-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Ensure search suggestions appear above all other elements */
#search-suggestions {
    position: absolute !important;
    z-index: 9999 !important;
}

/* Ensure stats cards don't interfere with search suggestions */
.stats-grid {
    position: relative;
    z-index: 1;
}

/* Search container should have higher z-index than other sections */
.search-container-wrapper {
    position: relative;
    z-index: 100;
}
</style>

<script>
// Barcode Scanner Functionality
function openBarcodeScanner() {
    // Check if browser supports camera access
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        alert('Camera access is not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.');
        return;
    }

    // Create scanner modal
    const modal = document.createElement('div');
    modal.className = 'scanner-modal';
    modal.innerHTML = `
        <div class="scanner-container">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-gray-900">📱 Barcode Scanner</h3>
                <button onclick="closeBarcodeScanner()" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <p class="text-gray-600 mb-4">Position the QR code or barcode within the camera view</p>
            <video id="scanner-video" class="scanner-video" autoplay playsinline></video>
            <canvas id="scanner-canvas" style="display: none;"></canvas>
            <div class="mt-4">
                <button onclick="closeBarcodeScanner()" class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                    Cancel
                </button>
            </div>
            <div id="scanner-result" class="mt-4 text-sm text-gray-600"></div>
        </div>
    `;

    document.body.appendChild(modal);

    // Start camera
    startCamera();
}

function startCamera() {
    const video = document.getElementById('scanner-video');
    const canvas = document.getElementById('scanner-canvas');
    const context = canvas.getContext('2d');

    navigator.mediaDevices.getUserMedia({
        video: {
            facingMode: 'environment', // Use back camera if available
            width: { ideal: 1280 },
            height: { ideal: 720 }
        }
    })
    .then(stream => {
        video.srcObject = stream;
        video.play();

        // Start scanning for QR codes
        scanForCodes(video, canvas, context);
    })
    .catch(err => {
        console.error('Camera access denied:', err);
        document.getElementById('scanner-result').innerHTML =
            '<p class="text-red-600">Camera access denied. Please allow camera access and try again.</p>';
    });
}

function scanForCodes(video, canvas, context) {
    const scanInterval = setInterval(() => {
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

            // Try to detect QR codes using a simple pattern detection
            // In a real implementation, you'd use a library like jsQR
            try {
                // Simulate QR code detection
                const result = detectQRCode(imageData);
                if (result) {
                    clearInterval(scanInterval);
                    handleScanResult(result);
                }
            } catch (error) {
                console.log('Scanning...', error);
            }
        }
    }, 100);

    // Store interval for cleanup
    window.scanInterval = scanInterval;
}

function detectQRCode(imageData) {
    // This is a simplified detection - in production, use jsQR library
    // For demo purposes, we'll simulate detection after 3 seconds
    if (!window.scanStartTime) {
        window.scanStartTime = Date.now();
    }

    if (Date.now() - window.scanStartTime > 3000) {
        // Simulate finding a QR code
        return 'http://localhost/dms/public/app/boxes/9';
    }

    return null;
}

function handleScanResult(result) {
    document.getElementById('scanner-result').innerHTML =
        '<p class="text-green-600 font-medium">✅ Code detected! Redirecting...</p>';

    setTimeout(() => {
        closeBarcodeScanner();

        // Parse the result and redirect accordingly
        if (result.includes('/boxes/')) {
            window.location.href = result;
        } else if (result.includes('/bundles/')) {
            window.location.href = result;
        } else if (result.includes('/documents/')) {
            window.location.href = result;
        } else {
            // Try to search for the scanned text
            window.location.href = '<?= url('/app/search') ?>?q=' + encodeURIComponent(result);
        }
    }, 1500);
}

function closeBarcodeScanner() {
    // Stop camera
    const video = document.getElementById('scanner-video');
    if (video && video.srcObject) {
        const tracks = video.srcObject.getTracks();
        tracks.forEach(track => track.stop());
    }

    // Clear scan interval
    if (window.scanInterval) {
        clearInterval(window.scanInterval);
    }

    // Remove modal
    const modal = document.querySelector('.scanner-modal');
    if (modal) {
        modal.remove();
    }

    // Reset scan timer
    window.scanStartTime = null;
}

// Enhanced Search Functionality
function handleSearchInput(event) {
    const query = event.target.value.trim();

    // Handle Enter key
    if (event.key === 'Enter' && query.length > 0) {
        performSearch(query);
        return;
    }

    // Show suggestions for queries longer than 2 characters
    if (query.length >= 2) {
        fetchSearchSuggestions(query);
    } else {
        hideSearchSuggestions();
    }
}

function showSearchSuggestions() {
    const suggestions = document.getElementById('search-suggestions');
    suggestions.classList.remove('hidden');
}

function hideSearchSuggestions() {
    const suggestions = document.getElementById('search-suggestions');
    suggestions.classList.add('hidden');
}

function fetchSearchSuggestions(query) {
    // Simulate API call for suggestions
    fetch(`<?= url('/app/search/suggestions') ?>?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchSuggestions(data.suggestions || []);
        })
        .catch(error => {
            console.error('Error fetching suggestions:', error);
        });
}

function displaySearchSuggestions(suggestions) {
    const suggestionsList = document.getElementById('suggestions-list');

    if (suggestions.length === 0) {
        suggestionsList.innerHTML = '<div class="text-sm text-gray-400 py-2">No suggestions found</div>';
        return;
    }

    suggestionsList.innerHTML = suggestions.map(suggestion => {
        let icon = '📄'; // default document icon
        let typeLabel = suggestion.type;

        switch(suggestion.type) {
            case 'document':
                icon = '📄';
                typeLabel = 'Document';
                break;
            case 'bundle':
                icon = '📁';
                typeLabel = 'Bundle';
                break;
            case 'box':
                icon = '📦';
                typeLabel = 'Box';
                break;
            case 'warehouse':
                icon = '🏢';
                typeLabel = 'Warehouse';
                break;
            case 'category':
                icon = '🏷️';
                typeLabel = 'Category';
                break;
            case 'filename':
                icon = '📄';
                typeLabel = 'File';
                break;
        }

        return `
            <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg cursor-pointer search-suggestion-item" onclick="selectSuggestion('${suggestion.suggestion}')">
                <div class="flex items-center space-x-3">
                    <div class="w-6 h-6 flex items-center justify-center">
                        ${icon}
                    </div>
                    <span class="text-sm text-gray-900">${suggestion.suggestion}</span>
                </div>
                <span class="text-xs text-gray-400">${typeLabel}</span>
            </div>
        `;
    }).join('');

    showSearchSuggestions();
}

function selectSuggestion(suggestion) {
    document.getElementById('main-search-input').value = suggestion;
    hideSearchSuggestions();
    performSearch(suggestion);
}

function performSearch(query) {
    if (!query || query.trim().length === 0) {
        return;
    }

    // Get active filters
    const searchTypes = Array.from(document.querySelectorAll('input[name="search_type"]:checked')).map(cb => cb.value);
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    const fileType = document.getElementById('file-type').value;

    // If no search types are selected, default to all types
    const defaultTypes = searchTypes.length > 0 ? searchTypes : ['documents', 'bundles', 'boxes'];

    // Build search URL with parameters
    const params = new URLSearchParams();
    params.append('q', query.trim());
    params.append('types', defaultTypes.join(','));
    if (dateFrom) params.append('from', dateFrom);
    if (dateTo) params.append('to', dateTo);
    if (fileType) params.append('file_type', fileType);

    // Redirect to search page
    window.location.href = '<?= url('/app/search') ?>?' + params.toString();
}

function toggleAdvancedFilters() {
    const filtersPanel = document.getElementById('advanced-filters');
    filtersPanel.classList.toggle('hidden');
}

function clearFilters() {
    // Reset checkboxes to default
    document.querySelectorAll('input[name="search_type"]').forEach(cb => {
        cb.checked = ['documents', 'bundles', 'boxes'].includes(cb.value);
    });

    // Clear date inputs
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';
    document.getElementById('file-type').value = '';
}

function applyFilters() {
    const query = document.getElementById('main-search-input').value.trim();
    if (query.length > 0) {
        performSearch(query);
    }
}

function quickSearch(type) {
    let query = '';
    switch(type) {
        case 'recent':
            query = 'created:last-week';
            break;
        case 'physical':
            query = 'storage_type:physical';
            break;
        case 'online':
            query = 'storage_type:online';
            break;
        case 'pending':
            query = 'status:pending';
            break;
    }

    document.getElementById('main-search-input').value = query;
    performSearch(query);
}

// Legacy function for backward compatibility
function openAdvancedSearch() {
    // Focus on the main search input and show filters
    document.getElementById('main-search-input').focus();
    toggleAdvancedFilters();
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('scanner-modal')) {
        closeBarcodeScanner();
    }

    // Hide search suggestions when clicking outside
    if (!event.target.closest('#main-search-input') && !event.target.closest('#search-suggestions')) {
        hideSearchSuggestions();
    }
});

// Close modals and hide suggestions with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeBarcodeScanner();
        hideSearchSuggestions();

        // Hide advanced filters if open
        const filtersPanel = document.getElementById('advanced-filters');
        if (filtersPanel && !filtersPanel.classList.contains('hidden')) {
            filtersPanel.classList.add('hidden');
        }
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
