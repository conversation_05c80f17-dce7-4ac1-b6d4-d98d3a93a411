<?php
/**
 * Direct Route Test
 * 
 * This script tests routing directly without .htaccess
 */

// Start session
session_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

echo "<h1>Direct Route Test</h1>";

try {
    // Create router instance
    $router = new \App\Core\Router();
    
    // Load routes
    require_once APP_ROOT . '/src/routes.php';
    
    echo "<h2>Routes Loaded Successfully</h2>";
    
    // Get all routes
    $routes = $router->getRoutes();
    echo "<p>Total routes: " . count($routes) . "</p>";
    
    // Find super-admin routes
    $superAdminRoutes = array_filter($routes, function($route) {
        return strpos($route['path'], '/super-admin') !== false;
    });
    
    echo "<h3>Super Admin Routes (" . count($superAdminRoutes) . " found)</h3>";
    foreach ($superAdminRoutes as $route) {
        echo "<p>{$route['method']} {$route['path']} → {$route['handler']}</p>";
    }
    
    // Test specific route
    $testUri = '/super-admin/dashboard';
    $testMethod = 'GET';
    
    echo "<h2>Testing Route: {$testMethod} {$testUri}</h2>";
    
    // Create super admin user session for testing
    $db = \App\Core\Database::getInstance();
    $superAdmin = $db->fetch("SELECT * FROM users WHERE role = 'super_admin' LIMIT 1");
    
    if ($superAdmin) {
        $_SESSION['user_id'] = $superAdmin['id'];
        $_SESSION['user'] = $superAdmin;
        $_SESSION['authenticated'] = true;
        echo "<p>✅ Super admin session created</p>";
    } else {
        echo "<p>❌ No super admin user found</p>";
    }
    
    // Test route matching manually
    $matchFound = false;
    foreach ($routes as $route) {
        if ($route['method'] === $testMethod) {
            if (preg_match($route['pattern'], $testUri, $matches)) {
                echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
                echo "<h4>✅ Route Match Found!</h4>";
                echo "<p><strong>Path:</strong> {$route['path']}</p>";
                echo "<p><strong>Handler:</strong> {$route['handler']}</p>";
                echo "<p><strong>Pattern:</strong> {$route['pattern']}</p>";
                echo "<p><strong>Middlewares:</strong> " . implode(', ', $route['middlewares']) . "</p>";
                echo "</div>";
                $matchFound = true;
                
                // Try to execute the route manually
                echo "<h3>Executing Route Handler</h3>";
                
                try {
                    // Check middlewares first
                    $middlewarePassed = true;
                    foreach ($route['middlewares'] as $middleware) {
                        echo "<p>Testing middleware: {$middleware}</p>";
                        
                        if (class_exists($middleware)) {
                            $middlewareInstance = new $middleware();
                            if (method_exists($middlewareInstance, 'handle')) {
                                $result = $middlewareInstance->handle();
                                if ($result === false) {
                                    echo "<p>❌ Middleware {$middleware} blocked the request</p>";
                                    $middlewarePassed = false;
                                    break;
                                } else {
                                    echo "<p>✅ Middleware {$middleware} passed</p>";
                                }
                            } else {
                                echo "<p>⚠️ Middleware {$middleware} has no handle method</p>";
                            }
                        } else {
                            echo "<p>❌ Middleware class {$middleware} not found</p>";
                            $middlewarePassed = false;
                            break;
                        }
                    }
                    
                    if ($middlewarePassed) {
                        echo "<p>✅ All middlewares passed</p>";
                        
                        // Execute handler
                        if (strpos($route['handler'], '@') !== false) {
                            list($controllerName, $method) = explode('@', $route['handler']);
                            $controllerClass = "App\\Controllers\\{$controllerName}";
                            
                            echo "<p>Executing: {$controllerClass}::{$method}</p>";
                            
                            if (class_exists($controllerClass)) {
                                echo "<p>✅ Controller class exists</p>";
                                
                                $controller = new $controllerClass();
                                echo "<p>✅ Controller instantiated</p>";
                                
                                if (method_exists($controller, $method)) {
                                    echo "<p>✅ Method exists</p>";
                                    
                                    // Capture output
                                    ob_start();
                                    $controller->$method();
                                    $output = ob_get_clean();
                                    
                                    if (!empty($output)) {
                                        echo "<div style='background: #d1ecf1; padding: 10px; border: 1px solid #bee5eb; margin: 10px 0;'>";
                                        echo "<h4>✅ Handler Executed Successfully!</h4>";
                                        echo "<p><strong>Output length:</strong> " . strlen($output) . " characters</p>";
                                        echo "<h5>Output Preview (first 1000 chars):</h5>";
                                        echo "<div style='max-height: 300px; overflow: auto; background: white; padding: 10px; border: 1px solid #ccc;'>";
                                        echo "<pre>" . htmlspecialchars(substr($output, 0, 1000)) . "</pre>";
                                        echo "</div>";
                                        echo "</div>";
                                    } else {
                                        echo "<p>⚠️ Handler executed but returned no output</p>";
                                    }
                                } else {
                                    echo "<p>❌ Method {$method} not found in controller</p>";
                                }
                            } else {
                                echo "<p>❌ Controller class {$controllerClass} not found</p>";
                            }
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
                    echo "<h4>❌ Handler Execution Error</h4>";
                    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
                    echo "</div>";
                }
                
                break;
            }
        }
    }
    
    if (!$matchFound) {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "<h4>❌ No Route Match Found</h4>";
        echo "<p>No route matches: {$testMethod} {$testUri}</p>";
        echo "</div>";
    }
    
    echo "<h2>Alternative Test</h2>";
    echo "<p>Try calling the router's handleRequest method directly:</p>";
    
    try {
        ob_start();
        $router->handleRequest($testMethod, $testUri);
        $routerOutput = ob_get_clean();
        
        if (!empty($routerOutput)) {
            echo "<div style='background: #d1ecf1; padding: 10px; border: 1px solid #bee5eb; margin: 10px 0;'>";
            echo "<h4>✅ Router HandleRequest Success!</h4>";
            echo "<p><strong>Output length:</strong> " . strlen($routerOutput) . " characters</p>";
            echo "<h5>Output Preview:</h5>";
            echo "<div style='max-height: 300px; overflow: auto; background: white; padding: 10px; border: 1px solid #ccc;'>";
            echo "<pre>" . htmlspecialchars(substr($routerOutput, 0, 1000)) . "</pre>";
            echo "</div>";
            echo "</div>";
        } else {
            echo "<p>⚠️ Router handleRequest returned no output</p>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "<h4>❌ Router HandleRequest Error</h4>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h3>❌ ERROR</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}
?>
