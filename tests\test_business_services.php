<?php
/**
 * Business Services Test Script
 * 
 * Simple test to verify business services are working correctly
 */

require_once __DIR__ . '/../src/autoload.php';
require_once __DIR__ . '/../src/config/database.php';

use App\Core\Database;
use App\Services\ServiceFactory;

// Test configuration
$testUser = [
    'id' => 1,
    'company_id' => 1,
    'first_name' => 'Test',
    'last_name' => 'User',
    'email' => '<EMAIL>'
];

$testCompany = [
    'id' => 1,
    'name' => 'Test Company'
];

function runTests() {
    global $testUser, $testCompany;
    
    echo "🧪 Starting Business Services Tests...\n\n";
    
    try {
        // Initialize database
        $db = Database::getInstance();
        
        // Initialize service factory
        ServiceFactory::initialize($db, $testUser, $testCompany);
        
        // Test 1: Box Handling Service
        echo "📦 Testing Box Handling Service...\n";
        testBoxHandlingService();
        
        // Test 2: Bundle Handling Service
        echo "📁 Testing Bundle Handling Service...\n";
        testBundleHandlingService();
        
        // Test 3: Search Service
        echo "🔍 Testing Search Service...\n";
        testSearchService();
        
        // Test 4: Intake Service
        echo "📥 Testing Intake Service...\n";
        testIntakeService();
        
        // Test 5: Delivery Service
        echo "🚚 Testing Delivery Service...\n";
        testDeliveryService();
        
        echo "\n✅ All tests completed successfully!\n";
        
    } catch (Exception $e) {
        echo "\n❌ Test failed: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
}

function testBoxHandlingService() {
    try {
        $boxService = ServiceFactory::getBoxHandlingService();
        
        // Test service instantiation
        if ($boxService) {
            echo "  ✓ Box service instantiated successfully\n";
        } else {
            throw new Exception("Failed to instantiate box service");
        }
        
        // Test search functionality (should not fail even with no data)
        $searchResults = $boxService->searchBoxes(['search' => 'test']);
        echo "  ✓ Box search functionality working (found " . count($searchResults) . " results)\n";
        
    } catch (Exception $e) {
        echo "  ❌ Box service test failed: " . $e->getMessage() . "\n";
        throw $e;
    }
}

function testBundleHandlingService() {
    try {
        $bundleService = ServiceFactory::getBundleHandlingService();
        
        // Test service instantiation
        if ($bundleService) {
            echo "  ✓ Bundle service instantiated successfully\n";
        } else {
            throw new Exception("Failed to instantiate bundle service");
        }
        
        // Test search functionality
        $searchResults = $bundleService->searchBundles(['search' => 'test']);
        echo "  ✓ Bundle search functionality working (found " . count($searchResults) . " results)\n";
        
    } catch (Exception $e) {
        echo "  ❌ Bundle service test failed: " . $e->getMessage() . "\n";
        throw $e;
    }
}

function testSearchService() {
    try {
        $searchService = ServiceFactory::getSearchService();
        
        // Test service instantiation
        if ($searchService) {
            echo "  ✓ Search service instantiated successfully\n";
        } else {
            throw new Exception("Failed to instantiate search service");
        }
        
        // Test suggestions functionality
        $suggestions = $searchService->getSearchSuggestions('test', 5);
        echo "  ✓ Search suggestions working (found " . count($suggestions) . " suggestions)\n";
        
    } catch (Exception $e) {
        echo "  ❌ Search service test failed: " . $e->getMessage() . "\n";
        throw $e;
    }
}

function testIntakeService() {
    try {
        $intakeService = ServiceFactory::getIntakeService();
        
        // Test service instantiation
        if ($intakeService) {
            echo "  ✓ Intake service instantiated successfully\n";
        } else {
            throw new Exception("Failed to instantiate intake service");
        }
        
        // Test validation functionality
        $validationErrors = $intakeService->validateIntakeData([
            'client_name' => 'Test Client',
            'source' => 'Test Source',
            'document_type' => 'contract',
            'description' => 'Test description'
        ]);
        
        if (empty($validationErrors)) {
            echo "  ✓ Intake validation working correctly\n";
        } else {
            echo "  ⚠️  Intake validation returned errors: " . implode(', ', $validationErrors) . "\n";
        }
        
        // Test search functionality
        $searchResults = $intakeService->searchIntakeRequests(['search' => 'test']);
        echo "  ✓ Intake search functionality working (found " . count($searchResults) . " results)\n";
        
    } catch (Exception $e) {
        echo "  ❌ Intake service test failed: " . $e->getMessage() . "\n";
        throw $e;
    }
}

function testDeliveryService() {
    try {
        $deliveryService = ServiceFactory::getDeliveryService();
        
        // Test service instantiation
        if ($deliveryService) {
            echo "  ✓ Delivery service instantiated successfully\n";
        } else {
            throw new Exception("Failed to instantiate delivery service");
        }
        
        // Test search functionality
        $searchResults = $deliveryService->searchDeliveries(['search' => 'test']);
        echo "  ✓ Delivery search functionality working (found " . count($searchResults) . " results)\n";
        
    } catch (Exception $e) {
        echo "  ❌ Delivery service test failed: " . $e->getMessage() . "\n";
        throw $e;
    }
}

function testServiceFactory() {
    echo "🏭 Testing Service Factory...\n";
    
    try {
        // Test available services
        $availableServices = ServiceFactory::getAvailableServices();
        echo "  ✓ Available services: " . count($availableServices) . "\n";
        
        foreach ($availableServices as $serviceClass => $serviceName) {
            echo "    - {$serviceName} ({$serviceClass})\n";
        }
        
    } catch (Exception $e) {
        echo "  ❌ Service factory test failed: " . $e->getMessage() . "\n";
        throw $e;
    }
}

// Run the tests
if (php_sapi_name() === 'cli') {
    runTests();
} else {
    echo "<pre>";
    runTests();
    echo "</pre>";
}
