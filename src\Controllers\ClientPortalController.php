<?php

namespace App\Controllers;

/**
 * Client Portal Controller
 * 
 * Implements Step 4: Online Integration/Access from documentation
 * Provides client access to view documents, search, and make requests
 */
class ClientPortalController extends BaseController
{
    /**
     * Client dashboard
     */
    public function dashboard()
    {
        $this->requireRole(['client']);
        
        try {
            // Get client company relationship
            $clientCompany = $this->getClientCompanyRelationship();
            if (!$clientCompany) {
                throw new \Exception('Client company relationship not found');
            }

            // Get client statistics
            $stats = $this->getClientStats($clientCompany['service_provider_company_id']);
            
            // Get recent documents
            $recentDocuments = $this->getClientRecentDocuments($clientCompany['service_provider_company_id'], 10);
            
            // Get pending requests
            $pendingRequests = $this->getClientRequests($clientCompany['service_provider_company_id'], 'pending', 5);
            
            // Get recent alerts
            $alerts = $this->getClientAlerts($clientCompany['service_provider_company_id'], 5);

            // Log client access
            $this->logClientAccess('login', null, null, null);

            $this->view('client-portal/dashboard', [
                'title' => 'Client Portal - Dashboard',
                'clientCompany' => $clientCompany,
                'stats' => $stats,
                'recentDocuments' => $recentDocuments,
                'pendingRequests' => $pendingRequests,
                'alerts' => $alerts
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading dashboard: ' . $e->getMessage(), 'error');
            $this->redirect('/client/login');
        }
    }

    /**
     * View client documents
     */
    public function documents()
    {
        $this->requireRole(['client']);
        
        try {
            $clientCompany = $this->getClientCompanyRelationship();
            if (!$clientCompany) {
                throw new \Exception('Client company relationship not found');
            }

            // Get search parameters
            $search = $_GET['search'] ?? '';
            $year = $_GET['year'] ?? '';
            $department = $_GET['department'] ?? '';
            $documentType = $_GET['document_type'] ?? '';
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 20;
            $offset = ($page - 1) * $limit;

            // Build search query
            $documents = $this->searchClientDocuments(
                $clientCompany['service_provider_company_id'],
                $search,
                $year,
                $department,
                $documentType,
                $limit,
                $offset
            );

            // Get total count for pagination
            $totalCount = $this->getClientDocumentCount(
                $clientCompany['service_provider_company_id'],
                $search,
                $year,
                $department,
                $documentType
            );

            // Get filter options
            $filterOptions = $this->getClientFilterOptions($clientCompany['service_provider_company_id']);

            // Log client access
            $this->logClientAccess('view_documents', null, null, null);

            $this->view('client-portal/documents', [
                'title' => 'My Documents',
                'clientCompany' => $clientCompany,
                'documents' => $documents,
                'totalCount' => $totalCount,
                'currentPage' => $page,
                'totalPages' => ceil($totalCount / $limit),
                'filterOptions' => $filterOptions,
                'filters' => [
                    'search' => $search,
                    'year' => $year,
                    'department' => $department,
                    'document_type' => $documentType
                ]
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading documents: ' . $e->getMessage(), 'error');
            $this->redirect('/client/dashboard');
        }
    }

    /**
     * View specific document details
     */
    public function viewDocument($id)
    {
        $this->requireRole(['client']);
        
        try {
            $clientCompany = $this->getClientCompanyRelationship();
            if (!$clientCompany) {
                throw new \Exception('Client company relationship not found');
            }

            // Get document details (ensure client has access)
            $document = $this->getClientDocument($id, $clientCompany['service_provider_company_id']);
            if (!$document) {
                throw new \Exception('Document not found or access denied');
            }

            // Get document bundle and box information
            $bundleInfo = $this->getDocumentBundleInfo($id);
            $boxInfo = $this->getDocumentBoxInfo($id);

            // Log client access
            $this->logClientAccess('view_document', 'document', $id, $document['reference_number']);

            $this->view('client-portal/document-details', [
                'title' => 'Document Details',
                'clientCompany' => $clientCompany,
                'document' => $document,
                'bundleInfo' => $bundleInfo,
                'boxInfo' => $boxInfo
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading document: ' . $e->getMessage(), 'error');
            $this->redirect('/client/documents');
        }
    }

    /**
     * Client requests page
     */
    public function requests()
    {
        $this->requireRole(['client']);
        
        try {
            $clientCompany = $this->getClientCompanyRelationship();
            if (!$clientCompany) {
                throw new \Exception('Client company relationship not found');
            }

            // Get client requests
            $requests = $this->getClientRequests($clientCompany['service_provider_company_id']);

            $this->view('client-portal/requests', [
                'title' => 'My Requests',
                'clientCompany' => $clientCompany,
                'requests' => $requests
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading requests: ' . $e->getMessage(), 'error');
            $this->redirect('/client/dashboard');
        }
    }

    /**
     * Create new request form
     */
    public function createRequest()
    {
        $this->requireRole(['client']);
        
        try {
            $clientCompany = $this->getClientCompanyRelationship();
            if (!$clientCompany) {
                throw new \Exception('Client company relationship not found');
            }

            // Get available documents/bundles/boxes for request
            $availableTargets = $this->getAvailableRequestTargets($clientCompany['service_provider_company_id']);

            $this->view('client-portal/create-request', [
                'title' => 'Create New Request',
                'clientCompany' => $clientCompany,
                'availableTargets' => $availableTargets
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading request form: ' . $e->getMessage(), 'error');
            $this->redirect('/client/requests');
        }
    }

    /**
     * Store new request
     */
    public function storeRequest()
    {
        $this->requireRole(['client']);
        
        try {
            $clientCompany = $this->getClientCompanyRelationship();
            if (!$clientCompany) {
                throw new \Exception('Client company relationship not found');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'request_type' => 'required|in:retrieval,digitization,destruction,return',
                'target_type' => 'required|in:document,bundle,box,intake',
                'target_id' => 'required|integer',
                'priority' => 'required|in:low,medium,high,urgent',
                'description' => 'required|max:1000',
                'required_by_date' => 'date',
                'client_notes' => 'max:2000'
            ]);

            // Get target reference
            $targetReference = $this->getTargetReference($data['target_type'], $data['target_id']);

            // Generate request number
            $requestNumber = $this->generateRequestNumber($clientCompany['service_provider_company_id']);

            // Create request
            $requestId = $this->db->execute(
                "INSERT INTO document_requests (
                    request_number, client_user_id, service_provider_company_id, request_type,
                    priority, description, target_type, target_id, target_reference,
                    required_by_date, client_notes, status, requested_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())",
                [
                    $requestNumber,
                    $this->user['id'],
                    $clientCompany['service_provider_company_id'],
                    $data['request_type'],
                    $data['priority'],
                    $data['description'],
                    $data['target_type'],
                    $data['target_id'],
                    $targetReference,
                    $data['required_by_date'] ?? null,
                    $data['client_notes'] ?? null
                ]
            );

            // Log request activity
            $this->logRequestActivity($requestId, 'created', 'Request created by client', null, 'pending');

            // Log client access
            $this->logClientAccess('request', $data['target_type'], $data['target_id'], $targetReference);

            $this->setFlashMessage('Request submitted successfully', 'success');
            $this->redirect('/client/requests');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to submit request: ' . $e->getMessage(), 'error');
            $this->redirect('/client/create-request');
        }
    }

    /**
     * Get client company relationship
     */
    private function getClientCompanyRelationship()
    {
        return $this->db->fetch(
            "SELECT cc.*, c.name as service_provider_name
             FROM client_companies cc
             JOIN companies c ON cc.service_provider_company_id = c.id
             WHERE cc.client_user_id = ? AND cc.status = 'active'",
            [$this->user['id']]
        );
    }

    /**
     * Get client statistics
     */
    private function getClientStats($serviceProviderCompanyId)
    {
        $stats = [];

        try {
            // Total documents
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM documents d
                 JOIN bundles b ON d.bundle_id = b.id
                 WHERE b.company_id = ? AND d.status != 'deleted'",
                [$serviceProviderCompanyId]
            );
            $stats['total_documents'] = $result['count'] ?? 0;

            // Total bundles
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM bundles WHERE company_id = ? AND status = 'active'",
                [$serviceProviderCompanyId]
            );
            $stats['total_bundles'] = $result['count'] ?? 0;

            // Total boxes
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM boxes WHERE company_id = ?",
                [$serviceProviderCompanyId]
            );
            $stats['total_boxes'] = $result['count'] ?? 0;

            // Pending requests
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM document_requests 
                 WHERE client_user_id = ? AND service_provider_company_id = ? AND status = 'pending'",
                [$this->user['id'], $serviceProviderCompanyId]
            );
            $stats['pending_requests'] = $result['count'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Search client documents
     */
    private function searchClientDocuments($serviceProviderCompanyId, $search, $year, $department, $documentType, $limit, $offset)
    {
        $where = ["b.company_id = ?"];
        $params = [$serviceProviderCompanyId];

        if (!empty($search)) {
            $where[] = "(d.file_name LIKE ? OR d.description LIKE ? OR b.name LIKE ?)";
            $searchTerm = "%{$search}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($year)) {
            $where[] = "b.year = ?";
            $params[] = $year;
        }

        if (!empty($department)) {
            $where[] = "b.department = ?";
            $params[] = $department;
        }

        if (!empty($documentType)) {
            $where[] = "b.document_type = ?";
            $params[] = $documentType;
        }

        $whereClause = implode(' AND ', $where);

        return $this->db->fetchAll(
            "SELECT d.*, b.name as bundle_name, b.reference_number as bundle_reference,
                    box.box_id, box.storage_location_code,
                    w.name as warehouse_name
             FROM documents d
             JOIN bundles b ON d.bundle_id = b.id
             LEFT JOIN boxes box ON b.box_id = box.id
             LEFT JOIN warehouses w ON box.warehouse_id = w.id
             WHERE {$whereClause} AND d.status != 'deleted'
             ORDER BY d.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, [$limit, $offset])
        );
    }

    /**
     * Get client document count
     */
    private function getClientDocumentCount($serviceProviderCompanyId, $search, $year, $department, $documentType)
    {
        $where = ["b.company_id = ?"];
        $params = [$serviceProviderCompanyId];

        if (!empty($search)) {
            $where[] = "(d.file_name LIKE ? OR d.description LIKE ? OR b.name LIKE ?)";
            $searchTerm = "%{$search}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($year)) {
            $where[] = "b.year = ?";
            $params[] = $year;
        }

        if (!empty($department)) {
            $where[] = "b.department = ?";
            $params[] = $department;
        }

        if (!empty($documentType)) {
            $where[] = "b.document_type = ?";
            $params[] = $documentType;
        }

        $whereClause = implode(' AND ', $where);

        $result = $this->db->fetch(
            "SELECT COUNT(*) as count
             FROM documents d
             JOIN bundles b ON d.bundle_id = b.id
             WHERE {$whereClause} AND d.status != 'deleted'",
            $params
        );

        return $result['count'] ?? 0;
    }

    /**
     * Get client filter options
     */
    private function getClientFilterOptions($serviceProviderCompanyId)
    {
        $options = [];

        // Get available years
        $years = $this->db->fetchAll(
            "SELECT DISTINCT b.year FROM bundles b WHERE b.company_id = ? AND b.year IS NOT NULL ORDER BY b.year DESC",
            [$serviceProviderCompanyId]
        );
        $options['years'] = array_column($years, 'year');

        // Get available departments
        $departments = $this->db->fetchAll(
            "SELECT DISTINCT b.department FROM bundles b WHERE b.company_id = ? AND b.department IS NOT NULL ORDER BY b.department",
            [$serviceProviderCompanyId]
        );
        $options['departments'] = array_column($departments, 'department');

        // Get available document types
        $documentTypes = $this->db->fetchAll(
            "SELECT DISTINCT b.document_type FROM bundles b WHERE b.company_id = ? AND b.document_type IS NOT NULL ORDER BY b.document_type",
            [$serviceProviderCompanyId]
        );
        $options['document_types'] = array_column($documentTypes, 'document_type');

        return $options;
    }

    /**
     * Get client requests
     */
    private function getClientRequests($serviceProviderCompanyId, $status = null, $limit = null)
    {
        $where = ["client_user_id = ?", "service_provider_company_id = ?"];
        $params = [$this->user['id'], $serviceProviderCompanyId];

        if ($status) {
            $where[] = "status = ?";
            $params[] = $status;
        }

        $whereClause = implode(' AND ', $where);
        $limitClause = $limit ? "LIMIT {$limit}" : "";

        return $this->db->fetchAll(
            "SELECT dr.*,
                    assigned.first_name as assigned_first_name, assigned.last_name as assigned_last_name,
                    approved.first_name as approved_first_name, approved.last_name as approved_last_name
             FROM document_requests dr
             LEFT JOIN users assigned ON dr.assigned_to = assigned.id
             LEFT JOIN users approved ON dr.approved_by = approved.id
             WHERE {$whereClause}
             ORDER BY dr.requested_date DESC
             {$limitClause}",
            $params
        );
    }

    /**
     * Generate request number
     */
    private function generateRequestNumber($serviceProviderCompanyId)
    {
        $count = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM document_requests WHERE service_provider_company_id = ?",
            [$serviceProviderCompanyId]
        );

        return 'REQ-' . date('Y') . '-' . str_pad($count + 1, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Log client access activity
     */
    private function logClientAccess($action, $targetType, $targetId, $targetReference)
    {
        try {
            $clientCompany = $this->getClientCompanyRelationship();
            if (!$clientCompany) return;

            $this->db->execute(
                "INSERT INTO client_access_log (
                    client_user_id, service_provider_company_id, action, target_type,
                    target_id, target_reference, ip_address, user_agent, session_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $this->user['id'],
                    $clientCompany['service_provider_company_id'],
                    $action,
                    $targetType,
                    $targetId,
                    $targetReference,
                    $_SERVER['REMOTE_ADDR'] ?? null,
                    $_SERVER['HTTP_USER_AGENT'] ?? null,
                    session_id()
                ]
            );
        } catch (\Exception $e) {
            // Log error but don't fail the request
            error_log("Failed to log client access: " . $e->getMessage());
        }
    }
}
