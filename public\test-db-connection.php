<?php
/**
 * Database Connection Test
 * 
 * Simple test to verify database connectivity before running billing setup
 */

// Security check - only allow from localhost
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';

if (!in_array($clientIP, $allowedIPs)) {
    die('Access denied. This test page can only be accessed from localhost for security reasons.');
}

require_once __DIR__ . '/../src/autoload.php';

$results = [];
$overallSuccess = true;

// Test 1: Check if autoload works
try {
    $results['autoload'] = ['status' => 'success', 'message' => 'Autoload working correctly'];
} catch (Exception $e) {
    $results['autoload'] = ['status' => 'error', 'message' => 'Autoload failed: ' . $e->getMessage()];
    $overallSuccess = false;
}

// Test 2: Check database config
try {
    require_once __DIR__ . '/../src/config/database.php';
    $results['config'] = ['status' => 'success', 'message' => 'Database config loaded'];
} catch (Exception $e) {
    $results['config'] = ['status' => 'error', 'message' => 'Database config failed: ' . $e->getMessage()];
    $overallSuccess = false;
}

// Test 3: Test database connection
try {
    use App\Core\Database;
    $db = Database::getInstance();
    $results['connection'] = ['status' => 'success', 'message' => 'Database connection successful'];
} catch (Exception $e) {
    $results['connection'] = ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
    $overallSuccess = false;
}

// Test 4: Test basic query
if ($overallSuccess) {
    try {
        $result = $db->fetch("SELECT 1 as test");
        if ($result && $result['test'] == 1) {
            $results['query'] = ['status' => 'success', 'message' => 'Basic query test passed'];
        } else {
            $results['query'] = ['status' => 'error', 'message' => 'Basic query returned unexpected result'];
            $overallSuccess = false;
        }
    } catch (Exception $e) {
        $results['query'] = ['status' => 'error', 'message' => 'Basic query failed: ' . $e->getMessage()];
        $overallSuccess = false;
    }
}

// Test 5: Check existing tables
if ($overallSuccess) {
    try {
        $tables = $db->fetchAll("SHOW TABLES");
        $tableNames = array_column($tables, array_keys($tables[0])[0]);
        $results['tables'] = [
            'status' => 'success', 
            'message' => 'Found ' . count($tableNames) . ' existing tables: ' . implode(', ', array_slice($tableNames, 0, 10)) . (count($tableNames) > 10 ? '...' : '')
        ];
    } catch (Exception $e) {
        $results['tables'] = ['status' => 'warning', 'message' => 'Could not list tables: ' . $e->getMessage()];
    }
}

// Test 6: Check if billing tables already exist
if ($overallSuccess) {
    $billingTables = ['billing_rates', 'billing_events', 'billing_invoices', 'billing_payments'];
    $existingBillingTables = [];
    
    foreach ($billingTables as $table) {
        try {
            $exists = $db->fetch("SHOW TABLES LIKE '{$table}'");
            if ($exists) {
                $existingBillingTables[] = $table;
            }
        } catch (Exception $e) {
            // Ignore errors for this test
        }
    }
    
    if (empty($existingBillingTables)) {
        $results['billing_tables'] = ['status' => 'info', 'message' => 'No billing tables found - ready for setup'];
    } else {
        $results['billing_tables'] = [
            'status' => 'warning', 
            'message' => 'Some billing tables already exist: ' . implode(', ', $existingBillingTables)
        ];
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test - DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database"></i> Database Connection Test
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($overallSuccess): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> All Tests Passed!</h5>
                                <p>Your database connection is working correctly. You can proceed with the billing module setup.</p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle"></i> Connection Issues Detected</h5>
                                <p>Please fix the issues below before proceeding with the billing setup.</p>
                            </div>
                        <?php endif; ?>

                        <h6>Test Results:</h6>
                        <div class="list-group">
                            <?php foreach ($results as $testName => $result): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <?php
                                                $icon = 'fas fa-question';
                                                $class = 'text-secondary';
                                                
                                                switch ($result['status']) {
                                                    case 'success':
                                                        $icon = 'fas fa-check-circle';
                                                        $class = 'text-success';
                                                        break;
                                                    case 'warning':
                                                        $icon = 'fas fa-exclamation-triangle';
                                                        $class = 'text-warning';
                                                        break;
                                                    case 'error':
                                                        $icon = 'fas fa-times-circle';
                                                        $class = 'text-danger';
                                                        break;
                                                    case 'info':
                                                        $icon = 'fas fa-info-circle';
                                                        $class = 'text-info';
                                                        break;
                                                }
                                                ?>
                                                <i class="<?= $icon ?> <?= $class ?>"></i>
                                                <?= ucfirst(str_replace('_', ' ', $testName)) ?>
                                            </h6>
                                            <p class="mb-0"><?= htmlspecialchars($result['message']) ?></p>
                                        </div>
                                        <span class="badge bg-<?= $result['status'] === 'success' ? 'success' : ($result['status'] === 'error' ? 'danger' : ($result['status'] === 'warning' ? 'warning' : 'info')) ?>">
                                            <?= ucfirst($result['status']) ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="mt-4">
                            <?php if ($overallSuccess): ?>
                                <a href="setup-billing.php" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> Proceed to Billing Setup
                                </a>
                            <?php else: ?>
                                <button type="button" class="btn btn-secondary" onclick="location.reload()">
                                    <i class="fas fa-redo"></i> Retry Tests
                                </button>
                            <?php endif; ?>
                            
                            <a href="../" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-home"></i> Back to DMS
                            </a>
                        </div>

                        <div class="mt-4 pt-3 border-top">
                            <h6>Database Configuration:</h6>
                            <small class="text-muted">
                                <strong>Host:</strong> <?= $_ENV['DB_HOST'] ?? 'localhost' ?><br>
                                <strong>Database:</strong> <?= $_ENV['DB_DATABASE'] ?? 'dms_system' ?><br>
                                <strong>Username:</strong> <?= $_ENV['DB_USERNAME'] ?? 'root' ?><br>
                                <strong>Port:</strong> <?= $_ENV['DB_PORT'] ?? '3306' ?>
                            </small>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt"></i> 
                                This test page is only accessible from localhost for security reasons.
                                You can safely delete this file after setup is complete.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
