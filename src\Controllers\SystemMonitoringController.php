<?php

namespace App\Controllers;

/**
 * Real-time System Monitoring Controller
 * 
 * Advanced system monitoring and health dashboard for super admin
 * Provides real-time metrics, alerts, and performance monitoring
 */
class SystemMonitoringController extends BaseController
{
    /**
     * System Monitoring Dashboard
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get real-time system health metrics
            $systemHealth = $this->getSystemHealthMetrics();
            
            // Get performance metrics
            $performanceMetrics = $this->getPerformanceMetrics();
            
            // Get active alerts
            $activeAlerts = $this->getActiveSystemAlerts();
            
            // Get system usage trends
            $usageTrends = $this->getSystemUsageTrends();
            
            // Get resource utilization
            $resourceUtilization = $this->getResourceUtilization();

            $this->view('super-admin/system-monitoring', [
                'title' => 'Real-time System Monitoring',
                'systemHealth' => $systemHealth,
                'performanceMetrics' => $performanceMetrics,
                'activeAlerts' => $activeAlerts,
                'usageTrends' => $usageTrends,
                'resourceUtilization' => $resourceUtilization
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading system monitoring: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/dashboard');
        }
    }

    /**
     * Performance Analytics Dashboard
     */
    public function performance()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get detailed performance analytics
            $databasePerformance = $this->getDatabasePerformance();
            $applicationPerformance = $this->getApplicationPerformance();
            $networkPerformance = $this->getNetworkPerformance();
            $storagePerformance = $this->getStoragePerformance();
            
            // Get performance trends
            $performanceTrends = $this->getPerformanceTrends();

            $this->view('super-admin/performance-analytics', [
                'title' => 'Performance Analytics',
                'databasePerformance' => $databasePerformance,
                'applicationPerformance' => $applicationPerformance,
                'networkPerformance' => $networkPerformance,
                'storagePerformance' => $storagePerformance,
                'performanceTrends' => $performanceTrends
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading performance analytics: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/system-monitoring');
        }
    }

    /**
     * Security Monitoring Dashboard
     */
    public function security()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get security metrics
            $securityMetrics = $this->getSecurityMetrics();
            
            // Get recent security events
            $securityEvents = $this->getRecentSecurityEvents();
            
            // Get threat analysis
            $threatAnalysis = $this->getThreatAnalysis();
            
            // Get access patterns
            $accessPatterns = $this->getAccessPatterns();

            $this->view('super-admin/security-monitoring', [
                'title' => 'Security Monitoring',
                'securityMetrics' => $securityMetrics,
                'securityEvents' => $securityEvents,
                'threatAnalysis' => $threatAnalysis,
                'accessPatterns' => $accessPatterns
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading security monitoring: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/system-monitoring');
        }
    }

    /**
     * Alert Management
     */
    public function alerts()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->handleAlertAction();
        }

        try {
            // Get all system alerts
            $allAlerts = $this->getAllSystemAlerts();
            
            // Get alert statistics
            $alertStats = $this->getAlertStatistics();
            
            // Get alert configuration
            $alertConfig = $this->getAlertConfiguration();

            $this->view('super-admin/alert-management', [
                'title' => 'Alert Management',
                'allAlerts' => $allAlerts,
                'alertStats' => $alertStats,
                'alertConfig' => $alertConfig
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading alert management: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/system-monitoring');
        }
    }

    /**
     * System Health API endpoint for real-time updates
     */
    public function healthApi()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            $health = $this->getSystemHealthMetrics();
            $this->jsonResponse([
                'success' => true,
                'data' => $health,
                'timestamp' => time()
            ]);
        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time system health metrics
     */
    private function getSystemHealthMetrics()
    {
        // Database health
        $dbHealth = $this->checkDatabaseHealth();
        
        // Storage health
        $storageHealth = $this->checkStorageHealth();
        
        // Application health
        $appHealth = $this->checkApplicationHealth();
        
        // Overall system status
        $overallStatus = $this->calculateOverallStatus($dbHealth, $storageHealth, $appHealth);

        return [
            'overall_status' => $overallStatus,
            'database' => $dbHealth,
            'storage' => $storageHealth,
            'application' => $appHealth,
            'uptime' => $this->getSystemUptime(),
            'last_check' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Check database health
     */
    private function checkDatabaseHealth()
    {
        try {
            $start = microtime(true);
            
            // Test database connection and response time
            $result = $this->db->fetch("SELECT 1 as test");
            $responseTime = (microtime(true) - $start) * 1000; // Convert to milliseconds
            
            // Get database statistics
            $dbStats = $this->db->fetch("SHOW STATUS LIKE 'Threads_connected'");
            $connections = $dbStats['Value'] ?? 0;
            
            // Get database size
            $sizeQuery = $this->db->fetch("
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            $dbSize = $sizeQuery['size_mb'] ?? 0;

            $status = 'healthy';
            if ($responseTime > 1000) $status = 'warning';
            if ($responseTime > 2000) $status = 'critical';

            return [
                'status' => $status,
                'response_time' => round($responseTime, 2),
                'connections' => $connections,
                'size_mb' => $dbSize,
                'max_connections' => 100 // This should be configurable
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'critical',
                'error' => $e->getMessage(),
                'response_time' => null,
                'connections' => 0,
                'size_mb' => 0
            ];
        }
    }

    /**
     * Check storage health
     */
    private function checkStorageHealth()
    {
        try {
            $uploadPath = APP_ROOT . '/public/uploads';
            $storagePath = APP_ROOT . '/storage';
            
            // Check disk space
            $totalSpace = disk_total_space($uploadPath);
            $freeSpace = disk_free_space($uploadPath);
            $usedSpace = $totalSpace - $freeSpace;
            $usagePercentage = ($usedSpace / $totalSpace) * 100;
            
            // Get upload directory size
            $uploadSize = $this->getDirectorySize($uploadPath);
            $storageSize = $this->getDirectorySize($storagePath);
            
            $status = 'healthy';
            if ($usagePercentage > 80) $status = 'warning';
            if ($usagePercentage > 90) $status = 'critical';

            return [
                'status' => $status,
                'total_space_gb' => round($totalSpace / (1024**3), 2),
                'free_space_gb' => round($freeSpace / (1024**3), 2),
                'used_space_gb' => round($usedSpace / (1024**3), 2),
                'usage_percentage' => round($usagePercentage, 2),
                'upload_size_mb' => round($uploadSize / (1024**2), 2),
                'storage_size_mb' => round($storageSize / (1024**2), 2)
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'critical',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check application health
     */
    private function checkApplicationHealth()
    {
        try {
            // Memory usage
            $memoryUsage = memory_get_usage(true);
            $memoryPeak = memory_get_peak_usage(true);
            $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
            $memoryPercentage = ($memoryUsage / $memoryLimit) * 100;
            
            // PHP version and extensions
            $phpVersion = PHP_VERSION;
            $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
            $missingExtensions = [];
            
            foreach ($requiredExtensions as $ext) {
                if (!extension_loaded($ext)) {
                    $missingExtensions[] = $ext;
                }
            }
            
            $status = 'healthy';
            if ($memoryPercentage > 80 || !empty($missingExtensions)) $status = 'warning';
            if ($memoryPercentage > 90) $status = 'critical';

            return [
                'status' => $status,
                'memory_usage_mb' => round($memoryUsage / (1024**2), 2),
                'memory_peak_mb' => round($memoryPeak / (1024**2), 2),
                'memory_limit_mb' => round($memoryLimit / (1024**2), 2),
                'memory_percentage' => round($memoryPercentage, 2),
                'php_version' => $phpVersion,
                'missing_extensions' => $missingExtensions
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'critical',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics()
    {
        return $this->db->fetchAll(
            "SELECT 
                metric_name,
                metric_value,
                metric_unit,
                status,
                recorded_at
             FROM system_performance_metrics 
             WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
             ORDER BY recorded_at DESC
             LIMIT 50",
            []
        );
    }

    /**
     * Get active system alerts
     */
    private function getActiveSystemAlerts()
    {
        return $this->db->fetchAll(
            "SELECT * FROM system_alerts 
             WHERE status = 'active' 
             ORDER BY severity DESC, created_at DESC 
             LIMIT 20",
            []
        );
    }

    /**
     * Helper methods
     */
    private function calculateOverallStatus($db, $storage, $app)
    {
        $statuses = [$db['status'], $storage['status'], $app['status']];
        
        if (in_array('critical', $statuses)) return 'critical';
        if (in_array('warning', $statuses)) return 'warning';
        return 'healthy';
    }

    private function getSystemUptime()
    {
        // This is a simplified uptime calculation
        // In production, you might want to track this more accurately
        return '99.9%';
    }

    private function getDirectorySize($directory)
    {
        $size = 0;
        if (is_dir($directory)) {
            foreach (new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory)) as $file) {
                $size += $file->getSize();
            }
        }
        return $size;
    }

    private function parseMemoryLimit($limit)
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $limit = (int) $limit;
        
        switch($last) {
            case 'g': $limit *= 1024;
            case 'm': $limit *= 1024;
            case 'k': $limit *= 1024;
        }
        
        return $limit;
    }

    // Additional methods for other features
    private function getSystemUsageTrends() { return []; }
    private function getResourceUtilization() { return []; }
    private function getDatabasePerformance() { return []; }
    private function getApplicationPerformance() { return []; }
    private function getNetworkPerformance() { return []; }
    private function getStoragePerformance() { return []; }
    private function getPerformanceTrends() { return []; }
    private function getSecurityMetrics() { return []; }
    private function getRecentSecurityEvents() { return []; }
    private function getThreatAnalysis() { return []; }
    private function getAccessPatterns() { return []; }
    private function getAllSystemAlerts() { return []; }
    private function getAlertStatistics() { return []; }
    private function getAlertConfiguration() { return []; }
    private function handleAlertAction() { return []; }
}
