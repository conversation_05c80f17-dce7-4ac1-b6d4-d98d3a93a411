<?php
/**
 * Test Original Route
 * 
 * This script tests the original /super-admin/dashboard route
 */

// Start session
session_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

echo "<h1>Testing Original Route: /super-admin/dashboard</h1>";

try {
    // Create router instance
    $router = new \App\Core\Router();
    
    // Load routes
    require_once APP_ROOT . '/src/routes.php';
    
    echo "<p>✅ Router and routes loaded successfully</p>";
    
    // Set up super admin session
    $db = \App\Core\Database::getInstance();
    $superAdmin = $db->fetch("SELECT * FROM users WHERE role = 'super_admin' LIMIT 1");
    
    if ($superAdmin) {
        $_SESSION['user_id'] = $superAdmin['id'];
        $_SESSION['user'] = $superAdmin;
        $_SESSION['authenticated'] = true;
        echo "<p>✅ Super admin session created</p>";
    }
    
    // Test the exact route
    $testUri = '/super-admin/dashboard';
    $testMethod = 'GET';
    
    echo "<h2>Testing Route: {$testMethod} {$testUri}</h2>";
    
    // Simulate the exact same processing as index.php
    $requestUri = $testUri;
    
    // Remove query string from URI
    $requestUri = strtok($requestUri, '?');
    echo "<p><strong>After query removal:</strong> {$requestUri}</p>";
    
    // Remove base path if application is in subdirectory
    $scriptName = '/dms/public/index.php';
    $basePath = dirname($scriptName);
    
    // Handle case where app is in subdirectory like /dms/public/index.php
    if (strpos($scriptName, '/dms/public/index.php') !== false) {
        $basePath = '/dms';
    }
    
    echo "<p><strong>Base path:</strong> {$basePath}</p>";
    
    if ($basePath !== '/' && $basePath !== '' && strpos($requestUri, $basePath) === 0) {
        $requestUri = substr($requestUri, strlen($basePath));
    }
    
    echo "<p><strong>After base path removal:</strong> {$requestUri}</p>";
    
    // Ensure URI starts with /
    if (!$requestUri || $requestUri[0] !== '/') {
        $requestUri = '/' . $requestUri;
    }
    
    echo "<p><strong>Final processed URI:</strong> {$requestUri}</p>";
    
    // Now test the router
    echo "<h3>Router Test</h3>";
    
    try {
        ob_start();
        $router->handleRequest($testMethod, $requestUri);
        $output = ob_get_clean();
        
        if (!empty($output)) {
            echo "<div style='background: #d1ecf1; padding: 10px; border: 1px solid #bee5eb; margin: 10px 0;'>";
            echo "<h4>✅ Route Handled Successfully!</h4>";
            echo "<p><strong>Output length:</strong> " . strlen($output) . " characters</p>";
            echo "<h5>Output Preview:</h5>";
            echo "<div style='max-height: 400px; overflow: auto; background: white; padding: 10px; border: 1px solid #ccc;'>";
            echo "<pre>" . htmlspecialchars(substr($output, 0, 2000)) . "</pre>";
            echo "</div>";
            echo "</div>";
        } else {
            echo "<p>⚠️ Router handled request but returned no output</p>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "<h4>❌ Router Error</h4>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        echo "</div>";
    }
    
    // Test direct controller access
    echo "<h3>Direct Controller Test</h3>";
    
    try {
        $controller = new \App\Controllers\SuperAdminController();
        
        ob_start();
        $controller->dashboard();
        $output = ob_get_clean();
        
        if (!empty($output)) {
            echo "<div style='background: #d1ecf1; padding: 10px; border: 1px solid #bee5eb; margin: 10px 0;'>";
            echo "<h4>✅ Direct Controller Access Successful!</h4>";
            echo "<p><strong>Output length:</strong> " . strlen($output) . " characters</p>";
            echo "<h5>Output Preview:</h5>";
            echo "<div style='max-height: 400px; overflow: auto; background: white; padding: 10px; border: 1px solid #ccc;'>";
            echo "<pre>" . htmlspecialchars(substr($output, 0, 2000)) . "</pre>";
            echo "</div>";
            echo "</div>";
        } else {
            echo "<p>⚠️ Controller executed but returned no output</p>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "<h4>❌ Controller Error</h4>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        echo "</div>";
    }
    
    echo "<h2>Conclusion</h2>";
    echo "<p>If both tests above show output, then the routing system is working correctly.</p>";
    echo "<p>The issue with the original URL might be:</p>";
    echo "<ul>";
    echo "<li>Apache .htaccess rewrite rules</li>";
    echo "<li>Base path detection in index.php</li>";
    echo "<li>Authentication middleware blocking access</li>";
    echo "</ul>";
    
    echo "<h3>Working Alternatives</h3>";
    echo "<p>✅ <a href='/dms/super-admin.php'>Direct Super Admin Dashboard</a></p>";
    echo "<p>✅ <a href='/dms/login'>Login Page</a></p>";
    echo "<p>✅ <a href='/dms/dashboard'>Main Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h3>❌ ERROR</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}
?>
