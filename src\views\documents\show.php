<?php
$title = $document['title'] ?? 'Document Details';
ob_start();
?>

<!-- Document Details -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900"><?= e($document['title'] ?? 'Document Details') ?></h1>
                    <p class="mt-2 text-gray-600">Document information and details</p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?= url('/app/documents') ?>" 
                       class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded hover:bg-gray-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Documents
                    </a>
                    <?php if (!empty($document['file_path'])): ?>
                    <a href="<?= url('/app/documents/' . $document['id'] . '/download') ?>" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if (empty($document)): ?>
        <!-- Document Not Found -->
        <div class="bg-white rounded-lg border border-gray-200 p-12 text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Document Not Available</h3>
            <p class="text-gray-600 mb-6">The document you're looking for could not be found or may have been removed.</p>
            <div class="flex justify-center space-x-3">
                <a href="<?= url('/app/documents') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 transition-colors">
                    View All Documents
                </a>
                <a href="<?= url('/app/bundles') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded hover:bg-gray-700 transition-colors">
                    Browse Bundles
                </a>
            </div>
        </div>
        <?php else: ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Main Document Information -->
            <div class="lg:col-span-2 space-y-6">
                
                <!-- Document Details -->
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Document Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm font-medium text-gray-500">File Name</label>
                            <p class="text-sm text-gray-900 font-mono"><?= e($document['file_name'] ?? 'N/A') ?></p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Document Type</label>
                            <p class="text-sm text-gray-900"><?= ucfirst(str_replace('_', ' ', $document['document_type'] ?? 'Unknown')) ?></p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">File Size</label>
                            <p class="text-sm text-gray-900"><?= formatFileSize($document['file_size'] ?? 0) ?></p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Status</label>
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium
                                <?php
                                switch ($document['status'] ?? 'unknown') {
                                    case 'approved': echo 'bg-green-100 text-green-800'; break;
                                    case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                    case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                    default: echo 'bg-gray-100 text-gray-800';
                                }
                                ?>">
                                <?= ucfirst($document['status'] ?? 'Unknown') ?>
                            </span>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Created Date</label>
                            <p class="text-sm text-gray-900"><?= date('M j, Y g:i A', strtotime($document['created_at'])) ?></p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Version</label>
                            <p class="text-sm text-gray-900"><?= $document['version'] ?? '1.0' ?></p>
                        </div>
                    </div>
                    
                    <?php if (!empty($document['description'])): ?>
                    <div class="mt-4">
                        <label class="text-sm font-medium text-gray-500">Description</label>
                        <p class="text-sm text-gray-700 mt-1"><?= e($document['description']) ?></p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Bundle Information -->
                <?php if (!empty($document['bundle_id'])): ?>
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Bundle Information</h2>
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">Part of Bundle</p>
                            <p class="text-sm text-gray-600">Bundle ID: <?= $document['bundle_id'] ?></p>
                        </div>
                        <a href="<?= url('/app/bundles/' . $document['bundle_id']) ?>" 
                           class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 transition-colors">
                            View Bundle
                        </a>
                    </div>
                </div>
                <?php endif; ?>

            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                
                <!-- Location Information -->
                <?php if (!empty($location)): ?>
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Storage Location</h3>
                    <div class="space-y-3">
                        <?php if (!empty($location['location_name'])): ?>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Location</label>
                            <p class="text-sm text-gray-900"><?= e($location['location_name']) ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($location['warehouse_name'])): ?>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Warehouse</label>
                            <p class="text-sm text-gray-900"><?= e($location['warehouse_name']) ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($location['identifier'])): ?>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Identifier</label>
                            <p class="text-sm text-gray-900 font-mono"><?= e($location['identifier']) ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Document Versions -->
                <?php if (!empty($versions) && count($versions) > 0): ?>
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Version History</h3>
                    <div class="space-y-3">
                        <?php foreach ($versions as $version): ?>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <div>
                                <p class="text-sm font-medium text-gray-900">Version <?= $version['version'] ?? '1.0' ?></p>
                                <p class="text-xs text-gray-600"><?= date('M j, Y', strtotime($version['created_at'])) ?></p>
                            </div>
                            <?php if ($version['id'] == $document['id']): ?>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Current</span>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Related Documents -->
                <?php if (!empty($relatedDocuments) && count($relatedDocuments) > 0): ?>
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Documents</h3>
                    <div class="space-y-3">
                        <?php foreach ($relatedDocuments as $related): ?>
                        <div class="border-b border-gray-200 pb-3 last:border-b-0">
                            <a href="<?= url('/app/documents/' . $related['id']) ?>" 
                               class="text-sm font-medium text-blue-600 hover:text-blue-800">
                                <?= e($related['title']) ?>
                            </a>
                            <p class="text-xs text-gray-600 mt-1"><?= date('M j, Y', strtotime($related['created_at'])) ?></p>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

            </div>
        </div>
        
        <?php endif; ?>

</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
