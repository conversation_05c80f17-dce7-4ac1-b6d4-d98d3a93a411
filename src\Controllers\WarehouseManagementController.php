<?php

namespace App\Controllers;

/**
 * Warehouse Management Controller
 * 
 * Advanced warehouse management features for super admin
 * Provides 3D visualization, capacity management, and analytics
 */
class WarehouseManagementController extends BaseController
{
    /**
     * Warehouse Management Dashboard
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get warehouse overview statistics
            $warehouseStats = $this->getWarehouseStats();
            
            // Get capacity analytics
            $capacityData = $this->getCapacityAnalytics();
            
            // Get recent movements
            $recentMovements = $this->getRecentMovements();
            
            // Get efficiency metrics
            $efficiencyMetrics = $this->getEfficiencyMetrics();

            $this->view('super-admin/warehouse-management', [
                'title' => 'Warehouse Management',
                'warehouseStats' => $warehouseStats,
                'capacityData' => $capacityData,
                'recentMovements' => $recentMovements,
                'efficiencyMetrics' => $efficiencyMetrics
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading warehouse management: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/dashboard');
        }
    }

    /**
     * 3D Warehouse Layout Viewer
     */
    public function layout($warehouseId = null)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get all warehouses
            $warehouses = $this->getAllWarehouses();
            
            // Get specific warehouse if ID provided
            $selectedWarehouse = null;
            $layoutData = null;
            $boxLocations = [];
            
            if ($warehouseId) {
                $selectedWarehouse = $this->getWarehouseById($warehouseId);
                $layoutData = $this->getWarehouseLayout($warehouseId);
                $boxLocations = $this->getBoxLocations($warehouseId);
            }

            $this->view('super-admin/warehouse-layout', [
                'title' => '3D Warehouse Layout',
                'warehouses' => $warehouses,
                'selectedWarehouse' => $selectedWarehouse,
                'layoutData' => $layoutData,
                'boxLocations' => $boxLocations
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading warehouse layout: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/warehouse-management');
        }
    }

    /**
     * Capacity Analytics Dashboard
     */
    public function capacity()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get capacity trends
            $capacityTrends = $this->getCapacityTrends();
            
            // Get warehouse utilization
            $warehouseUtilization = $this->getWarehouseUtilization();
            
            // Get capacity forecasts
            $capacityForecasts = $this->getCapacityForecasts();
            
            // Get optimization suggestions
            $optimizationSuggestions = $this->getOptimizationSuggestions();

            $this->view('super-admin/warehouse-capacity', [
                'title' => 'Warehouse Capacity Analytics',
                'capacityTrends' => $capacityTrends,
                'warehouseUtilization' => $warehouseUtilization,
                'capacityForecasts' => $capacityForecasts,
                'optimizationSuggestions' => $optimizationSuggestions
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading capacity analytics: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/warehouse-management');
        }
    }

    /**
     * Box Movement Tracking
     */
    public function movements()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get movement history
            $movements = $this->getMovementHistory();
            
            // Get movement statistics
            $movementStats = $this->getMovementStats();
            
            // Get active movements
            $activeMovements = $this->getActiveMovements();

            $this->view('super-admin/warehouse-movements', [
                'title' => 'Box Movement Tracking',
                'movements' => $movements,
                'movementStats' => $movementStats,
                'activeMovements' => $activeMovements
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading movement tracking: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/warehouse-management');
        }
    }

    /**
     * Efficiency Reports
     */
    public function efficiency()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get efficiency metrics
            $efficiencyMetrics = $this->getDetailedEfficiencyMetrics();
            
            // Get productivity data
            $productivityData = $this->getProductivityData();
            
            // Get bottleneck analysis
            $bottleneckAnalysis = $this->getBottleneckAnalysis();

            $this->view('super-admin/warehouse-efficiency', [
                'title' => 'Warehouse Efficiency Reports',
                'efficiencyMetrics' => $efficiencyMetrics,
                'productivityData' => $productivityData,
                'bottleneckAnalysis' => $bottleneckAnalysis
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading efficiency reports: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/warehouse-management');
        }
    }

    /**
     * Get warehouse overview statistics
     */
    private function getWarehouseStats()
    {
        return $this->db->fetch(
            "SELECT 
                COUNT(DISTINCT w.id) as total_warehouses,
                COUNT(DISTINCT b.id) as total_boxes,
                COUNT(DISTINCT CASE WHEN b.status = 'occupied' THEN b.id END) as occupied_boxes,
                COUNT(DISTINCT bun.id) as total_bundles,
                COUNT(DISTINCT d.id) as total_documents,
                ROUND(AVG(w.capacity_percentage), 2) as avg_capacity_usage,
                COUNT(DISTINCT CASE WHEN w.status = 'active' THEN w.id END) as active_warehouses
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             LEFT JOIN bundles bun ON b.id = bun.box_id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.status = 'active'",
            []
        );
    }

    /**
     * Get capacity analytics data
     */
    private function getCapacityAnalytics()
    {
        return $this->db->fetchAll(
            "SELECT 
                w.name as warehouse_name,
                w.capacity_percentage,
                COUNT(b.id) as total_boxes,
                COUNT(CASE WHEN b.status = 'occupied' THEN 1 END) as occupied_boxes,
                COUNT(CASE WHEN b.status = 'empty' THEN 1 END) as empty_boxes,
                w.total_capacity,
                w.used_capacity
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             WHERE w.status = 'active'
             GROUP BY w.id
             ORDER BY w.capacity_percentage DESC",
            []
        );
    }

    /**
     * Get recent box movements
     */
    private function getRecentMovements()
    {
        return $this->db->fetchAll(
            "SELECT 
                bm.*,
                b.box_id,
                b.name as box_name,
                u.first_name,
                u.last_name,
                w.name as warehouse_name
             FROM box_movements bm
             JOIN boxes b ON bm.box_id = b.id
             JOIN users u ON bm.moved_by = u.id
             JOIN warehouses w ON b.warehouse_id = w.id
             ORDER BY bm.created_at DESC
             LIMIT 20",
            []
        );
    }

    /**
     * Get efficiency metrics
     */
    private function getEfficiencyMetrics()
    {
        return $this->db->fetch(
            "SELECT 
                COUNT(DISTINCT bm.id) as total_movements_today,
                AVG(TIMESTAMPDIFF(MINUTE, bm.created_at, NOW())) as avg_movement_time,
                COUNT(DISTINCT CASE WHEN DATE(bm.created_at) = CURDATE() THEN bm.moved_by END) as active_staff_today,
                COUNT(DISTINCT CASE WHEN bm.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN bm.id END) as movements_last_hour
             FROM box_movements bm
             WHERE DATE(bm.created_at) = CURDATE()",
            []
        );
    }

    /**
     * Get all warehouses
     */
    private function getAllWarehouses()
    {
        return $this->db->fetchAll(
            "SELECT * FROM warehouses WHERE status = 'active' ORDER BY name",
            []
        );
    }

    /**
     * Get warehouse by ID
     */
    private function getWarehouseById($id)
    {
        return $this->db->fetch(
            "SELECT * FROM warehouses WHERE id = ? AND status = 'active'",
            [$id]
        );
    }

    /**
     * Get warehouse layout data
     */
    private function getWarehouseLayout($warehouseId)
    {
        $layout = $this->db->fetch(
            "SELECT layout_data FROM warehouse_layouts WHERE warehouse_id = ? ORDER BY created_at DESC LIMIT 1",
            [$warehouseId]
        );
        
        return $layout ? json_decode($layout['layout_data'], true) : null;
    }

    /**
     * Get box locations in warehouse
     */
    private function getBoxLocations($warehouseId)
    {
        return $this->db->fetchAll(
            "SELECT 
                b.id,
                b.box_id,
                b.name,
                b.storage_location_code,
                b.row_number,
                b.shelf_number,
                b.position_number,
                b.status,
                COUNT(bun.id) as bundle_count,
                COUNT(d.id) as document_count
             FROM boxes b
             LEFT JOIN bundles bun ON b.id = bun.box_id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE b.warehouse_id = ?
             GROUP BY b.id
             ORDER BY b.row_number, b.shelf_number, b.position_number",
            [$warehouseId]
        );
    }

    /**
     * Additional helper methods for advanced analytics
     */
    private function getCapacityTrends()
    {
        // Implementation for capacity trend analysis
        return [];
    }

    private function getWarehouseUtilization()
    {
        // Implementation for warehouse utilization metrics
        return [];
    }

    private function getCapacityForecasts()
    {
        // Implementation for capacity forecasting
        return [];
    }

    private function getOptimizationSuggestions()
    {
        // Implementation for optimization suggestions
        return [];
    }

    private function getMovementHistory()
    {
        // Implementation for movement history
        return [];
    }

    private function getMovementStats()
    {
        // Implementation for movement statistics
        return [];
    }

    private function getActiveMovements()
    {
        // Implementation for active movements
        return [];
    }

    private function getDetailedEfficiencyMetrics()
    {
        // Implementation for detailed efficiency metrics
        return [];
    }

    private function getProductivityData()
    {
        // Implementation for productivity data
        return [];
    }

    private function getBottleneckAnalysis()
    {
        // Implementation for bottleneck analysis
        return [];
    }
}
