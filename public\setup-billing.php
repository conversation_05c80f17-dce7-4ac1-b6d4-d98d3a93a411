<?php
/**
 * Web-based Billing Module Setup
 * 
 * This page allows you to run the billing migration through a web browser
 */

// Security check - only allow from localhost or if specifically enabled
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';

if (!in_array($clientIP, $allowedIPs) && !isset($_GET['allow_remote'])) {
    die('Access denied. This setup page can only be accessed from localhost for security reasons.');
}

require_once __DIR__ . '/../src/autoload.php';
require_once __DIR__ . '/../src/config/database.php';

use App\Core\Database;

$output = [];
$success = false;
$error = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    try {
        $output[] = "Starting Billing Module Migration...";
        
        $db = Database::getInstance();

        // Define SQL statements directly
        $statements = [
            // Create billing_rates table
            "CREATE TABLE IF NOT EXISTS billing_rates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                service_code VARCHAR(50) UNIQUE NOT NULL,
                service_name VARCHAR(255) NOT NULL,
                service_description TEXT,
                rate DECIMAL(10,2) NOT NULL,
                unit VARCHAR(20) DEFAULT 'each',
                category VARCHAR(50) NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_billing_rates_code (service_code),
                INDEX idx_billing_rates_category (category),
                INDEX idx_billing_rates_active (is_active)
            )",

            // Create billing_events table
            "CREATE TABLE IF NOT EXISTS billing_events (
                id INT AUTO_INCREMENT PRIMARY KEY,
                company_id INT NOT NULL,
                client_id INT NOT NULL,
                service_code VARCHAR(50) NOT NULL,
                event_type VARCHAR(100) NOT NULL,
                quantity DECIMAL(10,2) DEFAULT 1.00,
                unit_rate DECIMAL(10,2) NOT NULL,
                total_amount DECIMAL(10,2) NOT NULL,
                entity_type VARCHAR(50),
                entity_id INT,
                reference_number VARCHAR(100),
                billing_status ENUM('pending', 'invoiced', 'paid', 'disputed', 'cancelled') DEFAULT 'pending',
                invoice_id INT NULL,
                performed_by INT NOT NULL,
                performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                metadata JSON,
                INDEX idx_billing_events_company (company_id),
                INDEX idx_billing_events_client (client_id),
                INDEX idx_billing_events_service (service_code),
                INDEX idx_billing_events_date (performed_at),
                INDEX idx_billing_events_status (billing_status),
                INDEX idx_billing_events_invoice (invoice_id)
            )",

            // Create billing_invoices table
            "CREATE TABLE IF NOT EXISTS billing_invoices (
                id INT AUTO_INCREMENT PRIMARY KEY,
                company_id INT NOT NULL,
                client_id INT NOT NULL,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE NOT NULL,
                billing_period_start DATE NOT NULL,
                billing_period_end DATE NOT NULL,
                subtotal DECIMAL(12,2) NOT NULL DEFAULT 0.00,
                tax_rate DECIMAL(5,4) DEFAULT 0.0000,
                tax_amount DECIMAL(12,2) DEFAULT 0.00,
                total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
                payment_status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled', 'disputed') DEFAULT 'draft',
                payment_date DATE NULL,
                payment_method VARCHAR(50) NULL,
                payment_reference VARCHAR(100) NULL,
                line_items JSON NOT NULL,
                invoice_notes TEXT,
                pdf_file_path VARCHAR(500),
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                sent_at TIMESTAMP NULL,
                INDEX idx_billing_invoices_company (company_id),
                INDEX idx_billing_invoices_client (client_id),
                INDEX idx_billing_invoices_number (invoice_number),
                INDEX idx_billing_invoices_date (invoice_date),
                INDEX idx_billing_invoices_status (payment_status),
                INDEX idx_billing_invoices_period (billing_period_start, billing_period_end)
            )",

            // Create billing_payments table
            "CREATE TABLE IF NOT EXISTS billing_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                invoice_id INT NOT NULL,
                company_id INT NOT NULL,
                client_id INT NOT NULL,
                payment_amount DECIMAL(12,2) NOT NULL,
                payment_date DATE NOT NULL,
                payment_method VARCHAR(50) NOT NULL,
                payment_reference VARCHAR(100),
                bank_reference VARCHAR(100),
                transaction_id VARCHAR(100),
                payment_status ENUM('pending', 'cleared', 'bounced', 'refunded') DEFAULT 'pending',
                notes TEXT,
                recorded_by INT NOT NULL,
                recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_billing_payments_invoice (invoice_id),
                INDEX idx_billing_payments_client (client_id),
                INDEX idx_billing_payments_date (payment_date),
                INDEX idx_billing_payments_status (payment_status)
            )"
        ];

        $output[] = "Creating " . count($statements) . " billing tables...";

        foreach ($statements as $index => $statement) {
            try {
                $db->execute($statement);
                $output[] = "✓ Table " . ($index + 1) . " created successfully";
            } catch (Exception $e) {
                // Check if it's a "table already exists" error - we can ignore those
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    $output[] = "⚠ Table " . ($index + 1) . " already exists";
                } else {
                    throw new Exception("Error creating table " . ($index + 1) . ": " . $e->getMessage());
                }
            }
        }
        
        // Insert billing rates
        $output[] = "";
        $output[] = "Inserting default billing rates...";

        $billingRates = [
            ['intake.new', 'New Intake', 'Processing new document intake request', 80.00, 'each', 'processing'],
            ['bundle.handling', 'Bundle/File Handling', 'Processing and organizing document bundles', 5.00, 'each', 'processing'],
            ['box.handling', 'Box Handling', 'Physical box processing and management', 5.00, 'each', 'processing'],
            ['box.packing', 'Box Packing/Repacking', 'Packing or repacking documents in boxes', 10.00, 'each', 'processing'],
            ['box.registration', 'Box Registration (Indexing)', 'Registering and indexing box contents', 2.00, 'each', 'processing'],
            ['search.box', 'Search Box', 'Searching for specific boxes', 5.00, 'each', 'search'],
            ['search.bundle', 'Search Bundle/File', 'Searching for specific bundles or files', 5.00, 'each', 'search'],
            ['delivery.to_client', 'Deliver to Client', 'Delivery of boxes/bundles/files to client', 80.00, 'each', 'delivery'],
            ['collection.from_client', 'Collect from Client', 'Collection of boxes/bundles/files from client', 80.00, 'each', 'delivery'],
            ['storage.small_box', 'Small Box Storage', 'Monthly storage fee for small boxes', 1.00, 'month', 'storage'],
            ['storage.medium_box', 'Medium Box Storage', 'Monthly storage fee for medium boxes', 1.50, 'month', 'storage'],
            ['storage.large_box', 'Large Box Storage', 'Monthly storage fee for large boxes', 2.00, 'month', 'storage'],
            ['barcode.box', 'Box Barcode', 'Generate barcode for box', 0.20, 'each', 'barcode'],
            ['barcode.bundle', 'Bundle Barcode', 'Generate barcode for bundle', 0.15, 'each', 'barcode'],
            ['withdrawal.permanent', 'Permanent Withdrawal', 'Permanent removal of documents from storage', 10.00, 'each', 'special'],
            ['destruction.per_kg', 'Document Destruction', 'Secure destruction of documents', 3.00, 'kg', 'special'],
            ['supply.double_box', 'Double Size Document Box', 'Large capacity document storage box', 14.00, 'each', 'supplies'],
            ['supply.double_box_lid', 'Lid For Double Size Document Box', 'Protective lid for double size box', 4.00, 'each', 'supplies'],
            ['supply.drawing_roll', 'Drawing Roll', 'Storage tube for architectural drawings', 8.00, 'each', 'supplies'],
            ['supply.lever_arch', 'Lever Arch Size Box', 'Lever arch file storage box', 6.00, 'each', 'supplies'],
            ['supply.half_box', 'Half Size Document Box', 'Compact document storage box', 8.00, 'each', 'supplies'],
            ['supply.half_box_lid', 'Lid For Half Size Document Box', 'Protective lid for half size box', 2.50, 'each', 'supplies'],
            ['supply.large_box', 'Large Size Document Box', 'Large capacity document storage box', 12.00, 'each', 'supplies'],
            ['supply.large_box_lid', 'Lid For Large Size Document Box', 'Protective lid for large size box', 3.50, 'each', 'supplies'],
            ['digital.email', 'Email Document', 'Digital document delivery via email', 2.00, 'each', 'digital'],
            ['digital.cd_production', 'CD Production', 'Burn documents to CD/DVD media', 15.00, 'each', 'digital']
        ];

        $insertedCount = 0;
        foreach ($billingRates as $rate) {
            try {
                // Check if rate already exists
                $existing = $db->fetch("SELECT id FROM billing_rates WHERE service_code = ?", [$rate[0]]);
                if (!$existing) {
                    $db->execute(
                        "INSERT INTO billing_rates (service_code, service_name, service_description, rate, unit, category) VALUES (?, ?, ?, ?, ?, ?)",
                        $rate
                    );
                    $insertedCount++;
                }
            } catch (Exception $e) {
                $output[] = "⚠ Error inserting rate {$rate[0]}: " . $e->getMessage();
            }
        }

        $output[] = "✓ Inserted {$insertedCount} billing rates";

        // Verify tables were created
        $output[] = "";
        $output[] = "Verifying billing tables...";
        
        $requiredTables = [
            'billing_rates',
            'billing_events', 
            'billing_invoices',
            'billing_payments'
        ];
        
        foreach ($requiredTables as $table) {
            $exists = $db->fetch("SHOW TABLES LIKE '{$table}'");
            if ($exists) {
                $output[] = "✓ Table '{$table}' exists";
            } else {
                throw new Exception("Table '{$table}' was not created");
            }
        }
        
        // Check if billing rates were inserted
        $rateCount = $db->fetch("SELECT COUNT(*) as count FROM billing_rates");
        $output[] = "✓ Billing rates table has {$rateCount['count']} service rates";
        
        // Display some sample rates
        $output[] = "";
        $output[] = "Sample billing rates:";
        $sampleRates = $db->fetchAll(
            "SELECT service_code, service_name, rate, unit, category 
             FROM billing_rates 
             WHERE is_active = 1 
             ORDER BY category, service_name 
             LIMIT 10"
        );
        
        foreach ($sampleRates as $rate) {
            $output[] = sprintf(
                "  - %s: $%s per %s (%s)",
                $rate['service_name'],
                number_format($rate['rate'], 2),
                $rate['unit'],
                $rate['category']
            );
        }
        
        $output[] = "";
        $output[] = "✅ Billing Module Migration completed successfully!";
        $output[] = "";
        $output[] = "Next steps:";
        $output[] = "1. Access the billing dashboard at: /app/billing";
        $output[] = "2. Review and adjust service rates if needed";
        $output[] = "3. Start logging billing events in your application";
        $output[] = "4. Generate your first invoice at month-end";
        
        $success = true;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        $output[] = "";
        $output[] = "❌ Migration failed: " . $error;
        $output[] = "Please check your database connection and try again.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Billing Module Setup - DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .output-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-line;
        }
        .success { color: #198754; }
        .warning { color: #fd7e14; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database"></i> Billing Module Setup
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (!$success && !$error): ?>
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> Ready to Setup Billing Module</h5>
                                <p>This will create the necessary database tables and insert default billing rates for your document management system.</p>
                                <p><strong>What this will do:</strong></p>
                                <ul>
                                    <li>Create billing_rates table with 25+ predefined service rates</li>
                                    <li>Create billing_events table for tracking billable activities</li>
                                    <li>Create billing_invoices table for invoice management</li>
                                    <li>Create billing_payments table for payment tracking</li>
                                </ul>
                            </div>

                            <form method="POST">
                                <div class="d-grid">
                                    <button type="submit" name="run_migration" class="btn btn-primary btn-lg">
                                        <i class="fas fa-play"></i> Run Billing Module Setup
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> Setup Completed Successfully!</h5>
                                <p>The billing module has been installed and configured.</p>
                            </div>

                            <div class="mb-3">
                                <h6>Quick Links:</h6>
                                <a href="/app/billing" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-tachometer-alt"></i> Billing Dashboard
                                </a>
                                <a href="/app/billing/rates" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-list"></i> Service Rates
                                </a>
                                <a href="../docs/BILLING_MODULE_IMPLEMENTATION.md" class="btn btn-outline-info">
                                    <i class="fas fa-book"></i> Documentation
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle"></i> Setup Failed</h5>
                                <p><strong>Error:</strong> <?= htmlspecialchars($error) ?></p>
                                <p>Please check your database configuration and try again.</p>
                            </div>

                            <div class="d-grid">
                                <a href="<?= $_SERVER['PHP_SELF'] ?>" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> Try Again
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($output)): ?>
                            <div class="mt-4">
                                <h6>Setup Output:</h6>
                                <div class="output-box">
                                    <?php foreach ($output as $line): ?>
                                        <?php
                                        $class = '';
                                        if (strpos($line, '✓') === 0) $class = 'success';
                                        elseif (strpos($line, '⚠') === 0) $class = 'warning';
                                        elseif (strpos($line, '❌') === 0) $class = 'error';
                                        ?>
                                        <div class="<?= $class ?>"><?= htmlspecialchars($line) ?></div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mt-4 pt-3 border-top">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt"></i> 
                                This setup page is only accessible from localhost for security reasons.
                                After setup is complete, you can safely delete this file.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
