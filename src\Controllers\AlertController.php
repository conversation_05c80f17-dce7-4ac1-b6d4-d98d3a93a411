<?php

namespace App\Controllers;

/**
 * Alert Controller
 * 
 * Manages automated alerts for retention periods, storage limits, etc.
 * Implements automated alert system from documentation
 */
class AlertController extends BaseController
{
    /**
     * Display alerts dashboard
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager']);
        
        try {
            // Get filter parameters
            $type = $_GET['type'] ?? '';
            $severity = $_GET['severity'] ?? '';
            $status = $_GET['status'] ?? '';
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 20;
            $offset = ($page - 1) * $limit;

            // Build query
            $where = ["company_id = ?"];
            $params = [$this->user['company_id']];

            if (!empty($type)) {
                $where[] = "alert_type = ?";
                $params[] = $type;
            }

            if (!empty($severity)) {
                $where[] = "severity = ?";
                $params[] = $severity;
            }

            if (!empty($status)) {
                $where[] = "status = ?";
                $params[] = $status;
            }

            $whereClause = implode(' AND ', $where);

            // Get alerts
            $alerts = $this->db->fetchAll(
                "SELECT a.*, 
                        recipient.first_name as recipient_first_name, 
                        recipient.last_name as recipient_last_name
                 FROM automated_alerts a
                 LEFT JOIN users recipient ON a.recipient_user_id = recipient.id
                 WHERE {$whereClause}
                 ORDER BY a.alert_date DESC, a.severity DESC
                 LIMIT ? OFFSET ?",
                array_merge($params, [$limit, $offset])
            );

            // Get total count
            $totalCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM automated_alerts WHERE {$whereClause}",
                $params
            );

            // Get alert statistics
            $stats = $this->getAlertStats();

            $this->view('alerts/index', [
                'title' => 'Automated Alerts',
                'alerts' => $alerts,
                'totalCount' => $totalCount,
                'currentPage' => $page,
                'totalPages' => ceil($totalCount / $limit),
                'stats' => $stats,
                'filters' => [
                    'type' => $type,
                    'severity' => $severity,
                    'status' => $status
                ]
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading alerts: ' . $e->getMessage(), 'error');
            $this->redirect('/dashboard');
        }
    }

    /**
     * Show alert details
     */
    public function show($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager', 'editor']);
        
        try {
            $alert = $this->getAlertById($id);
            if (!$alert) {
                throw new \Exception('Alert not found');
            }

            // Get target details
            $targetDetails = $this->getAlertTargetDetails($alert['target_type'], $alert['target_id']);

            $this->view('alerts/show', [
                'title' => 'Alert Details',
                'alert' => $alert,
                'targetDetails' => $targetDetails
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading alert: ' . $e->getMessage(), 'error');
            $this->redirect('/app/alerts');
        }
    }

    /**
     * Acknowledge alert
     */
    public function acknowledge($id)
    {
        $this->requireAuth();
        
        try {
            $alert = $this->getAlertById($id);
            if (!$alert) {
                throw new \Exception('Alert not found');
            }

            if ($alert['status'] === 'acknowledged') {
                throw new \Exception('Alert is already acknowledged');
            }

            // Update alert
            $this->db->execute(
                "UPDATE automated_alerts SET 
                 status = 'acknowledged', 
                 acknowledged_at = NOW(),
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Log activity
            $this->logActivity('acknowledge', 'alert', $id, "Acknowledged alert: {$alert['title']}");

            $this->setFlashMessage('Alert acknowledged', 'success');
            
            if (isAjax()) {
                $this->jsonResponse(['success' => true, 'message' => 'Alert acknowledged']);
            } else {
                $this->redirect("/app/alerts/{$id}");
            }

        } catch (\Exception $e) {
            if (isAjax()) {
                $this->jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                $this->setFlashMessage('Failed to acknowledge alert: ' . $e->getMessage(), 'error');
                $this->redirect("/app/alerts/{$id}");
            }
        }
    }

    /**
     * Resolve alert
     */
    public function resolve($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager']);
        
        try {
            $alert = $this->getAlertById($id);
            if (!$alert) {
                throw new \Exception('Alert not found');
            }

            if ($alert['status'] === 'resolved') {
                throw new \Exception('Alert is already resolved');
            }

            // Update alert
            $this->db->execute(
                "UPDATE automated_alerts SET 
                 status = 'resolved', 
                 resolved_at = NOW(),
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Log activity
            $this->logActivity('resolve', 'alert', $id, "Resolved alert: {$alert['title']}");

            $this->setFlashMessage('Alert resolved', 'success');
            
            if (isAjax()) {
                $this->jsonResponse(['success' => true, 'message' => 'Alert resolved']);
            } else {
                $this->redirect("/app/alerts/{$id}");
            }

        } catch (\Exception $e) {
            if (isAjax()) {
                $this->jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                $this->setFlashMessage('Failed to resolve alert: ' . $e->getMessage(), 'error');
                $this->redirect("/app/alerts/{$id}");
            }
        }
    }

    /**
     * Generate retention alerts (called by cron job)
     */
    public function generateRetentionAlerts()
    {
        try {
            $alertsGenerated = 0;

            // Find bundles with retention periods due in 30 days
            $bundlesDue = $this->db->fetchAll(
                "SELECT b.*, c.name as company_name
                 FROM bundles b
                 JOIN companies c ON b.company_id = c.id
                 WHERE b.status = 'active' 
                 AND b.retention_period IS NOT NULL
                 AND DATE_ADD(b.created_at, INTERVAL b.retention_period YEAR) <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                 AND DATE_ADD(b.created_at, INTERVAL b.retention_period YEAR) > CURDATE()
                 AND NOT EXISTS (
                     SELECT 1 FROM automated_alerts 
                     WHERE target_type = 'bundle' 
                     AND target_id = b.id 
                     AND alert_type = 'retention_due'
                     AND status IN ('pending', 'sent')
                 )",
                []
            );

            foreach ($bundlesDue as $bundle) {
                $dueDate = date('Y-m-d', strtotime($bundle['created_at'] . ' + ' . $bundle['retention_period'] . ' years'));
                $daysUntilDue = ceil((strtotime($dueDate) - time()) / (60 * 60 * 24));

                $this->db->execute(
                    "INSERT INTO automated_alerts (
                        alert_type, company_id, target_type, target_id, target_reference,
                        title, message, severity, alert_date, due_date, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        'retention_due',
                        $bundle['company_id'],
                        'bundle',
                        $bundle['id'],
                        $bundle['reference_number'],
                        'Retention Period Due',
                        "Bundle {$bundle['reference_number']} retention period expires in {$daysUntilDue} days",
                        $daysUntilDue <= 7 ? 'critical' : 'warning',
                        date('Y-m-d'),
                        $dueDate,
                        'pending'
                    ]
                );

                $alertsGenerated++;
            }

            // Find overdue bundles
            $bundlesOverdue = $this->db->fetchAll(
                "SELECT b.*, c.name as company_name
                 FROM bundles b
                 JOIN companies c ON b.company_id = c.id
                 WHERE b.status = 'active' 
                 AND b.retention_period IS NOT NULL
                 AND DATE_ADD(b.created_at, INTERVAL b.retention_period YEAR) < CURDATE()
                 AND NOT EXISTS (
                     SELECT 1 FROM automated_alerts 
                     WHERE target_type = 'bundle' 
                     AND target_id = b.id 
                     AND alert_type = 'retention_overdue'
                     AND status IN ('pending', 'sent')
                 )",
                []
            );

            foreach ($bundlesOverdue as $bundle) {
                $dueDate = date('Y-m-d', strtotime($bundle['created_at'] . ' + ' . $bundle['retention_period'] . ' years'));
                $daysOverdue = ceil((time() - strtotime($dueDate)) / (60 * 60 * 24));

                $this->db->execute(
                    "INSERT INTO automated_alerts (
                        alert_type, company_id, target_type, target_id, target_reference,
                        title, message, severity, alert_date, due_date, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        'retention_overdue',
                        $bundle['company_id'],
                        'bundle',
                        $bundle['id'],
                        $bundle['reference_number'],
                        'Retention Period Overdue',
                        "Bundle {$bundle['reference_number']} retention period is {$daysOverdue} days overdue",
                        'critical',
                        date('Y-m-d'),
                        $dueDate,
                        'pending'
                    ]
                );

                $alertsGenerated++;
            }

            echo "Generated {$alertsGenerated} retention alerts\n";
            return $alertsGenerated;

        } catch (\Exception $e) {
            echo "Error generating retention alerts: " . $e->getMessage() . "\n";
            return 0;
        }
    }

    /**
     * Generate storage limit alerts
     */
    public function generateStorageAlerts()
    {
        try {
            $alertsGenerated = 0;

            // Find companies approaching storage limits
            $companies = $this->db->fetchAll(
                "SELECT c.*, 
                        (c.storage_used / c.storage_limit * 100) as usage_percentage
                 FROM companies c
                 WHERE c.status = 'active'
                 AND c.storage_limit > 0
                 AND (c.storage_used / c.storage_limit * 100) >= 80
                 AND NOT EXISTS (
                     SELECT 1 FROM automated_alerts 
                     WHERE target_type = 'company' 
                     AND target_id = c.id 
                     AND alert_type = 'storage_limit'
                     AND status IN ('pending', 'sent')
                     AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                 )",
                []
            );

            foreach ($companies as $company) {
                $severity = $company['usage_percentage'] >= 95 ? 'critical' : 'warning';
                
                $this->db->execute(
                    "INSERT INTO automated_alerts (
                        alert_type, company_id, target_type, target_id, target_reference,
                        title, message, severity, alert_date, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        'storage_limit',
                        $company['id'],
                        'company',
                        $company['id'],
                        'COMPANY-' . str_pad($company['id'], 3, '0', STR_PAD_LEFT),
                        'Storage Limit Warning',
                        "Company storage usage is at {$company['usage_percentage']}% capacity",
                        $severity,
                        date('Y-m-d'),
                        'pending'
                    ]
                );

                $alertsGenerated++;
            }

            echo "Generated {$alertsGenerated} storage alerts\n";
            return $alertsGenerated;

        } catch (\Exception $e) {
            echo "Error generating storage alerts: " . $e->getMessage() . "\n";
            return 0;
        }
    }

    /**
     * Get alert by ID
     */
    private function getAlertById($id)
    {
        return $this->db->fetch(
            "SELECT a.*, 
                    recipient.first_name as recipient_first_name, 
                    recipient.last_name as recipient_last_name,
                    recipient.email as recipient_email
             FROM automated_alerts a
             LEFT JOIN users recipient ON a.recipient_user_id = recipient.id
             WHERE a.id = ? AND a.company_id = ?",
            [$id, $this->user['company_id']]
        );
    }

    /**
     * Get alert statistics
     */
    private function getAlertStats()
    {
        $stats = [];

        try {
            // Total alerts
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM automated_alerts WHERE company_id = ?",
                [$this->user['company_id']]
            );
            $stats['total'] = $result['count'] ?? 0;

            // Pending alerts
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM automated_alerts WHERE company_id = ? AND status = 'pending'",
                [$this->user['company_id']]
            );
            $stats['pending'] = $result['count'] ?? 0;

            // Critical alerts
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM automated_alerts WHERE company_id = ? AND severity = 'critical' AND status IN ('pending', 'sent')",
                [$this->user['company_id']]
            );
            $stats['critical'] = $result['count'] ?? 0;

            // Alerts by type
            $alertsByType = $this->db->fetchAll(
                "SELECT alert_type, COUNT(*) as count 
                 FROM automated_alerts 
                 WHERE company_id = ? AND status IN ('pending', 'sent')
                 GROUP BY alert_type",
                [$this->user['company_id']]
            );
            $stats['by_type'] = [];
            foreach ($alertsByType as $row) {
                $stats['by_type'][$row['alert_type']] = $row['count'];
            }

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }
}
