<?php
$title = 'Create New Company';
ob_start();
?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Create New Company</h1>
                <p class="text-gray-600 mt-1">Add a new company to the document management system</p>
            </div>
            <a href="<?= url('/super-admin/dashboard') ?>" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-2xl shadow-lg p-8">
        <form method="POST" action="<?= url('/app/companies') ?>" class="space-y-8">
            
            <!-- Company Information -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Company Name *</label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               required 
                               value="<?= e($_POST['name'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter company name">
                    </div>

                    <div>
                        <label for="domain" class="block text-sm font-medium text-gray-700 mb-2">Domain</label>
                        <input type="text" 
                               id="domain" 
                               name="domain" 
                               value="<?= e($_POST['domain'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="company.com">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               required 
                               value="<?= e($_POST['email'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               value="<?= e($_POST['phone'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="+****************">
                    </div>

                    <div class="md:col-span-2">
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <textarea id="address" 
                                  name="address" 
                                  rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Enter company address"><?= e($_POST['address'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Subscription Plan -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription Plan</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label for="subscription_plan" class="block text-sm font-medium text-gray-700 mb-2">Plan *</label>
                        <select id="subscription_plan" 
                                name="subscription_plan" 
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select a plan</option>
                            <option value="basic" <?= ($_POST['subscription_plan'] ?? '') === 'basic' ? 'selected' : '' ?>>Basic</option>
                            <option value="premium" <?= ($_POST['subscription_plan'] ?? '') === 'premium' ? 'selected' : '' ?>>Premium</option>
                            <option value="enterprise" <?= ($_POST['subscription_plan'] ?? '') === 'enterprise' ? 'selected' : '' ?>>Enterprise</option>
                        </select>
                    </div>

                    <div>
                        <label for="storage_limit" class="block text-sm font-medium text-gray-700 mb-2">Storage Limit (GB)</label>
                        <input type="number" 
                               id="storage_limit" 
                               name="storage_limit" 
                               min="1"
                               value="<?= e($_POST['storage_limit'] ?? '5') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="5">
                        <p class="text-sm text-gray-500 mt-1">Storage limit in gigabytes</p>
                    </div>
                </div>
            </div>

            <!-- Company Admin -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Administrator</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label for="admin_first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                        <input type="text" 
                               id="admin_first_name" 
                               name="admin_first_name" 
                               required 
                               value="<?= e($_POST['admin_first_name'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="John">
                    </div>

                    <div>
                        <label for="admin_last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                        <input type="text" 
                               id="admin_last_name" 
                               name="admin_last_name" 
                               required 
                               value="<?= e($_POST['admin_last_name'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Doe">
                    </div>

                    <div>
                        <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <input type="email" 
                               id="admin_email" 
                               name="admin_email" 
                               required 
                               value="<?= e($_POST['admin_email'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="admin_username" class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                        <input type="text" 
                               id="admin_username" 
                               name="admin_username" 
                               required 
                               value="<?= e($_POST['admin_username'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="admin">
                    </div>

                    <div class="md:col-span-2">
                        <label for="admin_password" class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                        <input type="password" 
                               id="admin_password" 
                               name="admin_password" 
                               required 
                               minlength="8"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Minimum 8 characters">
                        <p class="text-sm text-gray-500 mt-1">Password must be at least 8 characters long</p>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="<?= url('/super-admin/dashboard') ?>" 
                   class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Create Company
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-fill storage limit based on plan selection
document.getElementById('subscription_plan').addEventListener('change', function() {
    const storageInput = document.getElementById('storage_limit');
    const planLimits = {
        'basic': 5,
        'premium': 50,
        'enterprise': 500
    };
    
    if (planLimits[this.value]) {
        storageInput.value = planLimits[this.value];
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
