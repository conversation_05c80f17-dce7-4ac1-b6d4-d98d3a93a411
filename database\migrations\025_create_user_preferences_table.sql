-- Create user_preferences table for storing user settings and preferences
CREATE TABLE IF NOT EXISTS user_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    preference_key VARCHAR(100) NOT NULL,
    preference_value TEXT,
    created_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preference (user_id, preference_key),
    INDEX idx_user_preferences (user_id),
    INDEX idx_preference_key (preference_key)
);

-- Insert default notification preferences for existing users
INSERT IGNORE INTO user_preferences (user_id, preference_key, preference_value)
SELECT 
    id as user_id,
    'email_notifications' as preference_key,
    '1' as preference_value
FROM users
WHERE status = 'active';

INSERT IGNORE INTO user_preferences (user_id, preference_key, preference_value)
SELECT 
    id as user_id,
    'document_alerts' as preference_key,
    '1' as preference_value
FROM users
WHERE status = 'active';

INSERT IGNORE INTO user_preferences (user_id, preference_key, preference_value)
SELECT 
    id as user_id,
    'system_updates' as preference_key,
    '1' as preference_value
FROM users
WHERE status = 'active';

INSERT IGNORE INTO user_preferences (user_id, preference_key, preference_value)
SELECT 
    id as user_id,
    'weekly_reports' as preference_key,
    '0' as preference_value
FROM users
WHERE status = 'active';
