-- Update warehouses table to match controller expectations
-- Add missing columns for address details and contact information

-- Add missing address columns
ALTER TABLE warehouses 
ADD COLUMN IF NOT EXISTS city VARCHAR(100) AFTER address,
ADD COLUMN IF NOT EXISTS state VARCHAR(100) AFTER city,
ADD COLUMN IF NOT EXISTS zip_code VARCHAR(20) AFTER state,
ADD COLUMN IF NOT EXISTS country VARCHAR(100) AFTER zip_code;

-- Add contact information columns
ALTER TABLE warehouses 
ADD COLUMN IF NOT EXISTS phone VARCHAR(20) AFTER country,
ADD COLUMN IF NOT EXISTS email VARCHAR(255) AFTER phone;

-- Add created_by column for tracking who created the warehouse
ALTER TABLE warehouses 
ADD COLUMN IF NOT EXISTS created_by INT AFTER email,
ADD FOREIGN KEY IF NOT EXISTS fk_warehouses_created_by (created_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add capacity column that controllers expect
ALTER TABLE warehouses 
ADD COLUMN IF NOT EXISTS capacity INT DEFAULT 1000 AFTER email;

-- Update status enum to include 'deleted' for soft deletes
ALTER TABLE warehouses 
MODIFY COLUMN status ENUM('active', 'inactive', 'maintenance', 'deleted') DEFAULT 'active';

-- Add indexes for new columns
ALTER TABLE warehouses 
ADD INDEX IF NOT EXISTS idx_warehouses_city (city),
ADD INDEX IF NOT EXISTS idx_warehouses_state (state),
ADD INDEX IF NOT EXISTS idx_warehouses_created_by (created_by);

-- Insert sample warehouse data if table is empty
INSERT IGNORE INTO warehouses (
    company_id, name, code, description, address, city, state, zip_code, country, 
    phone, email, capacity, created_by, status
) VALUES 
(1, 'Main Storage Facility', 'MSF-001', 'Primary document storage warehouse', 
 '123 Storage Street', 'New York', 'NY', '10001', 'USA', 
 '******-0123', '<EMAIL>', 5000, 1, 'active'),
 
(1, 'Secondary Storage', 'SEC-002', 'Backup storage facility for overflow documents', 
 '456 Archive Avenue', 'Brooklyn', 'NY', '11201', 'USA', 
 '******-0124', '<EMAIL>', 3000, 1, 'active'),
 
(1, 'Climate Controlled Facility', 'CCF-003', 'Special storage for sensitive documents', 
 '789 Secure Boulevard', 'Queens', 'NY', '11101', 'USA', 
 '******-0125', '<EMAIL>', 2000, 1, 'active');
