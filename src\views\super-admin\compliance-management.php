<?php
$title = $title ?? 'Compliance & Retention Management';
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
    
    <!-- Header -->
    <div class="bg-white/90 backdrop-blur-sm border-b border-white/30 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Compliance & Retention Management</h1>
                    <p class="text-gray-600 mt-1">Automated policies, destruction scheduling, and audit compliance</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Compliance Status</p>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-<?= $complianceStats['overdue_destructions'] > 0 ? 'red' : 'green' ?>-500 rounded-full"></div>
                            <p class="font-medium text-<?= $complianceStats['overdue_destructions'] > 0 ? 'red' : 'green' ?>-600">
                                <?= $complianceStats['overdue_destructions'] > 0 ? 'Action Required' : 'Compliant' ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Quick Actions -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="<?= url('/super-admin/compliance-management/retention-policies') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Retention Policies
                </a>
                
                <a href="<?= url('/super-admin/compliance-management/destruction-schedule') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Destruction Schedule
                </a>
                
                <a href="<?= url('/super-admin/compliance-management/legal-holds') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Legal Holds
                </a>
                
                <a href="<?= url('/super-admin/compliance-management/reports') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Compliance Reports
                </a>
            </div>
        </div>

        <!-- Compliance Overview Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Active Policies -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Policies</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($complianceStats['total_policies']) ?></p>
                        <p class="text-xs text-blue-600">Retention policies</p>
                    </div>
                </div>
            </div>

            <!-- Scheduled Destructions -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Scheduled Destructions</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($complianceStats['scheduled_destructions']) ?></p>
                        <p class="text-xs text-red-600"><?= number_format($complianceStats['destructions_next_7_days']) ?> next 7 days</p>
                    </div>
                </div>
            </div>

            <!-- Legal Holds -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Legal Holds</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($complianceStats['active_legal_holds']) ?></p>
                        <p class="text-xs text-yellow-600"><?= number_format($complianceStats['documents_under_hold']) ?> documents protected</p>
                    </div>
                </div>
            </div>

            <!-- Compliance Status -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-<?= $complianceStats['overdue_destructions'] > 0 ? 'red' : 'green' ?>-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-<?= $complianceStats['overdue_destructions'] > 0 ? 'red' : 'green' ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <?php if ($complianceStats['overdue_destructions'] > 0): ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                <?php else: ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                <?php endif; ?>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Compliance Status</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?= $complianceStats['overdue_destructions'] > 0 ? $complianceStats['overdue_destructions'] : '✓' ?>
                        </p>
                        <p class="text-xs text-<?= $complianceStats['overdue_destructions'] > 0 ? 'red' : 'green' ?>-600">
                            <?= $complianceStats['overdue_destructions'] > 0 ? 'Overdue items' : 'All compliant' ?>
                        </p>
                    </div>
                </div>
            </div>

        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Upcoming Destructions -->
            <div class="lg:col-span-2 bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Upcoming Destructions (Next 30 Days)</h3>
                    <a href="<?= url('/super-admin/compliance-management/destruction-schedule') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View all →
                    </a>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Document</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Company</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Type</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Scheduled Date</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($upcomingDestructions, 0, 8) as $destruction): ?>
                                <tr class="border-b border-gray-100 hover:bg-gray-50">
                                    <td class="py-3 px-4">
                                        <div>
                                            <p class="font-medium text-gray-900"><?= e($destruction['document_title']) ?></p>
                                            <p class="text-sm text-gray-500"><?= e($destruction['reference_number']) ?></p>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4 text-sm text-gray-900"><?= e($destruction['company_name']) ?></td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <?= e($destruction['document_type']) ?>
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 text-sm text-gray-900">
                                        <?php 
                                        $scheduledDate = new DateTime($destruction['scheduled_date']);
                                        $today = new DateTime();
                                        $diff = $today->diff($scheduledDate);
                                        $isOverdue = $scheduledDate < $today;
                                        ?>
                                        <div class="<?= $isOverdue ? 'text-red-600' : ($diff->days <= 7 ? 'text-yellow-600' : 'text-gray-900') ?>">
                                            <?= $scheduledDate->format('M j, Y') ?>
                                            <?php if ($isOverdue): ?>
                                                <span class="text-xs block">Overdue</span>
                                            <?php elseif ($diff->days <= 7): ?>
                                                <span class="text-xs block"><?= $diff->days ?> days</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $isOverdue ? 'red' : ($diff->days <= 7 ? 'yellow' : 'green') ?>-100 text-<?= $isOverdue ? 'red' : ($diff->days <= 7 ? 'yellow' : 'green') ?>-800">
                                            <?= $isOverdue ? 'Overdue' : ($diff->days <= 7 ? 'Due Soon' : 'Scheduled') ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Compliance Alerts & Policies -->
            <div class="space-y-6">
                
                <!-- Compliance Alerts -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Compliance Alerts</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <?= count($complianceAlerts) ?> active
                        </span>
                    </div>
                    
                    <div class="space-y-3">
                        <?php if (!empty($complianceAlerts)): ?>
                            <?php foreach (array_slice($complianceAlerts, 0, 5) as $alert): ?>
                                <div class="flex items-start p-3 bg-<?= $alert['severity'] === 'critical' ? 'red' : 'yellow' ?>-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <svg class="w-5 h-5 text-<?= $alert['severity'] === 'critical' ? 'red' : 'yellow' ?>-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <h4 class="text-sm font-medium text-gray-900"><?= e($alert['title']) ?></h4>
                                        <p class="text-sm text-gray-600 mt-1"><?= e($alert['message']) ?></p>
                                        <p class="text-xs text-gray-500 mt-1"><?= e($alert['company_name']) ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-gray-500 text-sm">No active compliance alerts</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Retention Policies Summary -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Retention Policies</h3>
                    
                    <div class="space-y-4">
                        <?php foreach (array_slice($retentionPolicies, 0, 5) as $policy): ?>
                            <div class="flex items-center justify-between">
                                <div>
                                    <span class="text-sm font-medium text-gray-900"><?= e($policy['document_type']) ?></span>
                                    <p class="text-xs text-gray-500"><?= e($policy['compliance_regulation']) ?></p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm text-gray-900"><?= $policy['retention_years'] ?> years</span>
                                    <p class="text-xs text-gray-500"><?= number_format($policy['affected_documents']) ?> docs</p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="mt-4">
                        <a href="<?= url('/super-admin/compliance-management/retention-policies') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Manage policies →
                        </a>
                    </div>
                </div>

            </div>

        </div>

    </div>
</div>

<script>
// Auto-refresh dashboard every 5 minutes for compliance updates
setTimeout(() => {
    location.reload();
}, 300000);

// Initialize any charts or interactive elements here
document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript for interactive features
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
