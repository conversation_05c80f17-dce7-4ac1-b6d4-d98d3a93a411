-- Create boxes table for physical storage containers
-- Implements the Box Storage Process from documentation (Step 2)

CREATE TABLE IF NOT EXISTS boxes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    
    -- Box Identification (as per documentation: CLIENT01-BOX001)
    box_id VARCHAR(50) UNIQUE NOT NULL,
    client_prefix VARCHAR(20) NOT NULL,
    box_number INT NOT NULL,
    
    -- Physical Information
    name VARCHAR(255) NOT NULL,
    description TEXT,
    capacity INT DEFAULT 100, -- Number of documents/bundles it can hold
    current_count INT DEFAULT 0,
    
    -- Storage Location (as per documentation: WH-R1-S2-B03)
    warehouse_id INT NOT NULL,
    storage_location_code VARCHAR(50), -- e.g., WH-R1-S2-B03
    row_number VARCHAR(10),
    shelf_number VARCHAR(10),
    position_number VARCHAR(10),
    
    -- Box Status
    status ENUM('empty', 'partial', 'full', 'sealed', 'archived') DEFAULT 'empty',
    
    -- Barcode Information
    barcode_value VARCHAR(100) UNIQUE,
    qr_code_value VARCHAR(255),
    barcode_generated_at TIMESTAMP NULL,
    
    -- Metadata
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sealed_at TIMESTAMP NULL,
    sealed_by INT NULL,
    
    -- Foreign Keys
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (sealed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_company_boxes (company_id),
    INDEX idx_warehouse_boxes (warehouse_id),
    INDEX idx_box_status (status),
    INDEX idx_storage_location (storage_location_code),
    INDEX idx_barcode (barcode_value),
    INDEX idx_client_prefix (client_prefix),
    
    -- Unique constraint for client prefix and box number
    UNIQUE KEY unique_client_box (company_id, client_prefix, box_number)
);

-- Update bundles table to link to boxes (one box holds many bundles)
ALTER TABLE bundles 
ADD COLUMN box_id INT NULL AFTER location_id,
ADD FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL,
ADD INDEX idx_bundle_box (box_id);

-- Update document_intake table to link to boxes
ALTER TABLE document_intake 
ADD FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL,
ADD INDEX idx_intake_box (box_id);

-- Insert sample boxes for demonstration
INSERT INTO boxes (
    company_id, box_id, client_prefix, box_number, name, description,
    warehouse_id, storage_location_code, row_number, shelf_number, position_number,
    status, created_by
) VALUES 
(1, 'CLIENT01-BOX001', 'CLIENT01', 1, 'Client 01 - Box 001', 'First storage box for Client 01 documents', 1, 'WH-R1-S1-B01', 'R1', 'S1', 'B01', 'empty', 1),
(1, 'CLIENT01-BOX002', 'CLIENT01', 2, 'Client 01 - Box 002', 'Second storage box for Client 01 documents', 1, 'WH-R1-S1-B02', 'R1', 'S1', 'B02', 'empty', 1),
(1, 'CLIENT02-BOX001', 'CLIENT02', 1, 'Client 02 - Box 001', 'First storage box for Client 02 documents', 1, 'WH-R1-S2-B01', 'R1', 'S2', 'B01', 'empty', 1);

-- Create box_bundles junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS box_bundles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    box_id INT NOT NULL,
    bundle_id INT NOT NULL,
    position_in_box INT DEFAULT 1,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    added_by INT NOT NULL,
    
    FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
    FOREIGN KEY (bundle_id) REFERENCES bundles(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    UNIQUE KEY unique_box_bundle (box_id, bundle_id),
    INDEX idx_box_bundles (box_id),
    INDEX idx_bundle_boxes (bundle_id)
);

-- Create box activity log for tracking box movements and changes
CREATE TABLE IF NOT EXISTS box_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    box_id INT NOT NULL,
    action VARCHAR(50) NOT NULL, -- 'created', 'moved', 'sealed', 'opened', 'scanned'
    description TEXT,
    old_location VARCHAR(100),
    new_location VARCHAR(100),
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_box_activity (box_id, created_at),
    INDEX idx_user_activity (user_id, created_at)
);
