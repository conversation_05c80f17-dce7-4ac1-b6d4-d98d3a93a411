<?php

namespace App\Controllers;

/**
 * Service Provider Controller - Third-Party Document Storage Service
 * 
 * Manages multiple client companies and their document storage needs
 * Handles both physical warehouse storage and online digital storage options
 */
class ServiceProviderController extends BaseController
{
    /**
     * Service provider dashboard - overview of all clients
     */
    public function dashboard()
    {
        $this->requireAuth();
        
        try {
            // Get service provider statistics
            $stats = $this->getServiceProviderStats();
            
            // Get client companies overview
            $clientCompanies = $this->getClientCompanies();
            
            // Get recent intake across all clients
            $recentIntakes = $this->getRecentIntakes();
            
            // Get storage utilization
            $storageStats = $this->getStorageUtilization();

            $this->view('service-provider/dashboard', [
                'title' => 'Service Provider Dashboard',
                'stats' => $stats,
                'clientCompanies' => $clientCompanies,
                'recentIntakes' => $recentIntakes,
                'storageStats' => $storageStats
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading dashboard: ' . $e->getMessage(), 'error');
            $this->redirect('/dashboard');
        }
    }

    /**
     * Client company management
     */
    public function clients()
    {
        $this->requireAuth();
        
        try {
            $clients = $this->db->fetchAll(
                "SELECT c.*, 
                        COUNT(DISTINCT di.id) as total_intakes,
                        COUNT(DISTINCT d.id) as total_documents,
                        COUNT(DISTINCT b.id) as total_bundles,
                        COUNT(DISTINCT sl.id) as total_boxes,
                        SUM(CASE WHEN d.storage_type = 'physical' THEN 1 ELSE 0 END) as physical_docs,
                        SUM(CASE WHEN d.storage_type = 'online' THEN 1 ELSE 0 END) as online_docs,
                        SUM(d.file_size) as total_storage_used
                 FROM companies c
                 LEFT JOIN document_intake di ON c.id = di.company_id
                 LEFT JOIN documents d ON c.id = d.company_id AND d.status != 'deleted'
                 LEFT JOIN bundles b ON c.id = b.company_id AND b.status = 'active'
                 LEFT JOIN warehouses w ON c.id = w.company_id
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.type = 'box'
                 WHERE c.status = 'active'
                 GROUP BY c.id
                 ORDER BY c.name",
                []
            );

            $this->view('service-provider/clients', [
                'title' => 'Client Companies',
                'clients' => $clients
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading clients: ' . $e->getMessage(), 'error');
            $this->redirect('/dashboard');
        }
    }

    /**
     * Storage type management - Physical vs Online
     */
    public function storageTypes()
    {
        $this->requireAuth();
        
        try {
            // Get storage type statistics
            $physicalStats = $this->getPhysicalStorageStats();
            $onlineStats = $this->getOnlineStorageStats();
            
            // Get recent storage activities
            $recentActivities = $this->getStorageActivities();

            $this->view('service-provider/storage-types', [
                'title' => 'Storage Management',
                'physicalStats' => $physicalStats,
                'onlineStats' => $onlineStats,
                'recentActivities' => $recentActivities
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading storage data: ' . $e->getMessage(), 'error');
            $this->redirect('/dashboard');
        }
    }

    /**
     * Client-specific dashboard
     */
    public function clientDashboard($clientId)
    {
        $this->requireAuth();
        
        try {
            // Get client company details
            $client = $this->db->fetch(
                "SELECT * FROM companies WHERE id = ? AND status = 'active'",
                [$clientId]
            );

            if (!$client) {
                throw new \Exception('Client company not found');
            }

            // Get client-specific statistics
            $clientStats = $this->getClientStats($clientId);
            
            // Get client's recent intakes
            $clientIntakes = $this->getClientIntakes($clientId);
            
            // Get client's storage breakdown
            $storageBreakdown = $this->getClientStorageBreakdown($clientId);

            $this->view('service-provider/client-dashboard', [
                'title' => $client['name'] . ' - Dashboard',
                'client' => $client,
                'stats' => $clientStats,
                'intakes' => $clientIntakes,
                'storageBreakdown' => $storageBreakdown
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading client dashboard: ' . $e->getMessage(), 'error');
            $this->redirect('/service-provider/clients');
        }
    }

    /**
     * Get service provider statistics
     */
    private function getServiceProviderStats()
    {
        $stats = [
            'total_clients' => 0,
            'total_documents' => 0,
            'total_physical_boxes' => 0,
            'total_online_storage' => 0,
            'pending_intakes' => 0,
            'monthly_revenue' => 0
        ];

        try {
            // Total active clients
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM companies WHERE status = 'active'");
            $stats['total_clients'] = $result['count'] ?? 0;

            // Total documents across all clients
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM documents WHERE status != 'deleted'");
            $stats['total_documents'] = $result['count'] ?? 0;

            // Total physical boxes
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM storage_locations sl 
                 JOIN warehouses w ON sl.warehouse_id = w.id 
                 WHERE sl.type = 'box' AND sl.storage_type = 'physical' AND sl.status = 'active'"
            );
            $stats['total_physical_boxes'] = $result['count'] ?? 0;

            // Total online storage used
            $result = $this->db->fetch(
                "SELECT SUM(file_size) as total FROM documents WHERE storage_type = 'online' AND status != 'deleted'"
            );
            $stats['total_online_storage'] = $result['total'] ?? 0;

            // Pending intakes across all clients
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM document_intake WHERE status = 'pending'");
            $stats['pending_intakes'] = $result['count'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get client companies with basic stats
     */
    private function getClientCompanies()
    {
        return $this->db->fetchAll(
            "SELECT c.*, 
                    COUNT(DISTINCT d.id) as document_count,
                    COUNT(DISTINCT di.id) as intake_count,
                    SUM(CASE WHEN d.storage_type = 'physical' THEN 1 ELSE 0 END) as physical_count,
                    SUM(CASE WHEN d.storage_type = 'online' THEN 1 ELSE 0 END) as online_count,
                    SUM(d.file_size) as storage_used
             FROM companies c
             LEFT JOIN documents d ON c.id = d.company_id AND d.status != 'deleted'
             LEFT JOIN document_intake di ON c.id = di.company_id AND di.status = 'pending'
             WHERE c.status = 'active'
             GROUP BY c.id
             ORDER BY c.name
             LIMIT 10",
            []
        );
    }

    /**
     * Get recent intakes across all clients
     */
    private function getRecentIntakes()
    {
        return $this->db->fetchAll(
            "SELECT di.*, c.name as client_name, u.first_name, u.last_name
             FROM document_intake di
             JOIN companies c ON di.company_id = c.id
             LEFT JOIN users u ON di.created_by = u.id
             WHERE di.status IN ('pending', 'processing')
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 15",
            []
        );
    }

    /**
     * Get storage utilization statistics
     */
    private function getStorageUtilization()
    {
        $stats = [
            'physical' => [
                'total_boxes' => 0,
                'occupied_boxes' => 0,
                'utilization_percentage' => 0
            ],
            'online' => [
                'total_storage' => 0,
                'used_storage' => 0,
                'utilization_percentage' => 0
            ]
        ];

        try {
            // Physical storage stats
            $result = $this->db->fetch(
                "SELECT 
                    COUNT(*) as total_boxes,
                    SUM(CASE WHEN sl.status = 'occupied' THEN 1 ELSE 0 END) as occupied_boxes
                 FROM storage_locations sl 
                 JOIN warehouses w ON sl.warehouse_id = w.id 
                 WHERE sl.type = 'box' AND sl.storage_type = 'physical'"
            );
            
            $stats['physical']['total_boxes'] = $result['total_boxes'] ?? 0;
            $stats['physical']['occupied_boxes'] = $result['occupied_boxes'] ?? 0;
            
            if ($stats['physical']['total_boxes'] > 0) {
                $stats['physical']['utilization_percentage'] = round(
                    ($stats['physical']['occupied_boxes'] / $stats['physical']['total_boxes']) * 100, 1
                );
            }

            // Online storage stats (assuming 1TB total capacity per client)
            $result = $this->db->fetch(
                "SELECT SUM(file_size) as used_storage FROM documents WHERE storage_type = 'online' AND status != 'deleted'"
            );
            
            $stats['online']['used_storage'] = $result['used_storage'] ?? 0;
            $stats['online']['total_storage'] = 1024 * 1024 * 1024 * 1024; // 1TB in bytes
            
            if ($stats['online']['total_storage'] > 0) {
                $stats['online']['utilization_percentage'] = round(
                    ($stats['online']['used_storage'] / $stats['online']['total_storage']) * 100, 1
                );
            }

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get physical storage statistics
     */
    private function getPhysicalStorageStats()
    {
        return [
            'total_warehouses' => $this->db->fetchColumn("SELECT COUNT(*) FROM warehouses WHERE status = 'active'"),
            'total_boxes' => $this->db->fetchColumn(
                "SELECT COUNT(*) FROM storage_locations sl 
                 JOIN warehouses w ON sl.warehouse_id = w.id 
                 WHERE sl.type = 'box' AND sl.storage_type = 'physical' AND sl.status = 'active'"
            ),
            'occupied_boxes' => $this->db->fetchColumn(
                "SELECT COUNT(DISTINCT sl.id) FROM storage_locations sl 
                 JOIN warehouses w ON sl.warehouse_id = w.id 
                 JOIN documents d ON sl.id = d.location_id 
                 WHERE sl.type = 'box' AND sl.storage_type = 'physical' AND d.status != 'deleted'"
            ),
            'total_documents' => $this->db->fetchColumn(
                "SELECT COUNT(*) FROM documents WHERE storage_type = 'physical' AND status != 'deleted'"
            )
        ];
    }

    /**
     * Get online storage statistics
     */
    private function getOnlineStorageStats()
    {
        return [
            'total_documents' => $this->db->fetchColumn(
                "SELECT COUNT(*) FROM documents WHERE storage_type = 'online' AND status != 'deleted'"
            ),
            'total_storage_used' => $this->db->fetchColumn(
                "SELECT SUM(file_size) FROM documents WHERE storage_type = 'online' AND status != 'deleted'"
            ),
            'total_clients_using_online' => $this->db->fetchColumn(
                "SELECT COUNT(DISTINCT company_id) FROM documents WHERE storage_type = 'online' AND status != 'deleted'"
            )
        ];
    }

    /**
     * Get recent storage activities
     */
    private function getStorageActivities()
    {
        return $this->db->fetchAll(
            "SELECT d.*, c.name as client_name, u.first_name, u.last_name,
                    CASE 
                        WHEN d.storage_type = 'physical' THEN CONCAT('Box: ', sl.name)
                        ELSE 'Online Storage'
                    END as storage_location
             FROM documents d
             JOIN companies c ON d.company_id = c.id
             LEFT JOIN users u ON d.created_by = u.id
             LEFT JOIN storage_locations sl ON d.location_id = sl.id
             WHERE d.status != 'deleted'
             ORDER BY d.created_at DESC
             LIMIT 20",
            []
        );
    }

    /**
     * Get client-specific statistics
     */
    private function getClientStats($clientId)
    {
        $stats = [];

        try {
            // Total documents
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM documents WHERE company_id = ? AND status != 'deleted'",
                [$clientId]
            );
            $stats['total_documents'] = $result['count'] ?? 0;

            // Physical vs Online breakdown
            $result = $this->db->fetch(
                "SELECT 
                    SUM(CASE WHEN storage_type = 'physical' THEN 1 ELSE 0 END) as physical_count,
                    SUM(CASE WHEN storage_type = 'online' THEN 1 ELSE 0 END) as online_count,
                    SUM(CASE WHEN storage_type = 'physical' THEN file_size ELSE 0 END) as physical_storage,
                    SUM(CASE WHEN storage_type = 'online' THEN file_size ELSE 0 END) as online_storage
                 FROM documents 
                 WHERE company_id = ? AND status != 'deleted'",
                [$clientId]
            );
            
            $stats['physical_documents'] = $result['physical_count'] ?? 0;
            $stats['online_documents'] = $result['online_count'] ?? 0;
            $stats['physical_storage'] = $result['physical_storage'] ?? 0;
            $stats['online_storage'] = $result['online_storage'] ?? 0;

            // Pending intakes
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM document_intake WHERE company_id = ? AND status = 'pending'",
                [$clientId]
            );
            $stats['pending_intakes'] = $result['count'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get client intakes
     */
    private function getClientIntakes($clientId)
    {
        return $this->db->fetchAll(
            "SELECT di.*, u.first_name, u.last_name
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             ORDER BY di.priority DESC, di.created_at ASC
             LIMIT 10",
            [$clientId]
        );
    }

    /**
     * Get client storage breakdown
     */
    private function getClientStorageBreakdown($clientId)
    {
        return $this->db->fetchAll(
            "SELECT 
                storage_type,
                document_type,
                COUNT(*) as document_count,
                SUM(file_size) as total_size
             FROM documents 
             WHERE company_id = ? AND status != 'deleted'
             GROUP BY storage_type, document_type
             ORDER BY storage_type, document_count DESC",
            [$clientId]
        );
    }
}
