<?php

namespace App\Controllers;

/**
 * Report Controller
 * 
 * Handles reports and analytics functionality
 */
class ReportController extends BaseController
{
    /**
     * Show reports dashboard
     */
    public function index()
    {
        $this->requireAuth();
        
        // Get basic statistics for the reports dashboard
        $stats = [];
        
        if ($this->db) {
            try {
                // Document statistics
                $stats['total_documents'] = $this->db->fetchColumn(
                    "SELECT COUNT(*) FROM documents WHERE company_id = ?",
                    [$this->user['company_id']]
                );
                
                // Storage statistics
                $stats['total_storage'] = $this->db->fetchColumn(
                    "SELECT COALESCE(SUM(file_size), 0) FROM documents WHERE company_id = ?",
                    [$this->user['company_id']]
                );
                
                // Recent activity
                $stats['recent_uploads'] = $this->db->fetchColumn(
                    "SELECT COUNT(*) FROM documents WHERE company_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)",
                    [$this->user['company_id']]
                );
                
                // User activity
                $stats['active_users'] = $this->db->fetchColumn(
                    "SELECT COUNT(*) FROM users WHERE company_id = ? AND status = 'active'",
                    [$this->user['company_id']]
                );
                
            } catch (\Exception $e) {
                // Database error - use default values
                $stats = [
                    'total_documents' => 0,
                    'total_storage' => 0,
                    'recent_uploads' => 0,
                    'active_users' => 0
                ];
            }
        }
        
        $this->view('reports/index', [
            'title' => 'Reports & Analytics',
            'stats' => $stats
        ]);
    }
    
    /**
     * Show usage reports
     */
    public function usage()
    {
        $this->requireAuth();
        
        $usageData = [];
        
        if ($this->db) {
            try {
                // Monthly document uploads
                $usageData['monthly_uploads'] = $this->db->fetchAll(
                    "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count 
                     FROM documents 
                     WHERE company_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                     GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                     ORDER BY month",
                    [$this->user['company_id']]
                );
                
                // User activity
                $usageData['user_activity'] = $this->db->fetchAll(
                    "SELECT u.first_name, u.last_name, COUNT(d.id) as document_count
                     FROM users u
                     LEFT JOIN documents d ON u.id = d.created_by AND d.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                     WHERE u.company_id = ? AND u.status = 'active'
                     GROUP BY u.id
                     ORDER BY document_count DESC
                     LIMIT 10",
                    [$this->user['company_id']]
                );
                
            } catch (\Exception $e) {
                $usageData = [
                    'monthly_uploads' => [],
                    'user_activity' => []
                ];
            }
        }
        
        $this->view('reports/usage', [
            'title' => 'Usage Reports',
            'usageData' => $usageData
        ]);
    }
    
    /**
     * Show storage reports
     */
    public function storage()
    {
        $this->requireAuth();
        
        $storageData = [];
        
        if ($this->db) {
            try {
                // Storage by category
                $storageData['by_category'] = $this->db->fetchAll(
                    "SELECT c.name, COALESCE(SUM(d.file_size), 0) as total_size, COUNT(d.id) as document_count
                     FROM categories c
                     LEFT JOIN documents d ON c.id = d.category_id AND d.company_id = ?
                     WHERE c.company_id = ?
                     GROUP BY c.id
                     ORDER BY total_size DESC",
                    [$this->user['company_id'], $this->user['company_id']]
                );
                
                // Storage by file type
                $storageData['by_type'] = $this->db->fetchAll(
                    "SELECT file_type, COALESCE(SUM(file_size), 0) as total_size, COUNT(*) as document_count
                     FROM documents 
                     WHERE company_id = ?
                     GROUP BY file_type
                     ORDER BY total_size DESC",
                    [$this->user['company_id']]
                );
                
                // Storage growth over time
                $storageData['growth'] = $this->db->fetchAll(
                    "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, SUM(file_size) as size_added
                     FROM documents 
                     WHERE company_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                     GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                     ORDER BY month",
                    [$this->user['company_id']]
                );
                
            } catch (\Exception $e) {
                $storageData = [
                    'by_category' => [],
                    'by_type' => [],
                    'growth' => []
                ];
            }
        }
        
        $this->view('reports/storage', [
            'title' => 'Storage Reports',
            'storageData' => $storageData
        ]);
    }
    
    /**
     * Show audit reports
     */
    public function audit()
    {
        $this->requireAuth();
        
        $auditData = [];
        
        if ($this->db) {
            try {
                // Recent document activities
                $auditData['recent_activities'] = $this->db->fetchAll(
                    "SELECT d.title, d.created_at, u.first_name, u.last_name, 'upload' as action
                     FROM documents d
                     JOIN users u ON d.created_by = u.id
                     WHERE d.company_id = ?
                     ORDER BY d.created_at DESC
                     LIMIT 50",
                    [$this->user['company_id']]
                );
                
                // User login activities (if audit table exists)
                // This would require an audit_logs table to be implemented
                $auditData['login_activities'] = [];
                
            } catch (\Exception $e) {
                $auditData = [
                    'recent_activities' => [],
                    'login_activities' => []
                ];
            }
        }
        
        $this->view('reports/audit', [
            'title' => 'Audit Reports',
            'auditData' => $auditData
        ]);
    }
    
    /**
     * Export reports
     */
    public function export()
    {
        $this->requireAuth();
        
        try {
            $reportType = $_POST['report_type'] ?? 'usage';
            $format = $_POST['format'] ?? 'csv';
            
            // Generate report data based on type
            $data = [];
            
            switch ($reportType) {
                case 'usage':
                    $data = $this->generateUsageReport();
                    break;
                case 'storage':
                    $data = $this->generateStorageReport();
                    break;
                case 'audit':
                    $data = $this->generateAuditReport();
                    break;
                default:
                    throw new \Exception('Invalid report type');
            }
            
            // Export based on format
            if ($format === 'csv') {
                $this->exportCSV($data, $reportType);
            } else {
                throw new \Exception('Unsupported export format');
            }
            
        } catch (\Exception $e) {
            $_SESSION['error'] = 'Failed to export report: ' . $e->getMessage();
            redirect('/app/reports');
        }
    }
    
    /**
     * Generate usage report data
     */
    private function generateUsageReport()
    {
        if (!$this->db) return [];
        
        return $this->db->fetchAll(
            "SELECT d.title, d.created_at, u.first_name, u.last_name, d.file_size
             FROM documents d
             JOIN users u ON d.created_by = u.id
             WHERE d.company_id = ?
             ORDER BY d.created_at DESC",
            [$this->user['company_id']]
        );
    }
    
    /**
     * Generate storage report data
     */
    private function generateStorageReport()
    {
        if (!$this->db) return [];
        
        return $this->db->fetchAll(
            "SELECT d.title, d.file_type, d.file_size, d.created_at, c.name as category
             FROM documents d
             LEFT JOIN categories c ON d.category_id = c.id
             WHERE d.company_id = ?
             ORDER BY d.file_size DESC",
            [$this->user['company_id']]
        );
    }
    
    /**
     * Generate audit report data
     */
    private function generateAuditReport()
    {
        if (!$this->db) return [];
        
        return $this->db->fetchAll(
            "SELECT d.title, d.created_at, u.first_name, u.last_name, 'Document Upload' as action
             FROM documents d
             JOIN users u ON d.created_by = u.id
             WHERE d.company_id = ?
             ORDER BY d.created_at DESC",
            [$this->user['company_id']]
        );
    }
    
    /**
     * Export data as CSV
     */
    private function exportCSV($data, $reportType)
    {
        $filename = $reportType . '_report_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        if (!empty($data)) {
            // Write headers
            fputcsv($output, array_keys($data[0]));
            
            // Write data
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
        exit;
    }
}
