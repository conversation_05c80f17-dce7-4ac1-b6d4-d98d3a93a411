<?php
$title = 'Edit Bundle - ' . ($bundle['name'] ?? 'Bundle');
ob_start();
?>

<!-- Edit Bundle Form -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- <PERSON> Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        Edit Bundle
                    </h1>
                    <p class="text-gray-600 mt-2">Update bundle information and settings</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/bundles/' . $bundle['id']) ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Bundle
                    </a>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
            <form action="<?= url('/app/bundles/' . $bundle['id']) ?>" method="POST" class="space-y-6">
                <input type="hidden" name="_method" value="PUT">
                
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Bundle Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Bundle Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="<?= e($bundle['name']) ?>"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="e.g., Q1 2024 Financial Documents">
                    </div>

                    <!-- Company -->
                    <div>
                        <label for="company_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Company <span class="text-red-500">*</span>
                        </label>
                        <select id="company_id" 
                                name="company_id" 
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="">Select a company</option>
                            <?php foreach ($companies as $company): ?>
                                <option value="<?= $company['id'] ?>" <?= $company['id'] == $bundle['company_id'] ? 'selected' : '' ?>>
                                    <?= e($company['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              placeholder="Describe the purpose and contents of this bundle..."><?= e($bundle['description'] ?? '') ?></textarea>
                </div>

                <!-- Category and Priority -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                            Category
                        </label>
                        <select id="category" 
                                name="category" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="general" <?= $bundle['category'] === 'general' ? 'selected' : '' ?>>General</option>
                            <option value="financial" <?= $bundle['category'] === 'financial' ? 'selected' : '' ?>>Financial</option>
                            <option value="legal" <?= $bundle['category'] === 'legal' ? 'selected' : '' ?>>Legal</option>
                            <option value="hr" <?= $bundle['category'] === 'hr' ? 'selected' : '' ?>>Human Resources</option>
                            <option value="marketing" <?= $bundle['category'] === 'marketing' ? 'selected' : '' ?>>Marketing</option>
                            <option value="technical" <?= $bundle['category'] === 'technical' ? 'selected' : '' ?>>Technical</option>
                            <option value="correspondence" <?= $bundle['category'] === 'correspondence' ? 'selected' : '' ?>>Correspondence</option>
                            <option value="reports" <?= $bundle['category'] === 'reports' ? 'selected' : '' ?>>Reports</option>
                            <option value="contracts" <?= $bundle['category'] === 'contracts' ? 'selected' : '' ?>>Contracts</option>
                            <option value="invoices" <?= $bundle['category'] === 'invoices' ? 'selected' : '' ?>>Invoices</option>
                        </select>
                    </div>

                    <!-- Priority -->
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                            Priority
                        </label>
                        <select id="priority" 
                                name="priority" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="low" <?= $bundle['priority'] === 'low' ? 'selected' : '' ?>>Low</option>
                            <option value="medium" <?= $bundle['priority'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                            <option value="high" <?= $bundle['priority'] === 'high' ? 'selected' : '' ?>>High</option>
                            <option value="urgent" <?= $bundle['priority'] === 'urgent' ? 'selected' : '' ?>>Urgent</option>
                        </select>
                    </div>

                </div>

                <!-- Retention and Access -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Retention Period -->
                    <div>
                        <label for="retention_period" class="block text-sm font-medium text-gray-700 mb-2">
                            Retention Period (years)
                        </label>
                        <input type="number" 
                               id="retention_period" 
                               name="retention_period" 
                               value="<?= e($bundle['retention_period']) ?>"
                               min="1"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="7">
                    </div>

                    <!-- Access Level -->
                    <div>
                        <label for="access_level" class="block text-sm font-medium text-gray-700 mb-2">
                            Access Level
                        </label>
                        <select id="access_level" 
                                name="access_level" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="public" <?= $bundle['access_level'] === 'public' ? 'selected' : '' ?>>Public</option>
                            <option value="private" <?= $bundle['access_level'] === 'private' ? 'selected' : '' ?>>Private</option>
                            <option value="restricted" <?= $bundle['access_level'] === 'restricted' ? 'selected' : '' ?>>Restricted</option>
                        </select>
                    </div>

                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-4">
                        <a href="<?= url('/app/bundles/' . $bundle['id']) ?>" 
                           class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update Bundle
                        </button>
                    </div>
                    
                    <!-- Delete Button -->
                    <button type="button" 
                            onclick="confirmDelete()"
                            class="px-6 py-3 bg-red-600 text-white font-medium rounded-xl hover:bg-red-700 transition-colors">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete Bundle
                    </button>
                </div>

            </form>
        </div>

        <!-- Bundle Information -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">Bundle Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-blue-800">
                <div>
                    <h4 class="font-medium mb-1">Reference Number:</h4>
                    <p><?= e($bundle['reference_number']) ?></p>
                </div>
                <div>
                    <h4 class="font-medium mb-1">Current Document Count:</h4>
                    <p class="font-semibold text-blue-600">
                        <?= number_format($stats['total_documents'] ?? 0) ?> documents
                    </p>
                </div>
                <div>
                    <h4 class="font-medium mb-1">Created:</h4>
                    <p><?= date('M j, Y g:i A', strtotime($bundle['created_at'])) ?></p>
                    <?php if (!empty($bundle['first_name']) || !empty($bundle['last_name'])): ?>
                        <p>by <?= e($bundle['first_name'] . ' ' . $bundle['last_name']) ?></p>
                    <?php endif; ?>
                </div>
                <div>
                    <h4 class="font-medium mb-1">Last Updated:</h4>
                    <p><?= date('M j, Y g:i A', strtotime($bundle['updated_at'])) ?></p>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-8 max-w-md mx-4">
        <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete Bundle</h3>
            <p class="text-gray-600 mb-6">Are you sure you want to delete this bundle? This action cannot be undone and will affect all associated documents.</p>
            <div class="flex items-center justify-center space-x-4">
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <form action="<?= url('/app/bundles/' . $bundle['id']) ?>" method="POST" class="inline">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" 
                            class="px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors">
                        Delete Bundle
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    document.getElementById('deleteModal').classList.remove('hidden');
    document.getElementById('deleteModal').classList.add('flex');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    document.getElementById('deleteModal').classList.remove('flex');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
