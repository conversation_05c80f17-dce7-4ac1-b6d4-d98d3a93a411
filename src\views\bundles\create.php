<?php
$title = 'Create New Bundle';
ob_start();
?>

<!-- Create Bundle Form -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- <PERSON> Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        Create New Bundle
                    </h1>
                    <p class="text-gray-600 mt-2">Create a logical grouping of documents for better organization</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/bundles') ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Bundles
                    </a>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
            <form action="<?= url('/app/bundles') ?>" method="POST" class="space-y-6">

                <!-- Essential Information -->
                <div class="space-y-6">

                    <!-- Bundle Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Bundle Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="name"
                               name="name"
                               required
                               maxlength="255"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="e.g., Q1 2024 Financial Documents">
                    </div>

                    <!-- Company -->
                    <div>
                        <label for="company_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Company <span class="text-red-500">*</span>
                        </label>
                        <select id="company_id"
                                name="company_id"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="">Select a company</option>
                            <?php foreach ($companies as $company): ?>
                                <option value="<?= $company['id'] ?>" <?= $company['id'] == $user['company_id'] ? 'selected' : '' ?>>
                                    <?= e($company['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description
                        </label>
                        <textarea id="description"
                                  name="description"
                                  rows="3"
                                  maxlength="1000"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                  placeholder="Describe the purpose and contents of this bundle..."></textarea>
                    </div>

                    <!-- Category and Priority -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- Category -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                Category
                            </label>
                            <select id="category"
                                    name="category"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                <option value="general">General</option>
                                <option value="financial">Financial</option>
                                <option value="legal">Legal</option>
                                <option value="hr">Human Resources</option>
                                <option value="marketing">Marketing</option>
                                <option value="technical">Technical</option>
                                <option value="correspondence">Correspondence</option>
                                <option value="reports">Reports</option>
                                <option value="contracts">Contracts</option>
                                <option value="invoices">Invoices</option>
                            </select>
                        </div>

                        <!-- Priority -->
                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                                Priority
                            </label>
                            <select id="priority"
                                    name="priority"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>

                    </div>

                    <!-- Access Level and Retention -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- Access Level -->
                        <div>
                            <label for="access_level" class="block text-sm font-medium text-gray-700 mb-2">
                                Access Level
                            </label>
                            <select id="access_level"
                                    name="access_level"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                <option value="public">Public</option>
                                <option value="private" selected>Private</option>
                                <option value="restricted">Restricted</option>
                            </select>
                        </div>

                        <!-- Retention Period -->
                        <div>
                            <label for="retention_period" class="block text-sm font-medium text-gray-700 mb-2">
                                Retention Period (years)
                            </label>
                            <input type="number"
                                   id="retention_period"
                                   name="retention_period"
                                   min="1"
                                   max="100"
                                   value="7"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="7">
                        </div>

                    </div>

                    <!-- Storage Type Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-4">
                            Storage Type
                        </label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Physical Storage Option -->
                            <div class="relative">
                                <input type="radio" name="storage_type" id="storage_physical" value="physical"
                                       class="peer sr-only">
                                <label for="storage_physical"
                                       class="flex flex-col items-center p-4 bg-gradient-to-br from-amber-50 to-orange-100 border-2 border-amber-200 rounded-xl cursor-pointer hover:border-amber-300 peer-checked:border-amber-500 peer-checked:bg-gradient-to-br peer-checked:from-amber-100 peer-checked:to-orange-200 transition-all duration-200">
                                    <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg flex items-center justify-center mb-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                    <h4 class="font-semibold text-gray-900 text-center">📦 Physical Only</h4>
                                    <p class="text-sm text-gray-600 text-center mt-1">Warehouse storage only</p>
                                    <div class="mt-3">
                                        <div class="w-4 h-4 border-2 border-amber-400 rounded-full peer-checked:bg-amber-500 peer-checked:border-amber-500 transition-all duration-200"></div>
                                    </div>
                                </label>
                            </div>

                            <!-- Online Storage Option -->
                            <div class="relative">
                                <input type="radio" name="storage_type" id="storage_online" value="online"
                                       class="peer sr-only">
                                <label for="storage_online"
                                       class="flex flex-col items-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-xl cursor-pointer hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-gradient-to-br peer-checked:from-blue-100 peer-checked:to-indigo-200 transition-all duration-200">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mb-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                        </svg>
                                    </div>
                                    <h4 class="font-semibold text-gray-900 text-center">☁️ Online Only</h4>
                                    <p class="text-sm text-gray-600 text-center mt-1">Digital storage only</p>
                                    <div class="mt-3">
                                        <div class="w-4 h-4 border-2 border-blue-400 rounded-full peer-checked:bg-blue-500 peer-checked:border-blue-500 transition-all duration-200"></div>
                                    </div>
                                </label>
                            </div>

                            <!-- Mixed Storage Option -->
                            <div class="relative">
                                <input type="radio" name="storage_type" id="storage_mixed" value="mixed" checked
                                       class="peer sr-only">
                                <label for="storage_mixed"
                                       class="flex flex-col items-center p-4 bg-gradient-to-br from-purple-50 to-pink-100 border-2 border-purple-200 rounded-xl cursor-pointer hover:border-purple-300 peer-checked:border-purple-500 peer-checked:bg-gradient-to-br peer-checked:from-purple-100 peer-checked:to-pink-200 transition-all duration-200">
                                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mb-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>
                                        </svg>
                                    </div>
                                    <h4 class="font-semibold text-gray-900 text-center">🔄 Mixed Storage</h4>
                                    <p class="text-sm text-gray-600 text-center mt-1">Both physical & online</p>
                                    <div class="mt-3">
                                        <div class="w-4 h-4 border-2 border-purple-400 rounded-full peer-checked:bg-purple-500 peer-checked:border-purple-500 transition-all duration-200"></div>
                                    </div>
                                </label>
                            </div>
                        </div>
                        <p class="text-sm text-gray-500 mt-3">Choose the storage type for this bundle. Mixed allows both physical and online documents.</p>
                    </div>

                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="<?= url('/app/bundles') ?>" 
                       class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Bundle
                    </button>
                </div>

            </form>
        </div>

        <!-- Help Information -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">Bundle Organization Guide</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                <div>
                    <h4 class="font-medium mb-2">Bundle Categories:</h4>
                    <ul class="space-y-1">
                        <li>• <strong>Financial:</strong> Invoices, receipts, tax documents</li>
                        <li>• <strong>Legal:</strong> Contracts, agreements, compliance</li>
                        <li>• <strong>HR:</strong> Employee files, policies, training</li>
                        <li>• <strong>Technical:</strong> Manuals, specifications, guides</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Access Levels:</h4>
                    <ul class="space-y-1">
                        <li>• <strong>Public:</strong> Accessible to all users</li>
                        <li>• <strong>Private:</strong> Limited to authorized users</li>
                        <li>• <strong>Restricted:</strong> Highest security level</li>
                    </ul>
                </div>
            </div>
            <div class="mt-4 p-4 bg-blue-100 rounded-lg">
                <p class="text-sm text-blue-800">
                    <strong>Workflow:</strong> Create bundles first, then assign them to boxes during the storage management process. Documents can be added to bundles after creation.
                </p>
            </div>
        </div>

    </div>
</div>

<script>
// Auto-suggest bundle name based on category and current date
document.getElementById('category').addEventListener('change', function() {
    const category = this.value;
    const nameInput = document.getElementById('name');

    if (!nameInput.value.trim()) {
        const currentDate = new Date();
        const quarter = Math.ceil((currentDate.getMonth() + 1) / 3);
        const year = currentDate.getFullYear();

        let suggestedName = '';

        switch(category) {
            case 'financial':
                suggestedName = `Q${quarter} ${year} Financial Documents`;
                break;
            case 'legal':
                suggestedName = `${year} Legal Documents`;
                break;
            case 'hr':
                suggestedName = `${year} HR Documents`;
                break;
            case 'contracts':
                suggestedName = `${year} Contracts`;
                break;
            case 'invoices':
                suggestedName = `${currentDate.toLocaleString('default', { month: 'long' })} ${year} Invoices`;
                break;
            default:
                suggestedName = `${category.charAt(0).toUpperCase() + category.slice(1)} Documents`;
        }

        nameInput.value = suggestedName;
    }
});

// Auto-adjust retention period and access level based on category
document.getElementById('category').addEventListener('change', function() {
    const category = this.value;
    const retentionInput = document.getElementById('retention_period');
    const accessSelect = document.getElementById('access_level');

    // Suggest retention periods and access levels based on common practices
    switch(category) {
        case 'financial':
        case 'legal':
            retentionInput.value = 10; // Financial and legal docs typically kept longer
            accessSelect.value = 'restricted';
            break;
        case 'hr':
            retentionInput.value = 7;
            accessSelect.value = 'restricted';
            break;
        case 'correspondence':
            retentionInput.value = 3;
            accessSelect.value = 'private';
            break;
        case 'marketing':
        case 'reports':
            retentionInput.value = 5;
            accessSelect.value = 'public';
            break;
        default:
            retentionInput.value = 7;
            accessSelect.value = 'private';
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const companyId = document.getElementById('company_id').value;

    if (!name) {
        e.preventDefault();
        alert('Please enter a bundle name.');
        document.getElementById('name').focus();
        return false;
    }

    if (!companyId) {
        e.preventDefault();
        alert('Please select a company.');
        document.getElementById('company_id').focus();
        return false;
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
