<?php
$title = $warehouse['name'];
ob_start();
?>

<!-- Enhanced Warehouse Details -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Enhanced Header -->
        <div class="mb-8">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                                <?= e($warehouse['name']) ?>
                            </h1>
                            <div class="flex items-center space-x-4 mt-2">
                                <span class="text-sm font-medium text-gray-600">
                                    📍 <?= e($warehouse['city']) ?>, <?= e($warehouse['state']) ?>
                                </span>
                                <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-green-100 text-green-800">
                                    ✅ Active
                                </span>
                                <?php if (!empty($warehouse['capacity'])): ?>
                                <span class="text-sm font-mono bg-gray-100 px-3 py-1 rounded-lg text-gray-700">
                                    Capacity: <?= number_format($warehouse['capacity']) ?>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                            <div class="text-2xl font-bold text-blue-600"><?= number_format($stats['total_boxes'] ?? 0) ?></div>
                            <div class="text-sm text-gray-600">Storage Boxes</div>
                        </div>
                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                            <div class="text-2xl font-bold text-green-600"><?= number_format($stats['total_documents'] ?? 0) ?></div>
                            <div class="text-sm text-gray-600">Documents</div>
                        </div>
                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                            <div class="text-2xl font-bold text-purple-600"><?= number_format($stats['occupied_boxes'] ?? 0) ?></div>
                            <div class="text-sm text-gray-600">Occupied Boxes</div>
                        </div>
                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                            <div class="text-2xl font-bold text-orange-600"><?= number_format($stats['utilization_percentage'] ?? 0) ?>%</div>
                            <div class="text-sm text-gray-600">Utilization</div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col space-y-3">
                    <a href="<?= url('/app/warehouses/' . $warehouse['id'] . '/edit') ?>"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-600 to-yellow-700 text-white font-medium rounded-lg hover:from-yellow-700 hover:to-yellow-800 transition-all duration-200 shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Warehouse
                    </a>
                    <button onclick="generateWarehouseReport()"
                            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white font-medium rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Generate Report
                    </button>
                    <button onclick="printWarehouseLayout()"
                            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        Print Layout
                    </button>
                    <a href="<?= url('/app/warehouses') ?>"
                       class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Warehouses
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Warehouse Information -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">

            <!-- Main Info -->
            <div class="lg:col-span-2">
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">Warehouse Information</h2>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-500">Established</span>
                            <span class="text-sm font-medium text-gray-700"><?= date('M Y', strtotime($warehouse['created_at'])) ?></span>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Address Section -->
                        <div class="bg-gray-50 p-4 rounded-xl">
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <h3 class="text-sm font-semibold text-gray-900">Physical Location</h3>
                            </div>
                            <div class="text-sm text-gray-700 space-y-1">
                                <p><?= e($warehouse['address']) ?></p>
                                <p><?= e($warehouse['city']) ?>, <?= e($warehouse['state']) ?> <?= e($warehouse['zip_code']) ?></p>
                                <p class="font-medium"><?= e($warehouse['country']) ?></p>
                            </div>
                        </div>

                        <!-- Contact Section -->
                        <?php if (!empty($warehouse['phone']) || !empty($warehouse['email'])): ?>
                        <div class="bg-gray-50 p-4 rounded-xl">
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <h3 class="text-sm font-semibold text-gray-900">Contact Information</h3>
                            </div>
                            <div class="text-sm text-gray-700 space-y-2">
                                <?php if (!empty($warehouse['phone'])): ?>
                                    <div class="flex items-center">
                                        <span class="text-gray-500 mr-2">📞</span>
                                        <span><?= e($warehouse['phone']) ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if (!empty($warehouse['email'])): ?>
                                    <div class="flex items-center">
                                        <span class="text-gray-500 mr-2">📧</span>
                                        <span><?= e($warehouse['email']) ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Capacity & Utilization -->
                        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                                </svg>
                                <h3 class="text-sm font-semibold text-blue-900">Storage Capacity</h3>
                            </div>
                            <div class="space-y-3">
                                <?php if (!empty($warehouse['capacity'])): ?>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-blue-700">Total Capacity:</span>
                                        <span class="text-sm font-bold text-blue-900"><?= number_format($warehouse['capacity']) ?> units</span>
                                    </div>
                                <?php endif; ?>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-blue-700">Utilization:</span>
                                    <span class="text-sm font-bold text-blue-900"><?= $stats['utilization_percentage'] ?? 0 ?>%</span>
                                </div>
                                <div class="w-full bg-blue-200 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-300"
                                         style="width: <?= $stats['utilization_percentage'] ?? 0 ?>%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Status & Operations -->
                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200">
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h3 class="text-sm font-semibold text-green-900">Operational Status</h3>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-green-700">Status:</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-green-100 text-green-800">
                                        ✅ Active
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-green-700">Last Activity:</span>
                                    <span class="text-xs text-green-600"><?= date('M j, Y', strtotime($warehouse['updated_at'] ?? $warehouse['created_at'])) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($warehouse['description'])): ?>
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-900 mb-2">Description</h3>
                        <p class="text-gray-700 bg-gray-50 p-4 rounded-lg"><?= e($warehouse['description']) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Enhanced Statistics & Analytics -->
            <div>
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Analytics & Performance</h2>

                    <div class="space-y-6">
                        <!-- Storage Boxes Stat -->
                        <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <p class="text-2xl font-bold text-blue-900"><?= number_format($stats['total_boxes'] ?? 0) ?></p>
                            <p class="text-sm text-blue-700 font-medium">Storage Boxes</p>
                            <p class="text-xs text-blue-600 mt-1"><?= number_format($stats['occupied_boxes'] ?? 0) ?> occupied</p>
                        </div>

                        <!-- Documents Stat -->
                        <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <p class="text-2xl font-bold text-green-900"><?= number_format($stats['total_documents'] ?? 0) ?></p>
                            <p class="text-sm text-green-700 font-medium">Documents Stored</p>
                            <p class="text-xs text-green-600 mt-1">Across all boxes</p>
                        </div>

                        <!-- Storage Locations Stat -->
                        <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border border-purple-200">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                            <p class="text-2xl font-bold text-purple-900"><?= number_format($stats['total_locations'] ?? 0) ?></p>
                            <p class="text-sm text-purple-700 font-medium">Storage Locations</p>
                            <p class="text-xs text-purple-600 mt-1">Physical & digital</p>
                        </div>

                        <!-- Utilization Progress -->
                        <div class="pt-4 border-t border-gray-200">
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-sm font-medium text-gray-700">Warehouse Utilization</span>
                                <span class="text-sm font-semibold text-gray-900"><?= $stats['utilization_percentage'] ?? 0 ?>%</span>
                            </div>
                            <?php
                            $utilization = $stats['utilization_percentage'] ?? 0;
                            $progressColor = $utilization >= 90 ? 'from-red-500 to-red-600' : ($utilization >= 70 ? 'from-yellow-500 to-yellow-600' : 'from-green-500 to-green-600');
                            ?>
                            <div class="w-full bg-gray-200 rounded-full h-3 mb-2">
                                <div class="bg-gradient-to-r <?= $progressColor ?> h-3 rounded-full transition-all duration-500"
                                     style="width: <?= $utilization ?>%"></div>
                            </div>
                            <div class="flex items-center justify-between text-xs text-gray-600">
                                <span>0%</span>
                                <span>50%</span>
                                <span>100%</span>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="pt-4 border-t border-gray-200">
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Quick Actions</h4>
                            <div class="space-y-2">
                                <button onclick="optimizeLayout()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                                    🔧 Optimize Layout
                                </button>
                                <button onclick="bulkOperations()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                                    📦 Bulk Box Operations
                                </button>
                                <button onclick="inventoryCheck()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                                    📋 Inventory Check
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Flow Hierarchy - INTAKE → BUNDLE → BOX → STORAGE -->
        <?php if (!empty($hierarchy)): ?>
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Document Flow Hierarchy</h2>
                    <p class="text-sm text-gray-600 mt-1">INTAKE → BUNDLE → BOX → STORAGE workflow in this warehouse</p>
                </div>
                <div class="text-sm text-gray-500">
                    <?= count($hierarchy) ?> workflow entries
                </div>
            </div>

            <div class="space-y-4">
                <?php foreach ($hierarchy as $entry): ?>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4">
                        <div class="flex items-center space-x-4">
                            <!-- Intake -->
                            <div class="flex-1">
                                <div class="text-xs font-medium text-blue-600 uppercase">INTAKE</div>
                                <div class="text-sm font-semibold text-gray-900"><?= e($entry['intake_ref']) ?></div>
                                <div class="text-xs text-gray-600"><?= e($entry['client_name']) ?></div>
                                <div class="text-xs text-<?= $entry['intake_status'] === 'completed' ? 'green' : 'yellow' ?>-600">
                                    <?= ucfirst($entry['intake_status']) ?>
                                </div>
                            </div>

                            <div class="text-gray-400">→</div>

                            <!-- Bundle -->
                            <div class="flex-1">
                                <div class="text-xs font-medium text-purple-600 uppercase">BUNDLE</div>
                                <?php if ($entry['bundle_name']): ?>
                                    <div class="text-sm font-semibold text-gray-900"><?= e($entry['bundle_name']) ?></div>
                                    <div class="text-xs text-gray-600"><?= e($entry['bundle_ref']) ?></div>
                                    <div class="text-xs text-green-600"><?= $entry['document_count'] ?> documents</div>
                                <?php else: ?>
                                    <div class="text-sm text-gray-500 italic">Not assigned</div>
                                <?php endif; ?>
                            </div>

                            <div class="text-gray-400">→</div>

                            <!-- Box -->
                            <div class="flex-1">
                                <div class="text-xs font-medium text-green-600 uppercase">BOX</div>
                                <?php if ($entry['box_identifier']): ?>
                                    <div class="text-sm font-semibold text-gray-900"><?= e($entry['box_identifier']) ?></div>
                                    <div class="text-xs text-gray-600"><?= e($entry['box_name']) ?></div>
                                    <div class="text-xs text-blue-600"><?= e($entry['storage_location_code']) ?></div>
                                <?php else: ?>
                                    <div class="text-sm text-gray-500 italic">Not assigned</div>
                                <?php endif; ?>
                            </div>

                            <div class="text-gray-400">→</div>

                            <!-- Storage -->
                            <div class="flex-1">
                                <div class="text-xs font-medium text-orange-600 uppercase">STORAGE</div>
                                <div class="text-sm font-semibold text-gray-900"><?= e($entry['warehouse_name']) ?></div>
                                <div class="text-xs text-gray-600">Physical Storage</div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Enhanced Storage Boxes Overview -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Storage Boxes Overview</h2>
                    <p class="text-sm text-gray-600 mt-1">Physical storage containers and their contents in this warehouse</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">View:</label>
                        <select id="viewMode" class="text-sm border border-gray-300 rounded-lg px-3 py-1">
                            <option value="cards">Card View</option>
                            <option value="table">Table View</option>
                            <option value="layout">Layout View</option>
                        </select>
                    </div>
                    <a href="<?= url('/app/boxes/create?warehouse_id=' . $warehouse['id']) ?>"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Add Box
                    </a>
                    <a href="<?= url('/app/storage-locations/create?warehouse_id=' . $warehouse['id']) ?>"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Location
                    </a>
                </div>
            </div>

            <!-- Filter and Search -->
            <div class="mb-6 flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Filter by status:</label>
                        <select id="statusFilter" class="text-sm border border-gray-300 rounded-lg px-3 py-1">
                            <option value="">All Status</option>
                            <option value="empty">Empty</option>
                            <option value="partial">Partial</option>
                            <option value="full">Full</option>
                            <option value="sealed">Sealed</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Sort by:</label>
                        <select id="sortFilter" class="text-sm border border-gray-300 rounded-lg px-3 py-1">
                            <option value="location">Location</option>
                            <option value="name">Name</option>
                            <option value="capacity">Capacity</option>
                            <option value="documents">Documents</option>
                        </select>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    <?= count(array_filter($storageLocations, function($item) { return $item['item_type'] === 'box'; })) ?> boxes,
                    <?= count(array_filter($storageLocations, function($item) { return $item['item_type'] === 'storage_location'; })) ?> locations
                </div>
            </div>

            <?php if (empty($storageLocations)): ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No storage boxes or locations</h3>
                    <p class="text-gray-600 mb-4">This warehouse doesn't have any storage boxes or locations yet. Start by creating your first storage box.</p>
                    <div class="flex items-center justify-center space-x-3">
                        <a href="<?= url('/app/boxes/create?warehouse_id=' . $warehouse['id']) ?>"
                           class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            Create First Box
                        </a>
                        <a href="<?= url('/app/storage-locations/create?warehouse_id=' . $warehouse['id']) ?>"
                           class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create First Location
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <!-- Card View (Default) -->
                <div id="cardView" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($storageLocations as $location): ?>
                        <?php if ($location['item_type'] === 'box'): ?>
                            <!-- Storage Box Card -->
                            <div class="storage-item bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200 group"
                                 data-status="<?= $location['occupancy_status'] ?>"
                                 data-name="<?= strtolower($location['name']) ?>"
                                 data-location="<?= strtolower($location['storage_location_code']) ?>"
                                 data-capacity="<?= $location['capacity'] ?>"
                                 data-documents="<?= $location['document_count'] ?>">

                                <!-- Box Header -->
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                            <a href="<?= url('/app/boxes/' . $location['id']) ?>">
                                                <?= e($location['name']) ?>
                                            </a>
                                        </h3>
                                        <p class="text-sm font-mono text-blue-600 mt-1"><?= e($location['box_id']) ?></p>
                                    </div>
                                    <div class="text-right">
                                        <?php
                                        $statusColors = [
                                            'empty' => 'bg-gray-100 text-gray-800',
                                            'partial' => 'bg-yellow-100 text-yellow-800',
                                            'full' => 'bg-red-100 text-red-800',
                                            'sealed' => 'bg-blue-100 text-blue-800'
                                        ];
                                        $statusColor = $statusColors[$location['occupancy_status']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium <?= $statusColor ?>">
                                            <?= ucfirst($location['occupancy_status']) ?>
                                        </span>
                                    </div>
                                </div>

                                <!-- Storage Location -->
                                <div class="mb-4">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        </svg>
                                        <span class="text-sm font-mono font-medium text-green-700 bg-green-50 px-2 py-1 rounded">
                                            <?= e($location['storage_location_code']) ?>
                                        </span>
                                    </div>
                                    <p class="text-xs text-gray-600 ml-6">
                                        Row <?= e($location['row_number']) ?>, Shelf <?= e($location['shelf_number']) ?>, Position <?= e($location['position_number']) ?>
                                    </p>
                                </div>

                                <!-- Statistics -->
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                                        <div class="text-lg font-bold text-blue-600"><?= number_format($location['document_count']) ?></div>
                                        <div class="text-xs text-blue-600">Documents</div>
                                    </div>
                                    <div class="text-center p-3 bg-green-50 rounded-lg">
                                        <div class="text-lg font-bold text-green-600"><?= number_format($location['bundle_count']) ?></div>
                                        <div class="text-xs text-green-600">Bundles</div>
                                    </div>
                                </div>

                                <!-- Capacity Bar -->
                                <div class="mb-4">
                                    <div class="flex items-center justify-between text-sm mb-2">
                                        <span class="text-gray-600">Capacity</span>
                                        <span class="font-medium text-gray-900">
                                            <?= number_format($location['current_count']) ?> / <?= number_format($location['capacity']) ?>
                                        </span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <?php
                                        $percentage = $location['capacity'] > 0 ? ($location['current_count'] / $location['capacity']) * 100 : 0;
                                        $colorClass = $percentage >= 90 ? 'bg-red-500' : ($percentage >= 70 ? 'bg-yellow-500' : 'bg-green-500');
                                        ?>
                                        <div class="<?= $colorClass ?> h-2 rounded-full transition-all duration-300" style="width: <?= min($percentage, 100) ?>%"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1"><?= number_format($percentage, 1) ?>% full</p>
                                </div>

                                <!-- Bundle Names (if any) -->
                                <?php if (!empty($location['bundle_names'])): ?>
                                <div class="mb-4">
                                    <p class="text-xs text-gray-500 mb-1">Contains bundles:</p>
                                    <p class="text-xs text-blue-600 bg-blue-50 p-2 rounded" title="<?= e($location['bundle_names']) ?>">
                                        <?= e(strlen($location['bundle_names']) > 50 ? substr($location['bundle_names'], 0, 50) . '...' : $location['bundle_names']) ?>
                                    </p>
                                </div>
                                <?php endif; ?>

                                <!-- Actions -->
                                <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                                    <a href="<?= url('/app/boxes/' . $location['id']) ?>"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details
                                    </a>
                                    <div class="flex items-center space-x-2">
                                        <a href="<?= url('/app/boxes/' . $location['id'] . '/edit') ?>"
                                           class="text-yellow-600 hover:text-yellow-800 text-sm font-medium">
                                            Edit
                                        </a>
                                        <button onclick="printBoxLabel(<?= $location['id'] ?>)"
                                                class="text-green-600 hover:text-green-800 text-sm font-medium">
                                            Print
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Storage Location Card -->
                            <div class="storage-item bg-gradient-to-br from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200"
                                 data-status="<?= $location['status'] ?>"
                                 data-name="<?= strtolower($location['name']) ?>"
                                 data-capacity="<?= $location['capacity'] ?? 0 ?>"
                                 data-documents="<?= $location['document_count'] ?>">

                                <div class="flex items-start justify-between mb-4">
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            <a href="<?= url('/app/storage-locations/' . $location['id']) ?>" class="hover:text-indigo-600">
                                                <?= e($location['name']) ?>
                                            </a>
                                        </h3>
                                        <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-indigo-100 text-indigo-800 mt-2">
                                            📍 <?= ucfirst($location['type']) ?> Location
                                        </span>
                                    </div>
                                    <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-green-100 text-green-800">
                                        <?= ucfirst($location['status']) ?>
                                    </span>
                                </div>

                                <div class="text-center p-4 bg-indigo-100 rounded-lg mb-4">
                                    <div class="text-2xl font-bold text-indigo-600"><?= number_format($location['document_count']) ?></div>
                                    <div class="text-sm text-indigo-600">Documents</div>
                                </div>

                                <div class="flex items-center justify-between pt-4 border-t border-indigo-200">
                                    <a href="<?= url('/app/storage-locations/' . $location['id']) ?>"
                                       class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                                        View Details
                                    </a>
                                    <a href="<?= url('/app/storage-locations/' . $location['id'] . '/edit') ?>"
                                       class="text-yellow-600 hover:text-yellow-800 text-sm font-medium">
                                        Edit
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Warehouse Activity Summary -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Recent Activity</h2>
                    <p class="text-sm text-gray-600 mt-1">Latest operations and changes in this warehouse</p>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="refreshActivity()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        🔄 Refresh
                    </button>
                    <a href="<?= url('/app/warehouses/' . $warehouse['id'] . '/activity') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View All Activity
                    </a>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Recent Box Additions -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <h3 class="text-sm font-semibold text-green-900">New Boxes</h3>
                    </div>
                    <p class="text-2xl font-bold text-green-600"><?= count(array_filter($storageLocations, function($item) { return $item['item_type'] === 'box' && strtotime($item['created_at']) > strtotime('-7 days'); })) ?></p>
                    <p class="text-xs text-green-600">Added this week</p>
                </div>

                <!-- Document Processing -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-sm font-semibold text-blue-900">Documents</h3>
                    </div>
                    <p class="text-2xl font-bold text-blue-600"><?= $stats['total_documents'] ?? 0 ?></p>
                    <p class="text-xs text-blue-600">Total stored</p>
                </div>

                <!-- Workflow Status -->
                <div class="bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-200 rounded-xl p-4">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-sm font-semibold text-purple-900">Workflow</h3>
                    </div>
                    <p class="text-2xl font-bold text-purple-600"><?= count($hierarchy ?? []) ?></p>
                    <p class="text-xs text-purple-600">Active processes</p>
                </div>
            </div>

            <!-- Quick Actions for Warehouse -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-semibold text-gray-900 mb-4">Warehouse Operations</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <button onclick="generateWarehouseReport()" class="flex items-center justify-center px-4 py-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                        <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="text-sm font-medium text-blue-700">Generate Report</span>
                    </button>

                    <button onclick="printWarehouseLayout()" class="flex items-center justify-center px-4 py-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                        <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        <span class="text-sm font-medium text-green-700">Print Layout</span>
                    </button>

                    <button onclick="optimizeLayout()" class="flex items-center justify-center px-4 py-3 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors">
                        <svg class="w-4 h-4 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="text-sm font-medium text-yellow-700">Optimize</span>
                    </button>

                    <button onclick="inventoryCheck()" class="flex items-center justify-center px-4 py-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                        <svg class="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                        </svg>
                        <span class="text-sm font-medium text-purple-700">Inventory</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Debug Information (only shown when ?debug=1 is in URL) -->
        <?php if ($debug): ?>
        <div class="bg-yellow-50 border border-yellow-200 rounded-3xl shadow-lg p-8 mt-8">
            <div class="flex items-center mb-4">
                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h2 class="text-xl font-bold text-yellow-800">Debug Information</h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-900">Storage Summary</h3>
                    <p class="text-sm text-gray-600">Boxes: <?= $debug['total_boxes_in_warehouse'] ?></p>
                    <p class="text-sm text-gray-600">Locations: <?= $debug['total_storage_locations'] ?></p>
                    <p class="text-sm text-gray-600">Hierarchy Entries: <?= $debug['hierarchy_entries'] ?></p>
                </div>

                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-900">Workflow Status</h3>
                    <p class="text-sm text-gray-600">
                        <?php if ($debug['hierarchy_entries'] > 0): ?>
                            ✅ Workflow data found
                        <?php else: ?>
                            ❌ No workflow data
                        <?php endif; ?>
                    </p>
                    <p class="text-sm text-gray-600">
                        <?php if ($debug['total_boxes_in_warehouse'] > 0): ?>
                            ✅ Boxes exist
                        <?php else: ?>
                            ❌ No boxes found
                        <?php endif; ?>
                    </p>
                </div>

                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-900">Troubleshooting</h3>
                    <p class="text-sm text-gray-600">
                        <?php if ($debug['hierarchy_entries'] === 0 && $debug['total_boxes_in_warehouse'] === 0): ?>
                            No intake has been processed to create boxes in this warehouse yet.
                        <?php elseif ($debug['total_boxes_in_warehouse'] > 0 && $debug['hierarchy_entries'] === 0): ?>
                            Boxes exist but no intake workflow data found. Check bundle-box relationships.
                        <?php else: ?>
                            Workflow appears to be functioning correctly.
                        <?php endif; ?>
                    </p>
                </div>
            </div>

            <?php if (!empty($debug['raw_hierarchy'])): ?>
            <div class="bg-white p-4 rounded-lg">
                <h3 class="font-semibold text-gray-900 mb-2">Raw Hierarchy Data</h3>
                <div class="text-xs text-gray-600 font-mono bg-gray-100 p-2 rounded overflow-x-auto">
                    <pre><?= htmlspecialchars(json_encode($debug['raw_hierarchy'], JSON_PRETTY_PRINT)) ?></pre>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Enhanced Warehouse Management JavaScript

// Filter and sort functionality
document.addEventListener('DOMContentLoaded', function() {
    const statusFilter = document.getElementById('statusFilter');
    const sortFilter = document.getElementById('sortFilter');
    const viewMode = document.getElementById('viewMode');
    const cardView = document.getElementById('cardView');

    if (statusFilter && sortFilter) {
        function filterAndSort() {
            const items = Array.from(document.querySelectorAll('.storage-item'));
            const statusValue = statusFilter.value;
            const sortValue = sortFilter.value;

            // Filter
            items.forEach(item => {
                const itemStatus = item.dataset.status;
                if (!statusValue || itemStatus === statusValue) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });

            // Sort visible items
            const visibleItems = items.filter(item => item.style.display !== 'none');
            visibleItems.sort((a, b) => {
                switch (sortValue) {
                    case 'location':
                        return (a.dataset.location || '').localeCompare(b.dataset.location || '');
                    case 'name':
                        return (a.dataset.name || '').localeCompare(b.dataset.name || '');
                    case 'capacity':
                        return parseInt(b.dataset.capacity || 0) - parseInt(a.dataset.capacity || 0);
                    case 'documents':
                        return parseInt(b.dataset.documents || 0) - parseInt(a.dataset.documents || 0);
                    default:
                        return 0;
                }
            });

            // Reorder in DOM
            if (cardView) {
                visibleItems.forEach(item => cardView.appendChild(item));
            }
        }

        statusFilter.addEventListener('change', filterAndSort);
        sortFilter.addEventListener('change', filterAndSort);
    }
});

// Warehouse management functions
function generateWarehouseReport() {
    const warehouseId = <?= (int)$warehouse['id'] ?>;
    window.open(`<?= url('/app/warehouses/') ?>${warehouseId}/report`, '_blank');
}

function printWarehouseLayout() {
    const warehouseInfo = {
        name: <?= json_encode($warehouse['name']) ?>,
        address: <?= json_encode($warehouse['address']) ?>,
        city: <?= json_encode($warehouse['city']) ?>,
        state: <?= json_encode($warehouse['state']) ?>,
        totalBoxes: <?= (int)($stats['total_boxes'] ?? 0) ?>,
        totalDocuments: <?= (int)($stats['total_documents'] ?? 0) ?>,
        utilization: <?= (int)($stats['utilization_percentage'] ?? 0) ?>
    };

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Warehouse Layout - ${warehouseInfo.name}</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 20px; }
                    .warehouse-info { margin: 20px 0; }
                    .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
                    .info-item { padding: 10px; border: 1px solid #ddd; }
                    .stats { background: #f5f5f5; padding: 15px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Warehouse Layout Report</h1>
                    <h2>\${warehouseInfo.name}</h2>
                    <p>Generated on \${new Date().toLocaleDateString()}</p>
                </div>

                <div class="warehouse-info">
                    <h3>Warehouse Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Address:</strong><br>
                            \${warehouseInfo.address}<br>
                            \${warehouseInfo.city}, \${warehouseInfo.state}
                        </div>
                        <div class="info-item">
                            <strong>Statistics:</strong><br>
                            Total Boxes: \${warehouseInfo.totalBoxes}<br>
                            Total Documents: \${warehouseInfo.totalDocuments}<br>
                            Utilization: \${warehouseInfo.utilization}%
                        </div>
                    </div>
                </div>

                <div class="stats">
                    <h3>Storage Overview</h3>
                    <p>This warehouse contains \${warehouseInfo.totalBoxes} storage boxes with \${warehouseInfo.totalDocuments} documents.</p>
                    <p>Current utilization rate: \${warehouseInfo.utilization}%</p>
                </div>

                <div class="footer">
                    <p>Document Management System - Warehouse Layout Report</p>
                    <p>For detailed box contents, please refer to individual box reports.</p>
                </div>

                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                <\/script>
            </body>
        </html>
    `);
    printWindow.document.close();
}

// Box management functions
function printBoxLabel(boxId) {
    window.open(`<?= url('/app/boxes/') ?>${boxId}?action=print_label`, '_blank');
}

// Quick action functions
function optimizeLayout() {
    if (confirm('This will analyze and suggest optimal box placement. Continue?')) {
        alert('Layout optimization feature - to be implemented');
    }
}

function bulkOperations() {
    alert('Bulk operations panel - to be implemented');
}

function inventoryCheck() {
    if (confirm('This will perform a comprehensive inventory check. Continue?')) {
        alert('Inventory check feature - to be implemented');
    }
}

// View mode switching (for future enhancement)
function switchViewMode(mode) {
    const cardView = document.getElementById('cardView');

    switch (mode) {
        case 'cards':
            if (cardView) cardView.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
            break;
        case 'table':
            // Future: Switch to table view
            alert('Table view - to be implemented');
            break;
        case 'layout':
            // Future: Switch to layout view
            alert('Layout view - to be implemented');
            break;
    }
}

// Refresh activity function
function refreshActivity() {
    // Future: AJAX refresh of activity data
    location.reload();
}

// Auto-refresh stats every 5 minutes
setInterval(function() {
    // Future: Auto-refresh warehouse statistics
    console.log('Auto-refresh warehouse stats');
}, 300000);
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
