<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? e($title) . ' - ' : '' ?><?= e($config['name'] ?? 'DMS') ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?= url('/assets/css/app.css') ?>">
    <?php if (isset($page_css)): ?>
        <?php foreach ($page_css as $css): ?>
            <link rel="stylesheet" href="<?= $css ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Meta tags -->
    <meta name="description" content="Document Management System for efficient document storage and retrieval">
    <meta name="author" content="DMS Team">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= $_SESSION['csrf_token'] ?? '' ?>">
    
    <script>
        // Configure Tailwind
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 font-sans antialiased min-h-screen">
    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div id="flash-message" class="fixed top-20 right-4 z-[9999] max-w-sm">
            <div class="bg-<?= $_SESSION['flash_type'] === 'error' ? 'red' : 'green' ?>-500 text-white px-6 py-4 rounded-lg shadow-lg">
                <div class="flex items-center justify-between">
                    <span><?= e($_SESSION['flash_message']) ?></span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <?php 
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        ?>
    <?php endif; ?>

    <!-- Navigation -->
    <?php if (isset($user) && $user): ?>
        <nav class="bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20 sticky top-0 z-50 py-2">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-20">
                    <!-- Logo and Navigation -->
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <a href="<?= url('/dashboard') ?>" class="flex items-center space-x-2 text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-blue-700 hover:to-purple-700 transition-all duration-200">
                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <span><?= e($config['name'] ?? 'DMS') ?></span>
                            </a>
                        </div>

                        <!-- Enhanced Main Navigation -->
                        <div class="hidden sm:ml-8 sm:flex sm:space-x-2">
                            <?php
                            // Get current path and clean it
                            $currentPath = $_SERVER['REQUEST_URI'] ?? '';
                            $currentPath = parse_url($currentPath, PHP_URL_PATH); // Remove query parameters
                            $currentPath = rtrim($currentPath, '/'); // Remove trailing slash

                            // Debug: Let's see what the current path is
                            // echo "<!-- Current Path: " . $currentPath . " -->";

                            $menuItems = [
                                [
                                    'url' => '/dashboard',
                                    'label' => 'Dashboard',
                                    'patterns' => ['/dashboard', '/dms/public/dashboard']
                                ],
                                [
                                    'url' => '/app/intake',
                                    'label' => 'Intake',
                                    'patterns' => ['/app/intake', '/dms/public/app/intake']
                                ],
                                [
                                    'url' => '/app/bundles',
                                    'label' => 'Bundles',
                                    'patterns' => ['/app/bundles', '/dms/public/app/bundles']
                                ],
                                [
                                    'url' => '/app/boxes',
                                    'label' => 'Boxes',
                                    'patterns' => ['/app/boxes', '/dms/public/app/boxes']
                                ],
                                [
                                    'url' => '/app/warehouses',
                                    'label' => 'Warehouses',
                                    'patterns' => ['/app/warehouses', '/dms/public/app/warehouses']
                                ],
                                [
                                    'url' => '/app/search',
                                    'label' => 'Search',
                                    'patterns' => ['/app/search', '/dms/public/app/search']
                                ],
                                [
                                    'url' => '/app/billing',
                                    'label' => 'Billing',
                                    'patterns' => ['/app/billing', '/dms/public/app/billing']
                                ]
                            ];

                            foreach ($menuItems as $item):
                                $isActive = false;
                                foreach ($item['patterns'] as $pattern) {
                                    // Check if current path matches or starts with the pattern
                                    if ($currentPath === $pattern || strpos($currentPath, $pattern) === 0) {
                                        $isActive = true;
                                        break;
                                    }
                                }

                                $activeClasses = $isActive
                                    ? 'bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 border-blue-500 shadow-md'
                                    : 'text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/50 border-transparent hover:border-blue-300';
                            ?>
                                <a href="<?= url($item['url']) ?>"
                                   class="group relative flex items-center px-5 py-3 rounded-xl font-medium text-sm transition-all duration-300 border-2 <?= $activeClasses ?> hover:shadow-lg hover:-translate-y-0.5">
                                    <span class="font-semibold"><?= $item['label'] ?></span>
                                    <?php if ($isActive): ?>
                                        <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></div>
                                    <?php endif; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="sm:hidden">
                        <button type="button"
                                class="inline-flex items-center justify-center p-2 rounded-xl text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                                aria-controls="mobile-menu"
                                aria-expanded="false"
                                onclick="toggleMobileMenu()">
                            <span class="sr-only">Open main menu</span>
                            <svg class="block h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="menu-icon">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                            <svg class="hidden h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="close-icon">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- User Menu -->
                    <div class="flex items-center space-x-3">
                        <!-- Enhanced Notifications -->
                        <button class="relative p-2.5 bg-white/90 backdrop-blur-sm rounded-xl text-gray-500 hover:text-blue-600 hover:bg-blue-50/50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300 shadow-sm hover:shadow-lg hover:-translate-y-0.5 menu-item">
                            <span class="sr-only">View notifications</span>
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6a2 2 0 012 2v9a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2z"></path>
                            </svg>
                            <!-- Enhanced Notification badge -->
                            <div class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full border-2 border-white notification-badge shadow-lg"></div>
                        </button>

                        <!-- Profile dropdown -->
                        <div class="relative">
                            <div>
                                <button type="button" class="flex items-center space-x-3 p-2 bg-white/80 backdrop-blur-sm rounded-xl hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                    <div class="h-8 w-8 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm shadow-lg">
                                        <?php if ($user && isset($user['first_name']) && isset($user['last_name'])): ?>
                                            <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                                        <?php else: ?>
                                            U
                                        <?php endif; ?>
                                    </div>
                                    <div class="hidden md:block text-left">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php if ($user && isset($user['first_name'])): ?>
                                                <?= e($user['first_name'] . ' ' . ($user['last_name'] ?? '')) ?>
                                            <?php else: ?>
                                                User
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            <?= e($user['email'] ?? '<EMAIL>') ?>
                                        </div>
                                    </div>
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                            </div>
                            
                            <!-- Dropdown menu -->
                            <div class="hidden origin-top-right absolute right-0 mt-3 w-64 rounded-2xl shadow-xl py-2 bg-white/95 backdrop-blur-md ring-1 ring-black ring-opacity-5 focus:outline-none border border-white/20" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1" id="user-menu">
                                <?php if ($user): ?>
                                    <div class="px-4 py-3 border-b border-gray-100">
                                        <div class="flex items-center space-x-3">
                                            <div class="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold shadow-lg">
                                                <?php if ($user && isset($user['first_name']) && isset($user['last_name'])): ?>
                                                    <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                                                <?php else: ?>
                                                    U
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-900"><?= e(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')) ?></div>
                                                <div class="text-sm text-gray-500"><?= e($user['email'] ?? '') ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="py-1">
                                        <a href="<?= url('/app/profile') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                            Your Profile
                                        </a>
                                        <?php if (isset($user['role']) && $user['role'] === 'super_admin'): ?>
                                            <a href="<?= url('/super-admin/dashboard') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                                </svg>
                                                Super Admin
                                            </a>
                                            <a href="<?= url('/super-admin/warehouse-management') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 00-2 2v2a2 2 0 002 2m0 0h14m-14 0a2 2 0 002 2v2a2 2 0 01-2 2"></path>
                                                </svg>
                                                Warehouse Management
                                            </a>
                                            <a href="<?= url('/super-admin/barcode-management') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                                                </svg>
                                                Barcode Management
                                            </a>
                                            <a href="<?= url('/super-admin/compliance-management') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                Compliance Management
                                            </a>
                                            <a href="<?= url('/super-admin/system-monitoring') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                </svg>
                                                System Monitoring
                                            </a>
                                            <a href="<?= url('/super-admin/business-intelligence') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                                </svg>
                                                Business Intelligence
                                            </a>
                                            <a href="<?= url('/app/companies') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                                </svg>
                                                Manage Companies
                                            </a>
                                            <a href="<?= url('/app/users') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                                </svg>
                                                Manage Users
                                            </a>
                                        <?php elseif (isset($user['role']) && in_array($user['role'], ['company_admin'])): ?>
                                            <a href="<?= url('/app/companies/' . $user['company_id']) ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                                </svg>
                                                Company Settings
                                            </a>
                                            <a href="<?= url('/app/users') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 715 0z"></path>
                                                </svg>
                                                Manage Users
                                            </a>
                                        <?php endif; ?>
                                        <a href="<?= url('/app/reports') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors rounded-lg mx-2" role="menuitem">
                                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                            </svg>
                                            Reports
                                        </a>
                                    </div>
                                    <div class="border-t border-gray-100 pt-1">
                                        <form method="POST" action="<?= url('/logout') ?>" class="block">
                                            <button type="submit" class="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors rounded-lg mx-2" role="menuitem">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                                </svg>
                                                Sign out
                                            </button>
                                        </form>
                                    </div>
                                <?php else: ?>
                                    <div class="px-4 py-2 text-sm text-gray-700">
                                        <a href="<?= url('/login') ?>" class="text-blue-600 hover:text-blue-700 font-medium">Please log in</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Mobile Menu -->
            <div class="sm:hidden hidden" id="mobile-menu">
                <div class="px-4 pt-2 pb-4 space-y-2 bg-white/95 backdrop-blur-md border-t border-gray-200/50">
                    <?php foreach ($menuItems as $item):
                        $isActive = false;
                        foreach ($item['patterns'] as $pattern) {
                            // Check if current path matches or starts with the pattern
                            if ($currentPath === $pattern || strpos($currentPath, $pattern) === 0) {
                                $isActive = true;
                                break;
                            }
                        }

                        $activeClasses = $isActive
                            ? 'bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-600 border-l-4 border-blue-500'
                            : 'text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/50 border-l-4 border-transparent hover:border-blue-300';
                    ?>
                        <a href="<?= url($item['url']) ?>"
                           class="group flex items-center px-4 py-3 rounded-r-xl font-medium text-base transition-all duration-300 <?= $activeClasses ?>">
                            <span class="font-semibold"><?= $item['label'] ?></span>
                            <?php if ($isActive): ?>
                                <div class="ml-auto w-2 h-2 bg-blue-500 rounded-full"></div>
                            <?php endif; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </nav>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="<?= isset($user) && $user ? 'py-8' : '' ?>">
        <?= $content ?? '' ?>
    </main>

    <!-- Footer -->
    <footer class="bg-white/80 backdrop-blur-md border-t border-white/20 mt-auto">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <span class="text-sm font-semibold text-gray-700"><?= e($config['name'] ?? 'DMS') ?></span>
                    </div>
                    <div class="text-sm text-gray-500">
                        © <?= date('Y') ?> All rights reserved.
                    </div>
                </div>
                <div class="flex items-center space-x-6">
                    <div class="text-sm text-gray-500">
                        Version <?= e($config['version'] ?? '1.0.0') ?>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-500">System Online</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Enhanced Menu Styles -->
    <style>
        /* Enhanced menu animations */
        .nav-item {
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        /* Mobile menu slide animation */
        #mobile-menu {
            transition: all 0.3s ease-in-out;
            transform: translateY(-10px);
            opacity: 0;
        }

        #mobile-menu:not(.hidden) {
            transform: translateY(0);
            opacity: 1;
        }

        /* Active menu item glow effect */
        .menu-active {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }

        /* Notification pulse animation */
        @keyframes notification-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .notification-badge {
            animation: notification-pulse 2s infinite;
        }

        /* Smooth hover transitions for all menu items */
        .menu-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .menu-item:hover {
            transform: translateY(-2px);
        }

        /* Glass morphism effect enhancement */
        .glass-nav {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>

    <!-- JavaScript -->
    <script src="<?= url('/assets/js/app.js') ?>"></script>
    
    <!-- Auto-hide flash messages -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessage = document.getElementById('flash-message');
            if (flashMessage) {
                setTimeout(() => {
                    flashMessage.remove();
                }, 5000);
            }
            
            // User menu toggle
            const userMenuButton = document.getElementById('user-menu-button');
            const userMenu = document.getElementById('user-menu');

            if (userMenuButton && userMenu) {
                userMenuButton.addEventListener('click', function() {
                    userMenu.classList.toggle('hidden');
                });

                // Close menu when clicking outside
                document.addEventListener('click', function(event) {
                    if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
                        userMenu.classList.add('hidden');
                    }
                });
            }
        });

        // Mobile menu toggle function
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');
            const closeIcon = document.getElementById('close-icon');

            if (mobileMenu && menuIcon && closeIcon) {
                mobileMenu.classList.toggle('hidden');
                menuIcon.classList.toggle('hidden');
                closeIcon.classList.toggle('hidden');
                menuIcon.classList.toggle('block');
                closeIcon.classList.toggle('block');
            }
        }
    </script>
    
    <?= $scripts ?? '' ?>
</body>
</html>
