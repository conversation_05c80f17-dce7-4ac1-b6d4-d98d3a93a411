<?php

namespace App\Controllers;

/**
 * Test Controller
 * 
 * Handles test pages and debugging functionality
 */
class TestController extends BaseController
{
    /**
     * Show business services test page
     */
    public function businessServices()
    {
        $this->requireAuth();

        try {
            $this->view('test/business_services', [
                'title' => 'Business Services Test',
                'user' => $this->user,
                'company' => $this->company
            ]);
        } catch (\Exception $e) {
            echo "<h1>Test Page</h1>";
            echo "<p>Error loading view: " . $e->getMessage() . "</p>";
            echo "<p>User: " . ($this->user ? $this->user['email'] : 'Not logged in') . "</p>";
            echo "<p>View file should be at: " . APP_ROOT . "/src/views/test/business_services.php</p>";
            echo "<p>File exists: " . (file_exists(APP_ROOT . "/src/views/test/business_services.php") ? 'Yes' : 'No') . "</p>";
        }
    }
    
    /**
     * Show routing debug information
     */
    public function routeDebug()
    {
        $this->requireAuth();
        
        // Get current request info
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        echo "<h1>Route Debug Information</h1>";
        echo "<p><strong>Method:</strong> {$method}</p>";
        echo "<p><strong>URI:</strong> {$uri}</p>";
        echo "<p><strong>User:</strong> " . ($this->user ? $this->user['email'] : 'Not logged in') . "</p>";
        
        // Show available routes
        echo "<h2>Available Routes</h2>";
        echo "<ul>";
        echo "<li>GET /app/test/business-services - Business Services Test Page</li>";
        echo "<li>GET /app/services/status - Service Status API</li>";
        echo "<li>POST /app/services/boxes/create - Create Box API</li>";
        echo "<li>POST /app/services/bundles/create - Create Bundle API</li>";
        echo "<li>POST /app/services/delivery/create - Create Delivery API</li>";
        echo "<li>GET /app/services/search - Search API</li>";
        echo "</ul>";
    }
}
