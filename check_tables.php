<?php
require_once 'src/autoload.php';

use App\Core\Database;

$db = Database::getInstance();

echo "Checking bundles table structure:\n";
try {
    $schema = $db->getTableSchema('bundles');
    foreach ($schema as $column) {
        echo $column['Field'] . ' - ' . $column['Type'] . "\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nChecking if boxes table exists:\n";
try {
    if ($db->tableExists('boxes')) {
        echo "✓ Boxes table exists\n";
        $schema = $db->getTableSchema('boxes');
        foreach ($schema as $column) {
            echo $column['Field'] . ' - ' . $column['Type'] . "\n";
        }
    } else {
        echo "✗ Boxes table does not exist\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nChecking document_intake table structure:\n";
try {
    $schema = $db->getTableSchema('document_intake');
    foreach ($schema as $column) {
        echo $column['Field'] . ' - ' . $column['Type'] . "\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
