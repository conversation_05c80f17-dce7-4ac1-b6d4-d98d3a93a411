-- Create delivery-related tables

-- Delivery requests table
CREATE TABLE IF NOT EXISTS delivery_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    delivery_reference VARCHAR(50) UNIQUE NOT NULL,
    
    -- Target information
    target_type <PERSON>NUM('box', 'bundle', 'document') NOT NULL,
    target_id INT NOT NULL,
    
    -- Client information
    client_id INT,
    client_name VARCHAR(255),
    
    -- Delivery details
    delivery_type ENUM('physical', 'digital', 'both') DEFAULT 'physical',
    delivery_method ENUM('courier', 'postal', 'express', 'pickup', 'email') DEFAULT 'courier',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    
    -- Scheduling
    requested_delivery_date DATE,
    scheduled_delivery_date DATE,
    
    -- Address and instructions
    delivery_address TEXT,
    special_instructions TEXT,
    
    -- Status tracking
    status ENUM('pending', 'approved', 'rejected', 'scheduled', 'dispatched', 'delivered', 'failed', 'returned') DEFAULT 'pending',
    
    -- Approval workflow
    approved_by INT,
    approved_at TIMESTAMP NULL,
    rejected_by INT,
    rejected_at TIMESTAMP NULL,
    rejection_reason TEXT,
    
    -- Dispatch information
    dispatched_at TIMESTAMP NULL,
    courier_id INT,
    tracking_number VARCHAR(100),
    
    -- Completion
    delivered_at TIMESTAMP NULL,
    recipient_name VARCHAR(255),
    recipient_signature TEXT,
    
    -- Return information
    returned_at TIMESTAMP NULL,
    
    -- Audit fields
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_delivery_company (company_id),
    INDEX idx_delivery_status (status),
    INDEX idx_delivery_target (target_type, target_id),
    INDEX idx_delivery_client (client_id),
    INDEX idx_delivery_date (requested_delivery_date)
);

-- Delivery items table (for detailed item tracking)
CREATE TABLE IF NOT EXISTS delivery_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_id INT NOT NULL,
    
    -- Item details
    item_type ENUM('box', 'bundle', 'document') NOT NULL,
    item_id INT NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    quantity INT DEFAULT 1,
    
    -- Item status
    status ENUM('pending', 'prepared', 'dispatched', 'delivered', 'returned') DEFAULT 'pending',
    
    -- Notes
    notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (delivery_id) REFERENCES delivery_requests(id) ON DELETE CASCADE,
    
    INDEX idx_delivery_items_delivery (delivery_id),
    INDEX idx_delivery_items_item (item_type, item_id)
);

-- Delivery tracking table (for status updates and location tracking)
CREATE TABLE IF NOT EXISTS delivery_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_id INT NOT NULL,
    
    -- Status information
    status VARCHAR(50) NOT NULL,
    location VARCHAR(255),
    notes TEXT,
    
    -- Timestamp and user
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (delivery_id) REFERENCES delivery_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_delivery_tracking_delivery (delivery_id),
    INDEX idx_delivery_tracking_status (status),
    INDEX idx_delivery_tracking_date (updated_at)
);

-- Delivery returns table (for handling returns)
CREATE TABLE IF NOT EXISTS delivery_returns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_id INT NOT NULL,
    
    -- Return details
    return_reason ENUM('client_request', 'delivery_failed', 'damaged', 'incorrect', 'other') DEFAULT 'client_request',
    return_condition ENUM('good', 'damaged', 'incomplete', 'lost') DEFAULT 'good',
    return_date DATE NOT NULL,
    
    -- Processing
    processed_by INT,
    notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (delivery_id) REFERENCES delivery_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id),
    
    INDEX idx_delivery_returns_delivery (delivery_id),
    INDEX idx_delivery_returns_date (return_date)
);

-- Box movements table (for tracking box location changes)
CREATE TABLE IF NOT EXISTS box_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    box_id INT NOT NULL,
    
    -- Movement details
    old_location VARCHAR(255),
    new_location VARCHAR(255) NOT NULL,
    movement_type ENUM('storage', 'delivery', 'return', 'transfer') DEFAULT 'storage',
    
    -- Tracking
    moved_by INT,
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    
    FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx_box_movements_box (box_id),
    INDEX idx_box_movements_date (moved_at)
);

-- Search logs table (for analytics)
CREATE TABLE IF NOT EXISTS search_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    company_id INT NOT NULL,
    
    -- Search details
    search_query TEXT NOT NULL,
    search_filters JSON,
    result_count INT DEFAULT 0,
    search_time_ms DECIMAL(10,2),
    
    -- Context
    search_context VARCHAR(50), -- 'dashboard', 'documents', 'bundles', etc.
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_search_logs_company (company_id),
    INDEX idx_search_logs_user (user_id),
    INDEX idx_search_logs_date (created_at)
);
