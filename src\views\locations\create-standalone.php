<?php
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="container mx-auto px-6 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        Create Storage Location
                    </h1>
                    <p class="text-gray-600">Add a new storage location to organize your documents</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/warehouses') ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Warehouses
                    </a>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="bg-white rounded-2xl shadow-lg p-8">
            <form action="<?= url('/app/storage-locations/create') ?>" method="POST" class="space-y-6">
                <!-- Warehouse Selection -->
                <div>
                    <label for="warehouse_id" class="block text-sm font-semibold text-gray-800 mb-2">
                        Select Warehouse <span class="text-red-500">*</span>
                    </label>
                    <select id="warehouse_id" name="warehouse_id" required 
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                            onchange="updateCreateUrl()">
                        <option value="">Choose a warehouse...</option>
                        <?php foreach ($warehouses as $warehouse): ?>
                            <option value="<?= $warehouse['id'] ?>" <?= (isset($_GET['warehouse_id']) && $_GET['warehouse_id'] == $warehouse['id']) ? 'selected' : '' ?>>
                                <?= e($warehouse['name']) ?> - <?= e($warehouse['city']) ?>, <?= e($warehouse['state']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-sm text-gray-600 mt-1">Select the warehouse where this storage location will be created</p>
                </div>

                <!-- Location Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-semibold text-gray-800 mb-2">
                            Location Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="name" name="name" required
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="e.g., Shelf A-1, Room 101, Aisle 3">
                    </div>

                    <div>
                        <label for="location_type" class="block text-sm font-semibold text-gray-800 mb-2">
                            Location Type
                        </label>
                        <select id="location_type" name="location_type"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="shelf">Shelf</option>
                            <option value="room">Room</option>
                            <option value="aisle">Aisle</option>
                            <option value="cabinet">Cabinet</option>
                            <option value="vault">Vault</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="capacity" class="block text-sm font-semibold text-gray-800 mb-2">
                            Capacity (Number of Documents)
                        </label>
                        <input type="number" id="capacity" name="capacity" min="1" value="100"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="100">
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-semibold text-gray-800 mb-2">
                            Status
                        </label>
                        <select id="status" name="status"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="active">Active</option>
                            <option value="maintenance">Under Maintenance</option>
                            <option value="full">Full</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-semibold text-gray-800 mb-2">
                        Description
                    </label>
                    <textarea id="description" name="description" rows="4"
                              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none"
                              placeholder="Optional description of the storage location..."></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="<?= url('/app/warehouses') ?>" 
                       class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" id="submit-btn" disabled
                            class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed">
                        Create Storage Location
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateCreateUrl() {
    const warehouseSelect = document.getElementById('warehouse_id');
    const submitBtn = document.getElementById('submit-btn');
    const form = document.querySelector('form');
    
    if (warehouseSelect.value) {
        // Update form action to warehouse-specific route
        form.action = `<?= url('/app/warehouses') ?>/${warehouseSelect.value}/locations`;
        submitBtn.disabled = false;
    } else {
        submitBtn.disabled = true;
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateCreateUrl();
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
