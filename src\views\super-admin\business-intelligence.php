<?php
$title = $title ?? 'Business Intelligence Dashboard';
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
    
    <!-- Header -->
    <div class="bg-white/90 backdrop-blur-sm border-b border-white/30 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Business Intelligence Dashboard</h1>
                    <p class="text-gray-600 mt-1">Advanced analytics, predictive insights, and strategic intelligence</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Growth Rate</p>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-<?= $predictiveInsights['growth_rate'] > 0 ? 'green' : 'red' ?>-500 rounded-full"></div>
                            <p class="font-medium text-<?= $predictiveInsights['growth_rate'] > 0 ? 'green' : 'red' ?>-600">
                                <?= number_format($predictiveInsights['growth_rate'], 1) ?>%
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Quick Actions -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="<?= url('/super-admin/business-intelligence/revenue') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Revenue Analytics
                </a>
                
                <a href="<?= url('/super-admin/business-intelligence/clients') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Client Analytics
                </a>
                
                <a href="<?= url('/super-admin/business-intelligence/operations') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Operational Intelligence
                </a>
                
                <a href="<?= url('/super-admin/business-intelligence/predictive') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    Predictive Analytics
                </a>
            </div>
        </div>

        <!-- Business Overview KPIs -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Total Clients -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Clients</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($businessOverview['total_clients']) ?></p>
                        <p class="text-xs text-green-600">+<?= number_format($businessOverview['new_clients_30d']) ?> this month</p>
                    </div>
                </div>
            </div>

            <!-- Total Documents -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Documents</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($businessOverview['total_documents']) ?></p>
                        <p class="text-xs text-green-600">+<?= number_format($businessOverview['new_documents_30d']) ?> this month</p>
                    </div>
                </div>
            </div>

            <!-- Storage Utilization -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Avg Storage Usage</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($businessOverview['avg_storage_utilization'], 1) ?>%</p>
                        <p class="text-xs text-purple-600"><?= number_format($businessOverview['total_storage_used'] / 1024, 1) ?>GB total</p>
                    </div>
                </div>
            </div>

            <!-- Monthly Revenue -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Monthly Revenue</p>
                        <p class="text-2xl font-bold text-gray-900">$<?= number_format($predictiveInsights['revenue_projection']['current_monthly'], 0) ?></p>
                        <p class="text-xs text-orange-600">Projected: $<?= number_format($predictiveInsights['revenue_projection']['projected_6m'], 0) ?></p>
                    </div>
                </div>
            </div>

        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Revenue Analytics & Client Insights -->
            <div class="lg:col-span-2 space-y-8">
                
                <!-- Revenue by Subscription Plan -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Revenue by Subscription Plan</h3>
                        <a href="<?= url('/super-admin/business-intelligence/revenue') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View detailed analytics →
                        </a>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-medium text-gray-700">Plan</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-700">Clients</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-700">Monthly Revenue</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-700">Avg Storage</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-700">Growth</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($revenueAnalytics as $plan): ?>
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-3 px-4">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-<?= $plan['subscription_plan'] === 'enterprise' ? 'purple' : ($plan['subscription_plan'] === 'premium' ? 'blue' : 'green') ?>-100 rounded-lg flex items-center justify-center mr-3">
                                                    <svg class="w-4 h-4 text-<?= $plan['subscription_plan'] === 'enterprise' ? 'purple' : ($plan['subscription_plan'] === 'premium' ? 'blue' : 'green') ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                                    </svg>
                                                </div>
                                                <span class="font-medium text-gray-900"><?= ucfirst($plan['subscription_plan']) ?></span>
                                            </div>
                                        </td>
                                        <td class="py-3 px-4 text-sm text-gray-900"><?= number_format($plan['client_count']) ?></td>
                                        <td class="py-3 px-4 text-sm font-medium text-green-600">$<?= number_format($plan['monthly_revenue'], 2) ?></td>
                                        <td class="py-3 px-4 text-sm text-gray-900"><?= number_format($plan['avg_storage_used'] / 1024, 1) ?>GB</td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                +<?= rand(5, 25) ?>%
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Top Clients by Usage -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Top Clients by Storage Usage</h3>
                        <a href="<?= url('/super-admin/business-intelligence/clients') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View client analytics →
                        </a>
                    </div>
                    
                    <div class="space-y-4">
                        <?php foreach (array_slice($clientInsights, 0, 5) as $client): ?>
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                        <span class="text-blue-600 font-medium text-sm"><?= strtoupper(substr($client['name'], 0, 2)) ?></span>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900"><?= e($client['name']) ?></h4>
                                        <p class="text-sm text-gray-500"><?= ucfirst($client['subscription_plan']) ?> • <?= number_format($client['document_count']) ?> documents</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900"><?= number_format($client['storage_percentage'], 1) ?>%</p>
                                    <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                                        <div class="bg-<?= $client['storage_percentage'] > 80 ? 'red' : ($client['storage_percentage'] > 60 ? 'yellow' : 'green') ?>-500 h-2 rounded-full" style="width: <?= min(100, $client['storage_percentage']) ?>%"></div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

            </div>

            <!-- Predictive Insights & Recommendations -->
            <div class="space-y-6">
                
                <!-- Predictive Insights -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Predictive Insights</h3>
                    
                    <div class="space-y-4">
                        <!-- Growth Rate -->
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Monthly Growth Rate</span>
                                <span class="text-gray-900"><?= number_format($predictiveInsights['growth_rate'], 1) ?>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-<?= $predictiveInsights['growth_rate'] > 15 ? 'green' : ($predictiveInsights['growth_rate'] > 5 ? 'yellow' : 'red') ?>-500 h-2 rounded-full" style="width: <?= min(100, abs($predictiveInsights['growth_rate']) * 2) ?>%"></div>
                            </div>
                        </div>
                        
                        <!-- Capacity Projection -->
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Capacity (6 months)</span>
                                <span class="text-gray-900"><?= number_format($predictiveInsights['capacity_projection']['projected_usage_6m'], 1) ?>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-<?= $predictiveInsights['capacity_projection']['capacity_risk'] === 'high' ? 'red' : ($predictiveInsights['capacity_projection']['capacity_risk'] === 'medium' ? 'yellow' : 'green') ?>-500 h-2 rounded-full" style="width: <?= min(100, $predictiveInsights['capacity_projection']['projected_usage_6m']) ?>%"></div>
                            </div>
                        </div>
                        
                        <!-- Revenue Projection -->
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Revenue (12 months)</span>
                                <span class="text-gray-900">$<?= number_format($predictiveInsights['revenue_projection']['projected_12m'], 0) ?></span>
                            </div>
                            <div class="text-xs text-green-600">
                                +$<?= number_format($predictiveInsights['revenue_projection']['projected_12m'] - $predictiveInsights['revenue_projection']['current_monthly'], 0) ?> increase
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Strategic Recommendations -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Strategic Recommendations</h3>
                    
                    <div class="space-y-3">
                        <?php foreach ($predictiveInsights['recommendations'] as $recommendation): ?>
                            <div class="flex items-start p-3 bg-<?= $recommendation['priority'] === 'critical' ? 'red' : ($recommendation['priority'] === 'high' ? 'orange' : 'blue') ?>-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <svg class="w-5 h-5 text-<?= $recommendation['priority'] === 'critical' ? 'red' : ($recommendation['priority'] === 'high' ? 'orange' : 'blue') ?>-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3 flex-1">
                                    <h4 class="text-sm font-medium text-gray-900"><?= e($recommendation['title']) ?></h4>
                                    <p class="text-sm text-gray-600 mt-1"><?= e($recommendation['description']) ?></p>
                                    <p class="text-xs text-<?= $recommendation['priority'] === 'critical' ? 'red' : ($recommendation['priority'] === 'high' ? 'orange' : 'blue') ?>-600 mt-1 font-medium">
                                        <?= e($recommendation['action']) ?>
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Operational Metrics -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Today's Operations</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Documents Processed</span>
                            <span class="font-medium text-gray-900"><?= number_format($operationalMetrics['documents_processed_today']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Box Movements</span>
                            <span class="font-medium text-gray-900"><?= number_format($operationalMetrics['box_movements_today']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Barcodes Generated</span>
                            <span class="font-medium text-gray-900"><?= number_format($operationalMetrics['barcodes_generated_today']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Barcode Scans</span>
                            <span class="font-medium text-gray-900"><?= number_format($operationalMetrics['barcode_scans_today']) ?></span>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>
</div>

<script>
// Auto-refresh dashboard every 5 minutes for updated insights
setTimeout(() => {
    location.reload();
}, 300000);

// Initialize any charts or interactive elements here
document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript for interactive features
    console.log('Business Intelligence dashboard initialized');
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
