-- Update bundles table to match documentation requirements
-- Implements Bundle Creation from documentation (Step 3)

-- Add new columns to bundles table for documentation compliance
ALTER TABLE bundles 
ADD COLUMN box_id INT NULL AFTER location_id,
ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation',
ADD COLUMN year INT NULL COMMENT 'Year as per documentation', 
ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation',
ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation',
ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation',
ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation',
ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation';

-- Add foreign key for box relationship
ALTER TABLE bundles 
ADD FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL,
ADD INDEX idx_bundle_box (box_id);

-- Update existing bundles to have proper reference numbers
-- This will be done in a separate script to avoid conflicts

-- Create bundle activity log for tracking bundle changes
CREATE TABLE IF NOT EXISTS bundle_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bundle_id INT NOT NULL,
    action VARCHAR(50) NOT NULL, -- 'created', 'moved', 'updated', 'sealed', 'digitized'
    description TEXT,
    old_box_id INT NULL,
    new_box_id INT NULL,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (bundle_id) REFERENCES bundles(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (old_box_id) REFERENCES boxes(id) ON DELETE SET NULL,
    FOREIGN KEY (new_box_id) REFERENCES boxes(id) ON DELETE SET NULL,
    
    INDEX idx_bundle_activity (bundle_id, created_at),
    INDEX idx_user_activity (user_id, created_at)
);

-- Insert sample bundle data that follows documentation format
INSERT INTO bundle_activity_log (bundle_id, action, description, user_id) 
SELECT id, 'created', CONCAT('Bundle created: ', name), created_by 
FROM bundles 
WHERE id NOT IN (SELECT DISTINCT bundle_id FROM bundle_activity_log WHERE action = 'created');

-- Update bundle reference numbers to follow CLIENT01-BOX001-BUNDLE03 format
-- This will be handled by the application logic for new bundles

-- Create view for bundle overview with box information
CREATE OR REPLACE VIEW bundle_overview AS
SELECT 
    b.id,
    b.name,
    b.reference_number,
    b.document_type,
    b.year,
    b.department,
    b.confidentiality_flag,
    b.pages_volume,
    b.scan_digitization_status,
    b.contents_summary,
    b.status,
    b.created_at,
    b.updated_at,
    box.box_id,
    box.storage_location_code,
    w.name as warehouse_name,
    c.name as company_name,
    u.first_name,
    u.last_name,
    COUNT(d.id) as document_count,
    SUM(d.file_size) as total_size
FROM bundles b
LEFT JOIN boxes box ON b.box_id = box.id
LEFT JOIN warehouses w ON box.warehouse_id = w.id
LEFT JOIN companies c ON b.company_id = c.id
LEFT JOIN users u ON b.created_by = u.id
LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
WHERE b.status = 'active'
GROUP BY b.id, box.id, w.id, c.id, u.id;

-- Create indexes for better performance
CREATE INDEX idx_bundles_document_type ON bundles(document_type);
CREATE INDEX idx_bundles_year ON bundles(year);
CREATE INDEX idx_bundles_department ON bundles(department);
CREATE INDEX idx_bundles_confidentiality ON bundles(confidentiality_flag);
CREATE INDEX idx_bundles_scan_status ON bundles(scan_digitization_status);

-- Update sample data to include new fields
UPDATE bundles 
SET 
    document_type = CASE 
        WHEN name LIKE '%invoice%' THEN 'invoice'
        WHEN name LIKE '%contract%' THEN 'contract'
        WHEN name LIKE '%report%' THEN 'report'
        ELSE 'general'
    END,
    year = YEAR(created_at),
    department = CASE 
        WHEN name LIKE '%HR%' OR name LIKE '%human%' THEN 'HR'
        WHEN name LIKE '%finance%' OR name LIKE '%accounting%' THEN 'Finance'
        WHEN name LIKE '%legal%' THEN 'Legal'
        ELSE 'General'
    END,
    confidentiality_flag = CASE 
        WHEN name LIKE '%confidential%' OR name LIKE '%private%' THEN TRUE
        ELSE FALSE
    END,
    scan_digitization_status = 'not_scanned',
    contents_summary = CONCAT('Bundle containing ', name, ' documents')
WHERE document_type IS NULL;
