<?php
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="container mx-auto px-6 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        Create Storage Location
                    </h1>
                    <p class="text-gray-600">Add a new storage location to <strong><?= e($warehouse['name']) ?></strong></p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/warehouses/' . $warehouse['id'] . '/locations') ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Locations
                    </a>
                </div>
            </div>
        </div>

        <!-- Warehouse Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-900"><?= e($warehouse['name']) ?></h3>
                    <p class="text-blue-700 text-sm"><?= e($warehouse['city']) ?>, <?= e($warehouse['state']) ?></p>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="bg-white rounded-2xl shadow-lg p-8">
            <form action="<?= url('/app/warehouses/' . $warehouse['id'] . '/locations') ?>" method="POST" class="space-y-6">
                <!-- Location Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-semibold text-gray-800 mb-2">
                            Location Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="name" name="name" required
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="e.g., Shelf A-1, Room 101, Aisle 3">
                        <p class="text-sm text-gray-600 mt-1">Unique name for this storage location</p>
                    </div>

                    <div>
                        <label for="location_type" class="block text-sm font-semibold text-gray-800 mb-2">
                            Location Type
                        </label>
                        <select id="location_type" name="location_type"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="shelf">📚 Shelf</option>
                            <option value="room">🏠 Room</option>
                            <option value="aisle">🛤️ Aisle</option>
                            <option value="cabinet">🗄️ Cabinet</option>
                            <option value="vault">🔒 Vault</option>
                            <option value="other">📦 Other</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="capacity" class="block text-sm font-semibold text-gray-800 mb-2">
                            Capacity (Number of Documents)
                        </label>
                        <input type="number" id="capacity" name="capacity" min="1" value="100"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="100">
                        <p class="text-sm text-gray-600 mt-1">Maximum number of documents this location can hold</p>
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-semibold text-gray-800 mb-2">
                            Status
                        </label>
                        <select id="status" name="status"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="active">✅ Active</option>
                            <option value="maintenance">🔧 Under Maintenance</option>
                            <option value="full">📦 Full</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-semibold text-gray-800 mb-2">
                        Description
                    </label>
                    <textarea id="description" name="description" rows="4"
                              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none"
                              placeholder="Optional description of the storage location, access instructions, or special notes..."></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="<?= url('/app/warehouses/' . $warehouse['id'] . '/locations') ?>" 
                       class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                        Cancel
                    </a>
                    <button type="submit"
                            class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Create Storage Location
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('Please enter a location name.');
        document.getElementById('name').focus();
        return false;
    }
    
    return true;
});

// Auto-focus on name field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('name').focus();
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
