<?php
/**
 * Test Billing Route
 * 
 * This page tests if the billing route is working
 */

session_start();

// Security check - only allow from localhost
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';

if (!in_array($clientIP, $allowedIPs)) {
    die('Access denied. This test page can only be accessed from localhost for security reasons.');
}

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

echo "<h1>Billing Route Test</h1>";

try {
    // Test 1: Check if BillingController exists
    echo "<h3>Test 1: BillingController Class</h3>";
    if (class_exists('App\Controllers\BillingController')) {
        echo "✅ BillingController class exists<br>";
        
        // Try to instantiate it
        $controller = new \App\Controllers\BillingController();
        echo "✅ BillingController can be instantiated<br>";
        
        // Check if index method exists
        if (method_exists($controller, 'index')) {
            echo "✅ BillingController::index method exists<br>";
        } else {
            echo "❌ BillingController::index method missing<br>";
        }
    } else {
        echo "❌ BillingController class not found<br>";
    }
    
    // Test 2: Check Router
    echo "<h3>Test 2: Router Setup</h3>";
    $router = new \App\Core\Router();
    echo "✅ Router created<br>";
    
    // Load routes
    require_once APP_ROOT . '/src/routes.php';
    echo "✅ Routes loaded<br>";
    
    // Test 3: Check if billing route is registered
    echo "<h3>Test 3: Route Registration</h3>";
    
    // Simulate the billing request
    $testUri = '/app/billing';
    $testMethod = 'GET';
    
    echo "Testing route: {$testMethod} {$testUri}<br>";
    
    // Test 4: Database connection
    echo "<h3>Test 4: Database Connection</h3>";
    require_once CONFIG_PATH . '/database.php';
    $db = \App\Core\Database::getInstance();
    echo "✅ Database connected<br>";
    
    // Test 5: Session check
    echo "<h3>Test 5: Session Status</h3>";
    if (isset($_SESSION['user_id'])) {
        echo "✅ User logged in (ID: " . $_SESSION['user_id'] . ")<br>";
        echo "✅ User role: " . ($_SESSION['user_role'] ?? 'Unknown') . "<br>";
    } else {
        echo "⚠️ User not logged in<br>";
    }
    
    // Test 6: Try to manually call the billing controller
    echo "<h3>Test 6: Manual Controller Test</h3>";
    
    if (isset($_SESSION['user_id'])) {
        try {
            // Set up service factory
            \App\Services\ServiceFactory::initialize($db, $_SESSION, $_SESSION);
            
            $billingController = new \App\Controllers\BillingController();
            echo "✅ BillingController instantiated with session<br>";
            
            // Try to call index method (but capture output)
            ob_start();
            try {
                $billingController->index();
                $output = ob_get_contents();
                echo "✅ BillingController::index() executed successfully<br>";
                echo "Output length: " . strlen($output) . " characters<br>";
            } catch (Exception $e) {
                echo "❌ Error calling index(): " . $e->getMessage() . "<br>";
            }
            ob_end_clean();
            
        } catch (Exception $e) {
            echo "❌ Error setting up controller: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "⚠️ Skipping controller test - user not logged in<br>";
    }
    
    // Test 7: Check if billing views exist
    echo "<h3>Test 7: View Files</h3>";
    $viewPath = APP_ROOT . '/src/views/billing/index.php';
    if (file_exists($viewPath)) {
        echo "✅ Billing index view exists<br>";
    } else {
        echo "❌ Billing index view missing: {$viewPath}<br>";
    }
    
    $headerPath = APP_ROOT . '/src/views/layouts/header.php';
    if (file_exists($headerPath)) {
        echo "✅ Header layout exists<br>";
    } else {
        echo "❌ Header layout missing: {$headerPath}<br>";
    }
    
    $footerPath = APP_ROOT . '/src/views/layouts/footer.php';
    if (file_exists($footerPath)) {
        echo "✅ Footer layout exists<br>";
    } else {
        echo "❌ Footer layout missing: {$footerPath}<br>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Fatal Error</h3>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h3>Direct Links to Test</h3>";
echo '<a href="/dms/public/app/billing" target="_blank">Test: /dms/public/app/billing</a><br>';
echo '<a href="/dms/public/index.php/app/billing" target="_blank">Test: /dms/public/index.php/app/billing</a><br>';
echo '<a href="/dms/app/billing" target="_blank">Test: /dms/app/billing</a><br>';

echo "<hr>";
echo "<p><strong>If all tests pass but the route still doesn't work, the issue is likely in the Router class or route matching logic.</strong></p>";
?>
