-- Seed test data for business services

-- Insert test warehouse if it doesn't exist
INSERT IGNORE INTO warehouses (id, name, code, address, city, state, zip_code, country, status, created_at, updated_at)
VALUES (1, 'Main Warehouse', 'WH01', '123 Storage Street', 'Storage City', 'Storage State', '12345', 'USA', 'active', NOW(), NOW());

-- Insert additional test warehouses
INSERT IGNORE INTO warehouses (id, name, code, address, city, state, zip_code, country, status, created_at, updated_at)
VALUES 
(2, 'Secondary Warehouse', 'WH02', '456 Archive Avenue', 'Archive City', 'Archive State', '67890', 'USA', 'active', NOW(), NOW()),
(3, 'Digital Storage Center', 'WH03', '789 Digital Drive', 'Digital City', 'Digital State', '11111', 'USA', 'active', NOW(), NOW());

-- Create activity_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    company_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_activity_user (user_id),
    INDEX idx_activity_company (company_id),
    INDEX idx_activity_entity (entity_type, entity_id),
    INDEX idx_activity_date (created_at)
);

-- Create box_bundles junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS box_bundles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    box_id INT NOT NULL,
    bundle_id INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    added_by INT,
    
    UNIQUE KEY unique_box_bundle (box_id, bundle_id),
    FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
    FOREIGN KEY (bundle_id) REFERENCES bundles(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id)
);

-- Update boxes table to add missing columns if they don't exist
ALTER TABLE boxes 
ADD COLUMN IF NOT EXISTS client_prefix VARCHAR(20),
ADD COLUMN IF NOT EXISTS box_number INT,
ADD COLUMN IF NOT EXISTS storage_location_code VARCHAR(100),
ADD COLUMN IF NOT EXISTS row_number VARCHAR(10) DEFAULT 'R1',
ADD COLUMN IF NOT EXISTS shelf_number VARCHAR(10) DEFAULT 'S1',
ADD COLUMN IF NOT EXISTS position_number VARCHAR(10),
ADD COLUMN IF NOT EXISTS capacity INT DEFAULT 100,
ADD COLUMN IF NOT EXISTS current_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS barcode_value VARCHAR(255),
ADD COLUMN IF NOT EXISTS barcode_generated_at TIMESTAMP NULL;

-- Update bundles table to add missing columns if they don't exist
ALTER TABLE bundles 
ADD COLUMN IF NOT EXISTS reference_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS bundle_type VARCHAR(50) DEFAULT 'custom',
ADD COLUMN IF NOT EXISTS category VARCHAR(100) DEFAULT 'general',
ADD COLUMN IF NOT EXISTS priority VARCHAR(20) DEFAULT 'medium',
ADD COLUMN IF NOT EXISTS retention_period INT DEFAULT 7,
ADD COLUMN IF NOT EXISTS access_level VARCHAR(20) DEFAULT 'private',
ADD COLUMN IF NOT EXISTS document_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_size BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS closed_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS closed_by INT;

-- Update document_intake table to add missing columns if they don't exist
ALTER TABLE document_intake 
ADD COLUMN IF NOT EXISTS reference_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS priority VARCHAR(20) DEFAULT 'medium',
ADD COLUMN IF NOT EXISTS expected_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS actual_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS sensitivity_level VARCHAR(20) DEFAULT 'internal',
ADD COLUMN IF NOT EXISTS department VARCHAR(255),
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS bundle_id INT,
ADD COLUMN IF NOT EXISTS approved_by INT,
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS approval_notes TEXT,
ADD COLUMN IF NOT EXISTS rejected_by INT,
ADD COLUMN IF NOT EXISTS rejected_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS processing_started_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS processing_notes TEXT,
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS completion_notes TEXT,
ADD COLUMN IF NOT EXISTS archived_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS archive_reason TEXT;

-- Update documents table to add missing columns if they don't exist
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS storage_type VARCHAR(20) DEFAULT 'physical',
ADD COLUMN IF NOT EXISTS tags JSON,
ADD COLUMN IF NOT EXISTS ocr_text TEXT;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_boxes_client_prefix ON boxes(client_prefix);
CREATE INDEX IF NOT EXISTS idx_boxes_storage_location ON boxes(storage_location_code);
CREATE INDEX IF NOT EXISTS idx_bundles_reference ON bundles(reference_number);
CREATE INDEX IF NOT EXISTS idx_bundles_type ON bundles(bundle_type);
CREATE INDEX IF NOT EXISTS idx_intake_reference ON document_intake(reference_number);
CREATE INDEX IF NOT EXISTS idx_intake_status ON document_intake(status);
CREATE INDEX IF NOT EXISTS idx_documents_storage_type ON documents(storage_type);
