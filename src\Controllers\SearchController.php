<?php

namespace App\Controllers;

/**
 * Search Controller - Phase 2 Implementation
 * 
 * Handles advanced search and filtering functionality
 */
class SearchController extends BaseController
{
    /**
     * Display search interface
     */
    public function index()
    {
        $this->requireAuth();
        
        // Get search parameters
        $query = $_GET['q'] ?? '';
        $searchTypes = $_GET['types'] ?? 'documents,bundles,boxes';
        $searchTypes = explode(',', $searchTypes);

        $filters = [
            'category' => $_GET['category'] ?? '',
            'type' => $_GET['type'] ?? '',
            'file_type' => $_GET['file_type'] ?? '',
            'date_from' => $_GET['from'] ?? $_GET['date_from'] ?? '',
            'date_to' => $_GET['to'] ?? $_GET['date_to'] ?? '',
            'location' => $_GET['location'] ?? '',
            'size_min' => $_GET['size_min'] ?? '',
            'size_max' => $_GET['size_max'] ?? '',
            'sort' => $_GET['sort'] ?? 'relevance',
            'order' => $_GET['order'] ?? 'DESC',
            'search_types' => $searchTypes
        ];

        $results = [];
        $totalCount = 0;
        $searchTime = 0;

        // Perform search if query is provided
        if (!empty($query)) {
            $startTime = microtime(true);
            $results = $this->performMultiTypeSearch($query, $filters);
            $totalCount = $this->getMultiTypeSearchCount($query, $filters);
            $searchTime = round((microtime(true) - $startTime) * 1000, 2);

            // Log search activity
            $this->logActivity('search', 'multi', null, "Searched for: {$query} in types: " . implode(',', $searchTypes));
        }

        // Get filter options
        $categories = $this->getCategories();
        $locations = $this->getStorageLocations();
        $documentTypes = $this->getDocumentTypes();

        $this->view('search/index', [
            'title' => 'Advanced Search',
            'query' => $query,
            'filters' => $filters,
            'results' => $results,
            'totalCount' => $totalCount,
            'searchTime' => $searchTime,
            'categories' => $categories,
            'locations' => $locations,
            'documentTypes' => $documentTypes
        ]);
    }

    /**
     * Perform search via AJAX
     */
    public function search()
    {
        $this->requireAuth();
        
        try {
            $query = $_POST['q'] ?? '';
            $filters = $_POST['filters'] ?? [];

            if (empty($query)) {
                throw new \Exception('Search query is required');
            }

            $startTime = microtime(true);
            $results = $this->performSearch($query, $filters);
            $totalCount = $this->getSearchCount($query, $filters);
            $searchTime = round((microtime(true) - $startTime) * 1000, 2);

            // Log search activity
            $this->logActivity('search', 'document', null, "Searched for: {$query}");

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'results' => $results,
                'totalCount' => $totalCount,
                'searchTime' => $searchTime,
                'query' => $query
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }

    /**
     * Get search suggestions
     */
    public function suggestions()
    {
        $this->requireAuth();
        
        try {
            $query = $_GET['q'] ?? '';
            
            if (strlen($query) < 2) {
                header('Content-Type: application/json');
                echo json_encode(['suggestions' => []]);
                exit;
            }

            // Get document title suggestions
            $titleSuggestions = $this->db->fetchAll(
                "SELECT DISTINCT title as suggestion, 'document' as type
                 FROM documents
                 WHERE company_id = ? AND status != 'deleted'
                 AND title LIKE ?
                 ORDER BY title
                 LIMIT 3",
                [$this->user['company_id'], '%' . $query . '%']
            );

            // Get bundle suggestions
            $bundleSuggestions = $this->db->fetchAll(
                "SELECT DISTINCT name as suggestion, 'bundle' as type
                 FROM bundles
                 WHERE company_id = ?
                 AND (name LIKE ? OR reference_number LIKE ?)
                 ORDER BY name
                 LIMIT 3",
                [$this->user['company_id'], '%' . $query . '%', '%' . $query . '%']
            );

            // Get box suggestions
            $boxSuggestions = $this->db->fetchAll(
                "SELECT DISTINCT name as suggestion, 'box' as type
                 FROM boxes
                 WHERE company_id = ?
                 AND (name LIKE ? OR box_id LIKE ?)
                 ORDER BY name
                 LIMIT 3",
                [$this->user['company_id'], '%' . $query . '%', '%' . $query . '%']
            );

            // Get warehouse suggestions
            $warehouseSuggestions = $this->db->fetchAll(
                "SELECT DISTINCT name as suggestion, 'warehouse' as type
                 FROM warehouses
                 WHERE company_id = ?
                 AND name LIKE ?
                 ORDER BY name
                 LIMIT 2",
                [$this->user['company_id'], '%' . $query . '%']
            );

            // Get category suggestions
            try {
                $categorySuggestions = $this->db->fetchAll(
                    "SELECT DISTINCT name as suggestion, 'category' as type
                     FROM categories
                     WHERE company_id = ? AND status = 'active'
                     AND name LIKE ?
                     ORDER BY name
                     LIMIT 2",
                    [$this->user['company_id'], '%' . $query . '%']
                );
            } catch (\Exception $e) {
                // Fallback to is_active column
                $categorySuggestions = $this->db->fetchAll(
                    "SELECT DISTINCT name as suggestion, 'category' as type
                     FROM categories
                     WHERE company_id = ? AND is_active = 1
                     AND name LIKE ?
                     ORDER BY name
                     LIMIT 2",
                    [$this->user['company_id'], '%' . $query . '%']
                );
            }

            $suggestions = array_merge($titleSuggestions, $bundleSuggestions, $boxSuggestions, $warehouseSuggestions, $categorySuggestions);

            header('Content-Type: application/json');
            echo json_encode(['suggestions' => $suggestions]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode(['suggestions' => []]);
        }
        exit;
    }

    /**
     * Perform the actual search
     */
    private function performSearch($query, $filters = [])
    {
        $where = ["d.company_id = ?", "d.status != 'deleted'"];
        $params = [$this->user['company_id']];
        $joins = [];

        // Full-text search on title, description, tags, and file_name
        $searchTerms = explode(' ', trim($query));
        $searchConditions = [];
        
        foreach ($searchTerms as $term) {
            if (!empty($term)) {
                $searchConditions[] = "(d.title LIKE ? OR d.description LIKE ? OR d.file_name LIKE ?)";
                $searchTerm = '%' . $term . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
        }

        if (!empty($searchConditions)) {
            $where[] = '(' . implode(' AND ', $searchConditions) . ')';
        }

        // Apply filters
        if (!empty($filters['category'])) {
            $where[] = "d.category_id = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['type'])) {
            $where[] = "d.document_type = ?";
            $params[] = $filters['type'];
        }

        if (!empty($filters['file_type'])) {
            switch ($filters['file_type']) {
                case 'pdf':
                    $where[] = "d.mime_type LIKE 'application/pdf%'";
                    break;
                case 'image':
                    $where[] = "d.mime_type LIKE 'image/%'";
                    break;
                case 'document':
                    $where[] = "(d.mime_type LIKE 'application/msword%' OR d.mime_type LIKE 'application/vnd.openxmlformats-officedocument.wordprocessingml%')";
                    break;
                case 'spreadsheet':
                    $where[] = "(d.mime_type LIKE 'application/vnd.ms-excel%' OR d.mime_type LIKE 'application/vnd.openxmlformats-officedocument.spreadsheetml%')";
                    break;
            }
        }

        if (!empty($filters['date_from'])) {
            $where[] = "d.created_at >= ?";
            $params[] = $filters['date_from'] . ' 00:00:00';
        }

        if (!empty($filters['date_to'])) {
            $where[] = "d.created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }

        if (!empty($filters['location'])) {
            $joins[] = "LEFT JOIN document_locations dl ON d.id = dl.document_id AND dl.is_current = 1";
            $where[] = "dl.location_id = ?";
            $params[] = $filters['location'];
        }

        if (!empty($filters['size_min'])) {
            $where[] = "d.file_size >= ?";
            $params[] = (int)$filters['size_min'];
        }

        if (!empty($filters['size_max'])) {
            $where[] = "d.file_size <= ?";
            $params[] = (int)$filters['size_max'];
        }

        // Build ORDER BY clause
        $orderBy = "d.created_at DESC"; // Default
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'title':
                    $orderBy = "d.title " . ($filters['order'] ?? 'ASC');
                    break;
                case 'date':
                    $orderBy = "d.created_at " . ($filters['order'] ?? 'DESC');
                    break;
                case 'size':
                    $orderBy = "d.file_size " . ($filters['order'] ?? 'DESC');
                    break;
                case 'downloads':
                    $orderBy = "d.download_count " . ($filters['order'] ?? 'DESC');
                    break;
                case 'relevance':
                default:
                    // Calculate relevance score
                    $relevanceScore = "(" .
                        "CASE WHEN d.title LIKE '%{$query}%' THEN 10 ELSE 0 END + " .
                        "CASE WHEN d.description LIKE '%{$query}%' THEN 5 ELSE 0 END + " .
                        "CASE WHEN d.file_name LIKE '%{$query}%' THEN 2 ELSE 0 END" .
                        ") as relevance_score";
                    $orderBy = "relevance_score DESC, d.created_at DESC";
                    break;
            }
        }

        // Build and execute query
        $joinClause = implode(' ', $joins);
        $whereClause = implode(' AND ', $where);
        
        $sql = "SELECT d.*, u.first_name, u.last_name, c.name as category_name";
        
        if ($filters['sort'] === 'relevance') {
            $sql .= ", " . $relevanceScore;
        }
        
        $sql .= " FROM documents d 
                 LEFT JOIN users u ON d.created_by = u.id 
                 LEFT JOIN categories c ON d.category_id = c.id 
                 {$joinClause}
                 WHERE {$whereClause} 
                 ORDER BY {$orderBy} 
                 LIMIT 50";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get search result count
     */
    private function getSearchCount($query, $filters = [])
    {
        $where = ["d.company_id = ?", "d.status != 'deleted'"];
        $params = [$this->user['company_id']];
        $joins = [];

        // Apply same search logic as performSearch
        $searchTerms = explode(' ', trim($query));
        $searchConditions = [];
        
        foreach ($searchTerms as $term) {
            if (!empty($term)) {
                $searchConditions[] = "(d.title LIKE ? OR d.description LIKE ? OR d.file_name LIKE ?)";
                $searchTerm = '%' . $term . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
        }

        if (!empty($searchConditions)) {
            $where[] = '(' . implode(' AND ', $searchConditions) . ')';
        }

        // Apply same filters as performSearch
        if (!empty($filters['category'])) {
            $where[] = "d.category_id = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['type'])) {
            $where[] = "d.document_type = ?";
            $params[] = $filters['type'];
        }

        if (!empty($filters['date_from'])) {
            $where[] = "d.created_at >= ?";
            $params[] = $filters['date_from'] . ' 00:00:00';
        }

        if (!empty($filters['date_to'])) {
            $where[] = "d.created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }

        if (!empty($filters['location'])) {
            $joins[] = "LEFT JOIN document_locations dl ON d.id = dl.document_id AND dl.is_current = 1";
            $where[] = "dl.location_id = ?";
            $params[] = $filters['location'];
        }

        if (!empty($filters['size_min'])) {
            $where[] = "d.file_size >= ?";
            $params[] = (int)$filters['size_min'];
        }

        if (!empty($filters['size_max'])) {
            $where[] = "d.file_size <= ?";
            $params[] = (int)$filters['size_max'];
        }

        $joinClause = implode(' ', $joins);
        $whereClause = implode(' AND ', $where);

        $sql = "SELECT COUNT(*) as count FROM documents d {$joinClause} WHERE {$whereClause}";
        $result = $this->db->fetch($sql, $params);
        
        return $result['count'] ?? 0;
    }

    /**
     * Get categories for filter dropdown
     */
    private function getCategories()
    {
        try {
            return $this->db->fetchAll(
                "SELECT id, name FROM categories
                 WHERE company_id = ? AND status = 'active'
                 ORDER BY name",
                [$this->user['company_id']]
            );
        } catch (\Exception $e) {
            // Fallback to is_active column
            return $this->db->fetchAll(
                "SELECT id, name FROM categories
                 WHERE company_id = ? AND is_active = 1
                 ORDER BY name",
                [$this->user['company_id']]
            );
        }
    }

    /**
     * Get storage locations for filter dropdown
     */
    private function getStorageLocations()
    {
        return $this->db->fetchAll(
            "SELECT sl.id, sl.name, sl.identifier, w.name as warehouse_name
             FROM storage_locations sl 
             JOIN warehouses w ON sl.warehouse_id = w.id 
             WHERE w.company_id = ? AND sl.status = 'active' AND sl.type = 'box'
             ORDER BY w.name, sl.name",
            [$this->user['company_id']]
        );
    }

    /**
     * Get document types for filter dropdown
     */
    private function getDocumentTypes()
    {
        return [
            'contract' => 'Contract',
            'invoice' => 'Invoice',
            'report' => 'Report',
            'image' => 'Image',
            'video' => 'Video',
            'audio' => 'Audio',
            'spreadsheet' => 'Spreadsheet',
            'presentation' => 'Presentation',
            'other' => 'Other'
        ];
    }

    /**
     * Perform multi-type search across documents, bundles, and boxes
     */
    private function performMultiTypeSearch($query, $filters = [])
    {
        $results = [];
        $searchTypes = $filters['search_types'] ?? ['documents'];

        // Search Documents
        if (in_array('documents', $searchTypes)) {
            $documentResults = $this->searchDocuments($query, $filters);
            foreach ($documentResults as $doc) {
                $doc['result_type'] = 'document';
                $doc['result_icon'] = '📄';
                $doc['result_url'] = url('/app/documents/' . $doc['id']);
                $results[] = $doc;
            }
        }

        // Search Bundles
        if (in_array('bundles', $searchTypes)) {
            $bundleResults = $this->searchBundles($query, $filters);
            foreach ($bundleResults as $bundle) {
                $bundle['result_type'] = 'bundle';
                $bundle['result_icon'] = '📁';
                $bundle['result_url'] = url('/app/bundles/' . $bundle['id']);
                $results[] = $bundle;
            }
        }

        // Search Boxes
        if (in_array('boxes', $searchTypes)) {
            $boxResults = $this->searchBoxes($query, $filters);
            foreach ($boxResults as $box) {
                $box['result_type'] = 'box';
                $box['result_icon'] = '📦';
                $box['result_url'] = url('/app/boxes/' . $box['id']);
                $results[] = $box;
            }
        }

        // Search Warehouses
        if (in_array('warehouses', $searchTypes)) {
            $warehouseResults = $this->searchWarehouses($query, $filters);
            foreach ($warehouseResults as $warehouse) {
                $warehouse['result_type'] = 'warehouse';
                $warehouse['result_icon'] = '🏢';
                $warehouse['result_url'] = url('/app/warehouses/' . $warehouse['id']);
                $results[] = $warehouse;
            }
        }

        // Sort results by relevance
        usort($results, function($a, $b) {
            $scoreA = $a['relevance_score'] ?? 0;
            $scoreB = $b['relevance_score'] ?? 0;
            return $scoreB <=> $scoreA;
        });

        return array_slice($results, 0, 50); // Limit to 50 results
    }

    /**
     * Get total count for multi-type search
     */
    private function getMultiTypeSearchCount($query, $filters = [])
    {
        $count = 0;
        $searchTypes = $filters['search_types'] ?? ['documents'];

        if (in_array('documents', $searchTypes)) {
            $count += $this->getSearchCount($query, $filters);
        }

        if (in_array('bundles', $searchTypes)) {
            $count += $this->getBundleSearchCount($query, $filters);
        }

        if (in_array('boxes', $searchTypes)) {
            $count += $this->getBoxSearchCount($query, $filters);
        }

        if (in_array('warehouses', $searchTypes)) {
            $count += $this->getWarehouseSearchCount($query, $filters);
        }

        return $count;
    }

    /**
     * Search in documents
     */
    private function searchDocuments($query, $filters = [])
    {
        return $this->performSearch($query, $filters);
    }

    /**
     * Search in bundles
     */
    private function searchBundles($query, $filters = [])
    {
        $where = ["b.company_id = ?"];
        $params = [$this->user['company_id']];

        // Search in bundle name, reference number, and description
        $searchTerms = explode(' ', trim($query));
        $searchConditions = [];

        foreach ($searchTerms as $term) {
            if (!empty($term)) {
                $searchConditions[] = "(b.name LIKE ? OR b.reference_number LIKE ? OR b.description LIKE ?)";
                $searchTerm = '%' . $term . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
        }

        if (!empty($searchConditions)) {
            $where[] = '(' . implode(' AND ', $searchConditions) . ')';
        }

        // Apply date filters
        if (!empty($filters['date_from'])) {
            $where[] = "b.created_at >= ?";
            $params[] = $filters['date_from'] . ' 00:00:00';
        }

        if (!empty($filters['date_to'])) {
            $where[] = "b.created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }

        $whereClause = implode(' AND ', $where);

        // Calculate relevance score
        $relevanceScore = "(" .
            "CASE WHEN b.name LIKE '%{$query}%' THEN 10 ELSE 0 END + " .
            "CASE WHEN b.reference_number LIKE '%{$query}%' THEN 8 ELSE 0 END + " .
            "CASE WHEN b.description LIKE '%{$query}%' THEN 5 ELSE 0 END" .
            ") as relevance_score";

        $sql = "SELECT b.*, u.first_name, u.last_name, {$relevanceScore}
                FROM bundles b
                LEFT JOIN users u ON b.created_by = u.id
                WHERE {$whereClause}
                ORDER BY relevance_score DESC, b.created_at DESC
                LIMIT 20";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Search in boxes
     */
    private function searchBoxes($query, $filters = [])
    {
        $where = ["b.company_id = ?"];
        $params = [$this->user['company_id']];

        // Search in box name, box_id, and description
        $searchTerms = explode(' ', trim($query));
        $searchConditions = [];

        foreach ($searchTerms as $term) {
            if (!empty($term)) {
                $searchConditions[] = "(b.name LIKE ? OR b.box_id LIKE ? OR b.description LIKE ?)";
                $searchTerm = '%' . $term . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
        }

        if (!empty($searchConditions)) {
            $where[] = '(' . implode(' AND ', $searchConditions) . ')';
        }

        // Apply date filters
        if (!empty($filters['date_from'])) {
            $where[] = "b.created_at >= ?";
            $params[] = $filters['date_from'] . ' 00:00:00';
        }

        if (!empty($filters['date_to'])) {
            $where[] = "b.created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }

        $whereClause = implode(' AND ', $where);

        // Calculate relevance score
        $relevanceScore = "(" .
            "CASE WHEN b.name LIKE '%{$query}%' THEN 10 ELSE 0 END + " .
            "CASE WHEN b.box_id LIKE '%{$query}%' THEN 8 ELSE 0 END + " .
            "CASE WHEN b.description LIKE '%{$query}%' THEN 5 ELSE 0 END" .
            ") as relevance_score";

        $sql = "SELECT b.*, w.name as warehouse_name, w.address as warehouse_address,
                w.city as warehouse_city, w.state as warehouse_state,
                u.first_name, u.last_name, {$relevanceScore}
                FROM boxes b
                LEFT JOIN users u ON b.created_by = u.id
                LEFT JOIN warehouses w ON b.warehouse_id = w.id
                WHERE {$whereClause}
                ORDER BY relevance_score DESC, b.created_at DESC
                LIMIT 20";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Search in warehouses
     */
    private function searchWarehouses($query, $filters = [])
    {
        $where = ["w.company_id = ?"];
        $params = [$this->user['company_id']];

        // Search in warehouse name, address, and description
        $searchTerms = explode(' ', trim($query));
        $searchConditions = [];

        foreach ($searchTerms as $term) {
            if (!empty($term)) {
                $searchConditions[] = "(w.name LIKE ? OR w.address LIKE ? OR w.description LIKE ?)";
                $searchTerm = '%' . $term . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
        }

        if (!empty($searchConditions)) {
            $where[] = '(' . implode(' AND ', $searchConditions) . ')';
        }

        $whereClause = implode(' AND ', $where);

        // Calculate relevance score
        $relevanceScore = "(" .
            "CASE WHEN w.name LIKE '%{$query}%' THEN 10 ELSE 0 END + " .
            "CASE WHEN w.address LIKE '%{$query}%' THEN 5 ELSE 0 END + " .
            "CASE WHEN w.description LIKE '%{$query}%' THEN 3 ELSE 0 END" .
            ") as relevance_score";

        $sql = "SELECT w.*, {$relevanceScore}
                FROM warehouses w
                WHERE {$whereClause}
                ORDER BY relevance_score DESC, w.created_at DESC
                LIMIT 10";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get bundle search count
     */
    private function getBundleSearchCount($query, $filters = [])
    {
        $where = ["b.company_id = ?"];
        $params = [$this->user['company_id']];

        $searchTerms = explode(' ', trim($query));
        $searchConditions = [];

        foreach ($searchTerms as $term) {
            if (!empty($term)) {
                $searchConditions[] = "(b.name LIKE ? OR b.reference_number LIKE ? OR b.description LIKE ?)";
                $searchTerm = '%' . $term . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
        }

        if (!empty($searchConditions)) {
            $where[] = '(' . implode(' AND ', $searchConditions) . ')';
        }

        $whereClause = implode(' AND ', $where);
        $sql = "SELECT COUNT(*) as count FROM bundles b WHERE {$whereClause}";
        $result = $this->db->fetch($sql, $params);

        return $result['count'] ?? 0;
    }

    /**
     * Get box search count
     */
    private function getBoxSearchCount($query, $filters = [])
    {
        $where = ["b.company_id = ?"];
        $params = [$this->user['company_id']];

        $searchTerms = explode(' ', trim($query));
        $searchConditions = [];

        foreach ($searchTerms as $term) {
            if (!empty($term)) {
                $searchConditions[] = "(b.name LIKE ? OR b.box_id LIKE ? OR b.description LIKE ?)";
                $searchTerm = '%' . $term . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
        }

        if (!empty($searchConditions)) {
            $where[] = '(' . implode(' AND ', $searchConditions) . ')';
        }

        $whereClause = implode(' AND ', $where);
        $sql = "SELECT COUNT(*) as count FROM boxes b WHERE {$whereClause}";
        $result = $this->db->fetch($sql, $params);

        return $result['count'] ?? 0;
    }

    /**
     * Get warehouse search count
     */
    private function getWarehouseSearchCount($query, $filters = [])
    {
        $where = ["w.company_id = ?"];
        $params = [$this->user['company_id']];

        $searchTerms = explode(' ', trim($query));
        $searchConditions = [];

        foreach ($searchTerms as $term) {
            if (!empty($term)) {
                $searchConditions[] = "(w.name LIKE ? OR w.address LIKE ? OR w.description LIKE ?)";
                $searchTerm = '%' . $term . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
        }

        if (!empty($searchConditions)) {
            $where[] = '(' . implode(' AND ', $searchConditions) . ')';
        }

        $whereClause = implode(' AND ', $where);
        $sql = "SELECT COUNT(*) as count FROM warehouses w WHERE {$whereClause}";
        $result = $this->db->fetch($sql, $params);

        return $result['count'] ?? 0;
    }
}
