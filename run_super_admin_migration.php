<?php
require_once 'src/autoload.php';

use App\Core\Database;

$db = Database::getInstance();

echo "Running super admin system migration...\n";

try {
    $sql = file_get_contents('database/migrations/020_create_super_admin_system.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $success = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $db->exec($statement);
                $success++;
                echo "✓ Executed statement successfully\n";
            } catch (Exception $e) {
                $errors++;
                echo "✗ Statement failed: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "Migration completed: {$success} successful, {$errors} errors\n";
    
} catch (Exception $e) {
    echo "Migration error: " . $e->getMessage() . "\n";
}
?>
