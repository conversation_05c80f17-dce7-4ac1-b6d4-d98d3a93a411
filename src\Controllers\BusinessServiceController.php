<?php

namespace App\Controllers;

use App\Services\ServiceFactory;

/**
 * Business Service Controller
 * 
 * Exposes business services through API endpoints
 */
class BusinessServiceController extends BaseController
{
    private $serviceFactory;

    public function __construct()
    {
        parent::__construct();
        
        // Initialize service factory with current user context
        ServiceFactory::initialize($this->db, $this->user, $this->company);
    }

    /**
     * Box Handling Services
     */
    
    /**
     * Create a new box
     */
    public function createBox()
    {
        $this->requireAuth();
        
        try {
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'warehouse_id' => 'required|integer',
                'client_prefix' => 'required|max:20',
                'description' => 'max:1000',
                'storage_type' => 'in:physical,online,mixed',
                'capacity' => 'integer|min:1',
                'barcode' => 'max:100'
            ]);

            $boxService = ServiceFactory::getBoxHandlingService();
            $result = $boxService->createBox($data);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Box created successfully'
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Move box to new location
     */
    public function moveBox($boxId)
    {
        $this->requireAuth();
        
        try {
            $data = $this->validate($_POST, [
                'warehouse_id' => 'required|integer',
                'row_number' => 'max:10',
                'shelf_number' => 'max:10',
                'position_number' => 'max:10'
            ]);

            $boxService = ServiceFactory::getBoxHandlingService();
            $result = $boxService->moveBox($boxId, $data['warehouse_id'], $data);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Box moved successfully'
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Add bundle to box
     */
    public function addBundleToBox()
    {
        $this->requireAuth();
        
        try {
            $data = $this->validate($_POST, [
                'box_id' => 'required|integer',
                'bundle_id' => 'required|integer'
            ]);

            $boxService = ServiceFactory::getBoxHandlingService();
            $result = $boxService->addBundleToBox($data['box_id'], $data['bundle_id']);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Bundle added to box successfully'
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Bundle Handling Services
     */
    
    /**
     * Create a new bundle
     */
    public function createBundle()
    {
        $this->requireAuth();
        
        try {
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'description' => 'max:2000',
                'bundle_type' => 'in:intake,project,department,custom',
                'category' => 'max:100',
                'priority' => 'in:low,medium,high,urgent',
                'retention_period' => 'integer|min:1',
                'access_level' => 'in:public,private,restricted',
                'intake_id' => 'integer'
            ]);

            $bundleService = ServiceFactory::getBundleHandlingService();
            $result = $bundleService->createBundle($data);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Bundle created successfully'
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Close bundle
     */
    public function closeBundle($bundleId)
    {
        $this->requireAuth();
        
        try {
            $options = $_POST['options'] ?? [];

            $bundleService = ServiceFactory::getBundleHandlingService();
            $result = $bundleService->closeBundle($bundleId, $options);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Bundle closed successfully'
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Delivery Services
     */
    
    /**
     * Create delivery request
     */
    public function createDeliveryRequest()
    {
        $this->requireAuth();
        
        try {
            $data = $this->validate($_POST, [
                'target_type' => 'required|in:box,bundle,document',
                'target_id' => 'required|integer',
                'client_id' => 'required|integer',
                'delivery_type' => 'required|in:physical,digital,both',
                'priority' => 'in:low,medium,high,urgent',
                'delivery_method' => 'in:courier,postal,express,pickup,email',
                'requested_delivery_date' => 'date',
                'delivery_address' => 'max:1000',
                'special_instructions' => 'max:2000'
            ]);

            $deliveryService = ServiceFactory::getDeliveryService();
            $result = $deliveryService->createDeliveryRequest($data);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Delivery request created successfully'
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Track delivery
     */
    public function trackDelivery($deliveryReference)
    {
        $this->requireAuth();
        
        try {
            $deliveryService = ServiceFactory::getDeliveryService();
            $result = $deliveryService->trackDelivery($deliveryReference);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Search Services
     */
    
    /**
     * Unified search
     */
    public function search()
    {
        $this->requireAuth();
        
        try {
            $query = $_GET['q'] ?? '';
            $filters = $_GET['filters'] ?? [];
            
            if (empty($query)) {
                throw new \Exception('Search query is required');
            }

            $searchService = ServiceFactory::getSearchService();
            $result = $searchService->unifiedSearch($query, $filters);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get search suggestions
     */
    public function searchSuggestions()
    {
        $this->requireAuth();
        
        try {
            $query = $_GET['q'] ?? '';
            $limit = (int)($_GET['limit'] ?? 10);

            $searchService = ServiceFactory::getSearchService();
            $suggestions = $searchService->getSearchSuggestions($query, $limit);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'suggestions' => $suggestions
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Intake Services
     */
    
    /**
     * Create intake request
     */
    public function createIntakeRequest()
    {
        $this->requireAuth();
        
        try {
            $data = $this->validate($_POST, [
                'client_name' => 'required|max:255',
                'client_id' => 'integer',
                'source' => 'required|max:255',
                'document_type' => 'required|in:contract,invoice,report,image,video,audio,other',
                'description' => 'required|max:2000',
                'priority' => 'in:low,medium,high,urgent',
                'expected_count' => 'integer|min:0',
                'sensitivity_level' => 'in:public,internal,confidential,restricted',
                'department' => 'max:255',
                'notes' => 'max:2000',
                'auto_create_bundle' => 'boolean',
                'notify_client' => 'boolean'
            ]);

            $intakeService = ServiceFactory::getIntakeService();
            $result = $intakeService->createIntakeRequest($data);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Intake request created successfully'
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get service status and health
     */
    public function getServiceStatus()
    {
        $this->requireAuth();
        
        try {
            $services = ServiceFactory::getAvailableServices();
            $status = [];
            
            foreach ($services as $serviceClass => $serviceName) {
                $status[$serviceClass] = [
                    'name' => $serviceName,
                    'status' => 'active',
                    'last_check' => date('Y-m-d H:i:s')
                ];
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'services' => $status,
                'total_services' => count($services)
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
}
