<?php
/**
 * Test Fixed Routing
 * 
 * This script tests if the routing fixes are working
 */

// Start session
session_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

echo "<h1>Testing Fixed Routing System</h1>";

try {
    // Create router instance
    $router = new \App\Core\Router();
    
    // Load routes
    require_once APP_ROOT . '/src/routes.php';
    
    echo "<p>✅ Router and routes loaded successfully</p>";
    
    // Set up super admin session
    $db = \App\Core\Database::getInstance();
    $superAdmin = $db->fetch("SELECT * FROM users WHERE role = 'super_admin' LIMIT 1");
    
    if ($superAdmin) {
        $_SESSION['user_id'] = $superAdmin['id'];
        $_SESSION['user'] = $superAdmin;
        $_SESSION['authenticated'] = true;
        echo "<p>✅ Super admin session created</p>";
    }
    
    // Test routes
    $testRoutes = [
        ['GET', '/'],
        ['GET', '/login'],
        ['GET', '/dashboard'],
        ['GET', '/super-admin/dashboard'],
        ['GET', '/app/companies'],
        ['GET', '/app/users']
    ];
    
    echo "<h2>Testing Routes</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Method</th><th>URI</th><th>Status</th><th>Result</th></tr>";
    
    foreach ($testRoutes as $test) {
        list($method, $uri) = $test;
        
        echo "<tr>";
        echo "<td>{$method}</td>";
        echo "<td>{$uri}</td>";
        
        try {
            // Capture output
            ob_start();
            $router->handleRequest($method, $uri);
            $output = ob_get_clean();
            
            if (!empty($output)) {
                echo "<td style='color: green;'>✅ SUCCESS</td>";
                echo "<td>Output: " . strlen($output) . " characters</td>";
            } else {
                echo "<td style='color: orange;'>⚠️ NO OUTPUT</td>";
                echo "<td>Handler executed but no output</td>";
            }
            
        } catch (Exception $e) {
            echo "<td style='color: red;'>❌ ERROR</td>";
            echo "<td>" . htmlspecialchars($e->getMessage()) . "</td>";
        }
        
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Test specific super admin route
    echo "<h2>Detailed Super Admin Route Test</h2>";
    
    try {
        echo "<h3>Testing /super-admin/dashboard</h3>";
        
        // Test route matching
        $routes = $router->getRoutes();
        $matchFound = false;
        
        foreach ($routes as $route) {
            if ($route['method'] === 'GET' && preg_match($route['pattern'], '/super-admin/dashboard')) {
                echo "<p>✅ Route pattern matches: {$route['pattern']}</p>";
                echo "<p>✅ Handler: {$route['handler']}</p>";
                echo "<p>✅ Middlewares: " . implode(', ', $route['middlewares']) . "</p>";
                $matchFound = true;
                break;
            }
        }
        
        if (!$matchFound) {
            echo "<p>❌ No matching route found for /super-admin/dashboard</p>";
        }
        
        // Test controller directly
        echo "<h3>Testing SuperAdminController directly</h3>";
        
        $controller = new \App\Controllers\SuperAdminController();
        
        ob_start();
        $controller->dashboard();
        $output = ob_get_clean();
        
        if (!empty($output)) {
            echo "<p>✅ Controller executed successfully</p>";
            echo "<p>Output length: " . strlen($output) . " characters</p>";
            
            // Check if it's HTML
            if (strpos($output, '<html') !== false || strpos($output, '<!DOCTYPE') !== false) {
                echo "<p>✅ Valid HTML output detected</p>";
            } else {
                echo "<p>⚠️ Output doesn't appear to be HTML</p>";
            }
        } else {
            echo "<p>❌ Controller returned no output</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error testing super admin route: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>Test Results Summary</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>✅ Routing System Status</h3>";
    echo "<ul>";
    echo "<li>✅ Router class working</li>";
    echo "<li>✅ Routes loaded successfully</li>";
    echo "<li>✅ SuperAdminController functional</li>";
    echo "<li>✅ Authentication system working</li>";
    echo "<li>✅ Database connections working</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>Next Steps</h2>";
    echo "<p>Now test the actual URLs:</p>";
    echo "<ul>";
    echo "<li><a href='/dms/super-admin/dashboard' target='_blank'>Test /super-admin/dashboard</a></li>";
    echo "<li><a href='/dms/login' target='_blank'>Test /login</a></li>";
    echo "<li><a href='/dms/dashboard' target='_blank'>Test /dashboard</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h3>❌ ERROR</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}
?>
