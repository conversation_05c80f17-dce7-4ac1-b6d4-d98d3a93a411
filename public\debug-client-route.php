<?php
/**
 * Debug Client Billing Route
 * 
 * This file helps debug the specific client billing route issue
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

echo "<h1>Client Billing Route Debug</h1>";

try {
    // Test 1: Check if route is registered
    echo "<h3>Test 1: Route Registration</h3>";
    
    $router = new \App\Core\Router();
    
    // Load routes
    require_once APP_ROOT . '/src/routes.php';
    
    echo "✅ Routes loaded<br>";
    
    // Test 2: Check specific route pattern
    echo "<h3>Test 2: Route Pattern Testing</h3>";
    
    $testUri = '/app/billing/client/1';
    $testMethod = 'GET';
    
    echo "Testing URI: <strong>{$testUri}</strong><br>";
    echo "Method: <strong>{$testMethod}</strong><br><br>";
    
    // Get all routes using reflection
    $reflection = new ReflectionClass($router);
    $routesProperty = $reflection->getProperty('routes');
    $routesProperty->setAccessible(true);
    $routes = $routesProperty->getValue($router);
    
    echo "<h4>All Registered Routes:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Method</th><th>Path</th><th>Handler</th><th>Pattern</th></tr>";
    
    $billingRoutes = [];
    foreach ($routes as $route) {
        if (strpos($route['path'], '/billing') !== false) {
            $billingRoutes[] = $route;
            echo "<tr>";
            echo "<td>" . htmlspecialchars($route['method']) . "</td>";
            echo "<td>" . htmlspecialchars($route['path']) . "</td>";
            echo "<td>" . htmlspecialchars($route['handler']) . "</td>";
            echo "<td>" . htmlspecialchars($route['pattern']) . "</td>";
            echo "</tr>";
        }
    }
    echo "</table><br>";
    
    // Test 3: Manual pattern matching
    echo "<h3>Test 3: Manual Pattern Matching</h3>";
    
    $matchFound = false;
    foreach ($billingRoutes as $route) {
        if ($route['method'] === $testMethod) {
            echo "<p>Testing pattern: <code>" . htmlspecialchars($route['pattern']) . "</code></p>";
            echo "<p>Against URI: <code>" . htmlspecialchars($testUri) . "</code></p>";
            
            if (preg_match($route['pattern'], $testUri, $matches)) {
                echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>✅ MATCH FOUND!</h4>";
                echo "<p><strong>Route:</strong> " . htmlspecialchars($route['path']) . "</p>";
                echo "<p><strong>Handler:</strong> " . htmlspecialchars($route['handler']) . "</p>";
                echo "<p><strong>Pattern:</strong> " . htmlspecialchars($route['pattern']) . "</p>";
                echo "<p><strong>Matches:</strong> " . htmlspecialchars(json_encode($matches)) . "</p>";
                echo "</div>";
                $matchFound = true;
                break;
            } else {
                echo "<p style='color: red;'>❌ No match</p>";
            }
            echo "<hr>";
        }
    }
    
    if (!$matchFound) {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ NO MATCHING ROUTE FOUND</h4>";
        echo "</div>";
    }
    
    // Test 4: Check controller and method existence
    echo "<h3>Test 4: Controller and Method Check</h3>";
    
    $controllerClass = "App\\Controllers\\BillingController";
    $method = "clientBilling";
    
    if (class_exists($controllerClass)) {
        echo "✅ Controller exists: {$controllerClass}<br>";
        
        if (method_exists($controllerClass, $method)) {
            echo "✅ Method exists: {$method}<br>";
        } else {
            echo "❌ Method does not exist: {$method}<br>";
        }
    } else {
        echo "❌ Controller does not exist: {$controllerClass}<br>";
    }
    
    // Test 5: Check view file
    echo "<h3>Test 5: View File Check</h3>";
    
    $viewFile = APP_ROOT . "/src/views/billing/client.php";
    if (file_exists($viewFile)) {
        echo "✅ View file exists: {$viewFile}<br>";
    } else {
        echo "❌ View file does not exist: {$viewFile}<br>";
    }
    
    // Test 6: Simulate the actual routing
    echo "<h3>Test 6: Simulate Routing</h3>";
    
    try {
        echo "Attempting to handle request: {$testMethod} {$testUri}<br>";
        
        // This will actually try to execute the route
        ob_start();
        $router->handleRequest($testMethod, $testUri);
        $output = ob_get_clean();
        
        if (!empty($output)) {
            echo "✅ Route executed successfully<br>";
            echo "<details><summary>Output Preview (first 500 chars)</summary>";
            echo "<pre>" . htmlspecialchars(substr($output, 0, 500)) . "...</pre>";
            echo "</details>";
        } else {
            echo "⚠️ Route executed but no output generated<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Route execution failed: " . htmlspecialchars($e->getMessage()) . "<br>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Fatal Error</h3>";
    echo "Error: " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<h3>Test URLs</h3>";
echo '<p><a href="/dms/public/app/billing/client/1" target="_blank">Test: /dms/public/app/billing/client/1</a></p>';
echo '<p><a href="/dms/app/billing/client/1" target="_blank">Test: /dms/app/billing/client/1</a></p>';
echo '<p><a href="/dms/public/index.php/app/billing/client/1" target="_blank">Test: /dms/public/index.php/app/billing/client/1</a></p>';

echo "<hr>";
echo "<h3>Working Alternative</h3>";
echo '<p><a href="/dms/public/billing-direct.php?action=client&id=1" target="_blank">Direct Access: billing-direct.php?action=client&id=1</a></p>';
?>
