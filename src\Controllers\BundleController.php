<?php

namespace App\Controllers;

/**
 * Bundle Controller - Document Bundle Management
 * 
 * Manages logical groupings of documents for organization
 */
class BundleController extends BaseController
{
    /**
     * Display bundles list
     */
    public function index()
    {
        $this->requireAuth();

        try {
            // Get filter parameters with smart defaults
            $page = max(1, (int)($_GET['page'] ?? 1));
            $perPage = min(100, max(10, (int)($_GET['per_page'] ?? 25)));
            $archiveStatus = $_GET['archive_status'] ?? 'active';
            $dateRange = $_GET['date_range'] ?? 'last_3_months';
            $searchTerm = $_GET['search'] ?? '';
            $storageType = $_GET['storage_type'] ?? '';
            $priority = $_GET['priority'] ?? '';
            $category = $_GET['category'] ?? '';

            // Build WHERE clause with enhanced filtering
            $whereConditions = [
                "b.status IN ('active', 'open')",
                "b.name NOT LIKE '[DELETED]%'"
            ];
            $params = [];

            // Company filtering for non-super admins
            if ($this->user['role'] !== 'super_admin') {
                $whereConditions[] = "b.company_id = ?";
                $params[] = $this->user['company_id'];
            }

            // Archive status filter
            if ($archiveStatus === 'archived') {
                $whereConditions[] = "b.archive_status IN ('archived', 'auto_archived')";
            } else {
                $whereConditions[] = "b.archive_status = 'active'";
            }

            // Date range filter (smart defaults)
            $dateCondition = $this->buildDateRangeCondition($dateRange);
            if ($dateCondition) {
                $whereConditions[] = $dateCondition['condition'];
                $params = array_merge($params, $dateCondition['params']);
            }

            // Search filter
            if (!empty($searchTerm)) {
                $whereConditions[] = "(b.name LIKE ? OR b.reference_number LIKE ? OR c.name LIKE ?)";
                $searchParam = '%' . $searchTerm . '%';
                $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
            }

            // Storage type filter
            if (!empty($storageType) && $storageType !== 'all') {
                $whereConditions[] = "b.storage_type = ?";
                $params[] = $storageType;
            }

            // Priority filter
            if (!empty($priority)) {
                $whereConditions[] = "b.priority = ?";
                $params[] = $priority;
            }

            // Category filter
            if (!empty($category)) {
                $whereConditions[] = "b.category = ?";
                $params[] = $category;
            }

            $whereClause = "WHERE " . implode(" AND ", $whereConditions);

            // Get total count for pagination
            $totalCount = $this->db->fetchColumn(
                "SELECT COUNT(DISTINCT b.id) FROM bundles b
                 LEFT JOIN companies c ON b.company_id = c.id
                 {$whereClause}",
                $params
            );

            // Calculate pagination
            $totalPages = ceil($totalCount / $perPage);
            $offset = ($page - 1) * $perPage;

            // Get bundles with pagination
            $bundles = $this->db->fetchAll(
                "SELECT b.*,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT bb.box_id) as box_count,
                        SUM(d.file_size) as total_size,
                        COUNT(DISTINCT CASE WHEN d.storage_type = 'physical' THEN d.id END) as physical_documents,
                        COUNT(DISTINCT CASE WHEN d.storage_type = 'online' THEN d.id END) as online_documents,
                        u.first_name, u.last_name,
                        c.name as company_name,
                        di.expected_count as intake_expected_count,
                        di.actual_count as intake_actual_count,
                        di.reference_number as intake_reference
                 FROM bundles b
                 LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN box_bundles bb ON b.id = bb.bundle_id
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 LEFT JOIN document_intake di ON b.id = di.bundle_id AND di.status != 'cancelled'
                 {$whereClause}
                 GROUP BY b.id, b.storage_type
                 ORDER BY b.created_at DESC
                 LIMIT {$perPage} OFFSET {$offset}",
                $params
            );

            // Get archive statistics
            $archiveStats = $this->getArchiveStatistics();

            $this->view('bundles/index', [
                'title' => 'Bundle Management',
                'bundles' => $bundles,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => $totalPages,
                    'total_count' => $totalCount,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $totalPages
                ],
                'filters' => [
                    'archive_status' => $archiveStatus,
                    'date_range' => $dateRange,
                    'search' => $searchTerm,
                    'storage_type' => $storageType,
                    'priority' => $priority,
                    'category' => $category
                ],
                'archive_stats' => $archiveStats
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading bundles: ' . $e->getMessage(), 'error');
            $this->view('bundles/index', [
                'title' => 'Bundle Management',
                'bundles' => [],
                'pagination' => ['current_page' => 1, 'total_pages' => 1, 'total_count' => 0],
                'filters' => [],
                'archive_stats' => []
            ]);
        }
    }

    /**
     * Show create bundle form
     */
    public function create()
    {
        $this->requireAuth();
        
        // Get available companies (for service provider)
        $companies = $this->db->fetchAll(
            "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name",
            []
        );

        $this->view('bundles/create', [
            'title' => 'Create New Bundle',
            'companies' => $companies
        ]);
    }

    /**
     * Store new bundle
     */
    public function store()
    {
        $this->requireAuth();

        try {
            // Validate input - simplified to essential fields only
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'description' => 'max:1000',
                'company_id' => 'required|integer',
                'category' => 'max:100',
                'priority' => 'in:low,medium,high,urgent',
                'retention_period' => 'integer|min:1|max:100',
                'access_level' => 'in:public,private,restricted',
                'storage_type' => 'in:physical,online,mixed'
            ]);

            // Generate simple reference number
            $reference = $this->generateSimpleReference($data['company_id']);

            // Create bundle with simplified fields including storage type
            $bundleId = $this->db->execute(
                "INSERT INTO bundles (
                    company_id, name, description, reference_number, category, priority,
                    retention_period, access_level, storage_type, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())",
                [
                    $data['company_id'],
                    $data['name'],
                    $data['description'] ?? null,
                    $reference,
                    $data['category'] ?? 'general',
                    $data['priority'] ?? 'medium',
                    $data['retention_period'] ?? 7,
                    $data['access_level'] ?? 'private',
                    $data['storage_type'] ?? 'mixed',
                    $this->user['id']
                ]
            );

            // Log activity
            $this->logActivity('create', 'bundle', $bundleId, "Created bundle: {$data['name']}");

            $this->setFlashMessage('Bundle created successfully', 'success');
            $this->redirect('/app/bundles');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create bundle: ' . $e->getMessage(), 'error');
            $this->redirect('/app/bundles/create');
        }
    }

    /**
     * Show bundle details
     */
    public function show($id)
    {
        $this->requireAuth();

        try {
            $bundle = $this->getBundleById($id);
            if (!$bundle) {
                $this->setFlashMessage('Bundle not found or access denied', 'error');
                $this->redirect('/app/bundles');
                return;
            }


            // Get documents in this bundle
            $documents = $this->getBundleDocuments($id);

            // Get bundle statistics
            $stats = $this->getBundleStats($id);

            // Get boxes that contain this bundle
            $boxes = $this->getBundleBoxes($id);

            // Get intake information for this bundle
            $intake = $this->getBundleIntake($id);

            $this->view('bundles/show', [
                'title' => $bundle['name'],
                'bundle' => $bundle,
                'documents' => $documents,
                'stats' => $stats,
                'boxes' => $boxes,
                'intake' => $intake
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading bundle: ' . $e->getMessage(), 'error');
            $this->redirect('/app/bundles');
        }
    }

    /**
     * Show edit bundle form
     */
    public function edit($id)
    {
        $this->requireAuth();
        
        try {
            $bundle = $this->getBundleById($id);
            if (!$bundle) {
                $this->setFlashMessage('Bundle not found', 'error');
                $this->redirect('/app/bundles');
                return;
            }

            // Get available companies
            $companies = $this->db->fetchAll(
                "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name",
                []
            );

            // Get bundle statistics for document count
            $stats = $this->getBundleStats($id);

            $this->view('bundles/edit', [
                'title' => 'Edit Bundle',
                'bundle' => $bundle,
                'companies' => $companies,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading bundle: ' . $e->getMessage(), 'error');
            $this->redirect('/app/bundles');
        }
    }

    /**
     * Update bundle
     */
    public function update($id)
    {
        $this->requireAuth();
        
        try {
            $bundle = $this->getBundleById($id);
            if (!$bundle) {
                throw new \Exception('Bundle not found');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'description' => 'max:1000',
                'company_id' => 'required|integer',
                'category' => 'max:100',
                'priority' => 'in:low,medium,high,urgent',
                'retention_period' => 'integer|min:1|max:100',
                'access_level' => 'in:public,private,restricted'
            ]);

            // Update bundle
            $this->db->execute(
                "UPDATE bundles SET 
                 name = ?, description = ?, company_id = ?, category = ?, priority = ?,
                 retention_period = ?, access_level = ?, updated_at = NOW()
                 WHERE id = ?",
                [
                    $data['name'],
                    $data['description'] ?? null,
                    $data['company_id'],
                    $data['category'] ?? 'general',
                    $data['priority'] ?? 'medium',
                    $data['retention_period'] ?? 7,
                    $data['access_level'] ?? 'private',
                    $id
                ]
            );

            // Log activity
            $this->logActivity('update', 'bundle', $id, "Updated bundle: {$data['name']}");

            $this->setFlashMessage('Bundle updated successfully', 'success');
            $this->redirect("/app/bundles/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Update failed: ' . $e->getMessage(), 'error');
            $this->redirect("/app/bundles/{$id}/edit");
        }
    }

    /**
     * Delete bundle (soft delete)
     */
    public function delete($id)
    {
        $this->requireAuth();
        
        try {
            $bundle = $this->getBundleById($id);
            if (!$bundle) {
                throw new \Exception('Bundle not found');
            }

            // Check if bundle has documents
            $documentCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM documents WHERE bundle_id = ? AND status != 'deleted'",
                [$id]
            );

            if ($documentCount > 0) {
                throw new \Exception("Cannot delete bundle. It contains {$documentCount} document(s). Please move or delete them first.");
            }

            // Soft delete bundle (using 'open' status with [DELETED] prefix since 'deleted' is not valid)
            // Only add [DELETED] prefix if it's not already there
            $this->db->execute(
                "UPDATE bundles SET
                 status = 'open',
                 name = CASE
                     WHEN name LIKE '[DELETED]%' THEN name
                     ELSE CONCAT('[DELETED] ', name)
                 END,
                 updated_at = NOW()
                 WHERE id = ?",
                [$id]
            );

            // Log activity
            $this->logActivity('delete', 'bundle', $id, "Deleted bundle: {$bundle['name']}");

            $this->setFlashMessage('Bundle deleted successfully', 'success');
            $this->redirect('/app/bundles');

        } catch (\Exception $e) {
            $this->setFlashMessage('Delete failed: ' . $e->getMessage(), 'error');
            $this->redirect("/app/bundles/{$id}");
        }
    }

    /**
     * Get bundle by ID
     */
    private function getBundleById($id)
    {
        // For security, filter by company_id if user is not super admin
        $companyFilter = '';
        $params = [$id];

        if ($this->user['role'] !== 'super_admin') {
            $companyFilter = ' AND b.company_id = ?';
            $params[] = $this->user['company_id'];
        }

        return $this->db->fetch(
            "SELECT b.*, u.first_name, u.last_name, c.name as company_name
             FROM bundles b
             LEFT JOIN users u ON b.created_by = u.id
             LEFT JOIN companies c ON b.company_id = c.id
             WHERE b.id = ? AND b.status IN ('active', 'open') AND b.name NOT LIKE '[DELETED]%'" . $companyFilter,
            $params
        );
    }

    /**
     * Get intake information for a bundle
     */
    private function getBundleIntake($bundleId)
    {
        // For security, filter by company_id if user is not super admin
        $companyFilter = '';
        $params = [$bundleId];

        if ($this->user['role'] !== 'super_admin') {
            $companyFilter = ' AND di.company_id = ?';
            $params[] = $this->user['company_id'];
        }

        return $this->db->fetch(
            "SELECT di.*,
                    u_created.first_name as created_by_first_name,
                    u_created.last_name as created_by_last_name,
                    u_processed.first_name as processed_by_first_name,
                    u_processed.last_name as processed_by_last_name,
                    u_completed.first_name as completed_by_first_name,
                    u_completed.last_name as completed_by_last_name
             FROM document_intake di
             LEFT JOIN users u_created ON di.created_by = u_created.id
             LEFT JOIN users u_processed ON di.processed_by = u_processed.id
             LEFT JOIN users u_completed ON di.completed_by = u_completed.id
             WHERE di.bundle_id = ?" . $companyFilter,
            $params
        );
    }

    /**
     * Get documents in bundle
     */
    private function getBundleDocuments($bundleId)
    {
        return $this->db->fetchAll(
            "SELECT d.*, sl.name as location_name, sl.identifier as location_identifier,
                    w.name as warehouse_name, u.first_name, u.last_name
             FROM documents d
             LEFT JOIN storage_locations sl ON d.location_id = sl.id
             LEFT JOIN warehouses w ON sl.warehouse_id = w.id
             LEFT JOIN users u ON d.created_by = u.id
             WHERE d.bundle_id = ? AND d.status != 'deleted'
             ORDER BY d.created_at DESC",
            [$bundleId]
        );
    }

    /**
     * Get bundle statistics
     */
    private function getBundleStats($bundleId)
    {
        $stats = [];

        try {
            // Total documents
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM documents WHERE bundle_id = ? AND status != 'deleted'",
                [$bundleId]
            );
            $stats['total_documents'] = $result['count'] ?? 0;

            // Total size
            $result = $this->db->fetch(
                "SELECT SUM(file_size) as total FROM documents WHERE bundle_id = ? AND status != 'deleted'",
                [$bundleId]
            );
            $stats['total_size'] = $result['total'] ?? 0;

            // Document types
            $stats['document_types'] = $this->db->fetchAll(
                "SELECT document_type, COUNT(*) as count 
                 FROM documents 
                 WHERE bundle_id = ? AND status != 'deleted' 
                 GROUP BY document_type 
                 ORDER BY count DESC",
                [$bundleId]
            );

            // Storage locations
            $result = $this->db->fetch(
                "SELECT COUNT(DISTINCT location_id) as count 
                 FROM documents 
                 WHERE bundle_id = ? AND status != 'deleted' AND location_id IS NOT NULL",
                [$bundleId]
            );
            $stats['storage_locations'] = $result['count'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get boxes that contain this bundle (REWRITTEN - Enhanced junction table query)
     */
    private function getBundleBoxes($bundleId)
    {
        return $this->db->fetchAll(
            "SELECT b.*,
                    bb.position_in_box,
                    bb.added_at,
                    bb.added_by,
                    w.name as warehouse_name,
                    w.address as warehouse_address,
                    w.city as warehouse_city,
                    w.state as warehouse_state,
                    COUNT(DISTINCT d.id) as document_count_in_bundle,
                    SUM(d.file_size) as bundle_size_in_box,
                    u.first_name as added_by_first_name,
                    u.last_name as added_by_last_name
             FROM box_bundles bb
             JOIN boxes b ON bb.box_id = b.id
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN documents d ON d.bundle_id = ? AND d.status != 'deleted'
             LEFT JOIN users u ON bb.added_by = u.id
             WHERE bb.bundle_id = ?
             GROUP BY b.id, bb.position_in_box, bb.added_at, bb.added_by
             ORDER BY bb.added_at DESC",
            [$bundleId, $bundleId]
        );
    }

    /**
     * Generate simple bundle reference
     * Format: BUN-YYYY-NNNNNN
     */
    private function generateSimpleReference($companyId)
    {
        try {
            // Count existing bundles for this company
            $bundleCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM bundles WHERE company_id = ?",
                [$companyId]
            );

            $bundleNumber = str_pad($bundleCount + 1, 6, '0', STR_PAD_LEFT);
            return 'BUN-' . date('Y') . '-' . $bundleNumber;

        } catch (\Exception $e) {
            // Fallback reference
            return 'BUN-' . date('Y') . '-' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
        }
    }

    /**
     * Build date range condition for filtering
     */
    private function buildDateRangeCondition($dateRange)
    {
        switch ($dateRange) {
            case 'today':
                return [
                    'condition' => 'DATE(b.created_at) = CURDATE()',
                    'params' => []
                ];
            case 'last_7_days':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
                    'params' => []
                ];
            case 'last_30_days':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
                    'params' => []
                ];
            case 'last_3_months':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)',
                    'params' => []
                ];
            case 'last_6_months':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)',
                    'params' => []
                ];
            case 'last_year':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)',
                    'params' => []
                ];
            case 'this_year':
                return [
                    'condition' => 'YEAR(b.created_at) = YEAR(NOW())',
                    'params' => []
                ];
            case 'custom':
                $startDate = $_GET['start_date'] ?? '';
                $endDate = $_GET['end_date'] ?? '';
                if ($startDate && $endDate) {
                    return [
                        'condition' => 'DATE(b.created_at) BETWEEN ? AND ?',
                        'params' => [$startDate, $endDate]
                    ];
                }
                break;
            case 'all':
            default:
                return null;
        }
        return null;
    }

    /**
     * Get archive statistics
     */
    private function getArchiveStatistics()
    {
        try {
            $companyFilter = '';
            $params = [];

            if ($this->user['role'] !== 'super_admin') {
                $companyFilter = 'WHERE company_id = ?';
                $params[] = $this->user['company_id'];
            }

            return $this->db->fetch(
                "SELECT
                    COUNT(CASE WHEN archive_status = 'active' THEN 1 END) as active_count,
                    COUNT(CASE WHEN archive_status = 'archived' THEN 1 END) as archived_count,
                    COUNT(CASE WHEN archive_status = 'auto_archived' THEN 1 END) as auto_archived_count,
                    COUNT(*) as total_count
                 FROM bundles
                 {$companyFilter}",
                $params
            );
        } catch (\Exception $e) {
            return [
                'active_count' => 0,
                'archived_count' => 0,
                'auto_archived_count' => 0,
                'total_count' => 0
            ];
        }
    }

    /**
     * Archive bundle
     */
    public function archive($id)
    {
        $this->requireAuth();

        try {
            $bundle = $this->getBundleById($id);
            if (!$bundle) {
                throw new \Exception('Bundle not found');
            }

            $this->db->execute(
                "UPDATE bundles SET archive_status = 'archived', last_activity_at = NOW() WHERE id = ?",
                [$id]
            );

            $this->logActivity('archive', 'bundle', $id, "Archived bundle: {$bundle['name']}");
            $this->setFlashMessage('Bundle archived successfully', 'success');

        } catch (\Exception $e) {
            $this->setFlashMessage('Archive failed: ' . $e->getMessage(), 'error');
        }

        $this->redirect('/app/bundles');
    }

    /**
     * Unarchive bundle
     */
    public function unarchive($id)
    {
        $this->requireAuth();

        try {
            $bundle = $this->getBundleById($id);
            if (!$bundle) {
                throw new \Exception('Bundle not found');
            }

            $this->db->execute(
                "UPDATE bundles SET archive_status = 'active', last_activity_at = NOW() WHERE id = ?",
                [$id]
            );

            $this->logActivity('unarchive', 'bundle', $id, "Unarchived bundle: {$bundle['name']}");
            $this->setFlashMessage('Bundle unarchived successfully', 'success');

        } catch (\Exception $e) {
            $this->setFlashMessage('Unarchive failed: ' . $e->getMessage(), 'error');
        }

        $this->redirect('/app/bundles');
    }


}
