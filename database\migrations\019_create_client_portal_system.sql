-- Client Portal System Implementation
-- Implements Step 4: Online Integration/Access from documentation

-- Add client role to existing users table
ALTER TABLE users MODIFY COLUMN role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer', 'client') NOT NULL;

-- Create client_companies table for client-to-company relationships
CREATE TABLE IF NOT EXISTS client_companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_user_id INT NOT NULL,
    service_provider_company_id INT NOT NULL,
    client_company_name VARCHAR(255) NOT NULL,
    client_company_id VARCHAR(50), -- External client ID
    access_level ENUM('full', 'limited', 'view_only') DEFAULT 'view_only',
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_user_id) REFERENCES users(id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (service_provider_company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_client_service (client_user_id, service_provider_company_id),
    INDEX idx_client_companies_client (client_user_id),
    INDEX idx_client_companies_service (service_provider_company_id),
    INDEX idx_client_companies_status (status)
);

-- Create document_requests table for retrieval/digitization/destruction requests
CREATE TABLE IF NOT EXISTS document_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_number VARCHAR(50) UNIQUE NOT NULL,
    client_user_id INT NOT NULL,
    service_provider_company_id INT NOT NULL,
    
    -- Request Details
    request_type ENUM('retrieval', 'digitization', 'destruction', 'return') NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    description TEXT NOT NULL,
    
    -- Target Documents/Bundles/Boxes
    target_type ENUM('document', 'bundle', 'box', 'intake') NOT NULL,
    target_id INT NOT NULL,
    target_reference VARCHAR(100), -- Store reference number for easy lookup
    
    -- Request Status
    status ENUM('pending', 'approved', 'rejected', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    
    -- Dates
    requested_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    required_by_date DATE NULL,
    approved_date TIMESTAMP NULL,
    completed_date TIMESTAMP NULL,
    
    -- Staff Assignment
    assigned_to INT NULL,
    approved_by INT NULL,
    completed_by INT NULL,
    
    -- Notes
    client_notes TEXT NULL,
    admin_notes TEXT NULL,
    completion_notes TEXT NULL,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_provider_company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (completed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_requests_client (client_user_id),
    INDEX idx_requests_service (service_provider_company_id),
    INDEX idx_requests_status (status),
    INDEX idx_requests_type (request_type),
    INDEX idx_requests_target (target_type, target_id),
    INDEX idx_requests_assigned (assigned_to),
    INDEX idx_requests_date (requested_date)
);

-- Create request_activity_log for tracking request changes
CREATE TABLE IF NOT EXISTS request_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    action VARCHAR(50) NOT NULL, -- 'created', 'approved', 'rejected', 'assigned', 'completed'
    description TEXT,
    old_status VARCHAR(20),
    new_status VARCHAR(20),
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (request_id) REFERENCES document_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_request_activity (request_id, created_at),
    INDEX idx_request_user_activity (user_id, created_at)
);

-- Create automated_alerts table for retention and other alerts
CREATE TABLE IF NOT EXISTS automated_alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    alert_type ENUM('retention_due', 'retention_overdue', 'storage_limit', 'request_pending', 'document_expiry') NOT NULL,
    company_id INT NOT NULL,
    
    -- Alert Target
    target_type ENUM('document', 'bundle', 'box', 'intake', 'request', 'company') NOT NULL,
    target_id INT NOT NULL,
    target_reference VARCHAR(100),
    
    -- Alert Details
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    severity ENUM('info', 'warning', 'critical') DEFAULT 'info',
    
    -- Alert Status
    status ENUM('pending', 'sent', 'acknowledged', 'resolved') DEFAULT 'pending',
    
    -- Dates
    alert_date DATE NOT NULL,
    due_date DATE NULL,
    sent_at TIMESTAMP NULL,
    acknowledged_at TIMESTAMP NULL,
    resolved_at TIMESTAMP NULL,
    
    -- Recipients
    recipient_user_id INT NULL,
    recipient_email VARCHAR(255) NULL,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_alerts_company (company_id),
    INDEX idx_alerts_type (alert_type),
    INDEX idx_alerts_status (status),
    INDEX idx_alerts_target (target_type, target_id),
    INDEX idx_alerts_date (alert_date),
    INDEX idx_alerts_recipient (recipient_user_id)
);

-- Create client_access_log for tracking client portal access
CREATE TABLE IF NOT EXISTS client_access_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_user_id INT NOT NULL,
    service_provider_company_id INT NOT NULL,
    
    -- Access Details
    action VARCHAR(50) NOT NULL, -- 'login', 'view_document', 'search', 'request', 'download'
    target_type VARCHAR(50) NULL,
    target_id INT NULL,
    target_reference VARCHAR(100) NULL,
    
    -- Session Info
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_provider_company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_client_access_user (client_user_id),
    INDEX idx_client_access_company (service_provider_company_id),
    INDEX idx_client_access_action (action),
    INDEX idx_client_access_date (created_at)
);

-- Insert sample client users for testing
INSERT INTO users (
    company_id, username, email, password_hash, first_name, last_name, 
    role, status, email_verified
) VALUES 
(1, 'client_demo', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Demo', 'Client', 'client', 'active', 1),
(1, 'client_acme', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Smith', 'client', 'active', 1);

-- Link sample clients to service provider
INSERT INTO client_companies (
    client_user_id, service_provider_company_id, client_company_name, 
    client_company_id, access_level, status
) VALUES 
((SELECT id FROM users WHERE username = 'client_demo'), 1, 'Demo Client Company', 'CLIENT01', 'full', 'active'),
((SELECT id FROM users WHERE username = 'client_acme'), 1, 'ACME Corporation', 'CLIENT02', 'view_only', 'active');

-- Create sample requests
INSERT INTO document_requests (
    request_number, client_user_id, service_provider_company_id, request_type, 
    priority, description, target_type, target_id, target_reference, status
) VALUES 
('REQ-2024-001', (SELECT id FROM users WHERE username = 'client_demo'), 1, 'retrieval', 'medium', 'Need to retrieve Q1 2024 financial documents for audit', 'bundle', 1, 'CLIENT01-BOX001-BUNDLE01', 'pending'),
('REQ-2024-002', (SELECT id FROM users WHERE username = 'client_acme'), 1, 'digitization', 'high', 'Request digitization of all HR documents from 2023', 'box', 1, 'CLIENT02-BOX001', 'approved');

-- Create sample alerts for retention due
INSERT INTO automated_alerts (
    alert_type, company_id, target_type, target_id, target_reference, 
    title, message, severity, alert_date, due_date, status
) VALUES 
('retention_due', 1, 'bundle', 1, 'CLIENT01-BOX001-BUNDLE01', 'Retention Period Due', 'Bundle CLIENT01-BOX001-BUNDLE01 retention period expires in 30 days', 'warning', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'pending'),
('storage_limit', 1, 'company', 1, 'COMPANY-001', 'Storage Limit Warning', 'Company storage usage is at 85% capacity', 'warning', CURDATE(), NULL, 'pending');
