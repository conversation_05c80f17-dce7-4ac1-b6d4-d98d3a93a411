<?php

namespace App\Controllers;

/**
 * Dashboard Controller
 * 
 * Handles the main dashboard for authenticated users
 */
class DashboardController extends BaseController
{
    /**
     * Show the dashboard
     */
    public function index()
    {
        $this->requireAuth();
        
        // Get dashboard statistics
        $stats = $this->getDashboardStats();
        
        $this->view('dashboard/index', [
            'title' => 'Dashboard',
            'stats' => $stats
        ]);
    }
    
    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $stats = [
            'total_documents' => 0,
            'total_warehouses' => 0,
            'total_bundles' => 0,
            'total_boxes' => 0,
            'storage_used' => 0,
            'recent_documents' => [],
            'recent_activities' => []
        ];

        // Return empty data if database is not available
        if (!$this->db) {
            return [
                'total_documents' => 0,
                'total_warehouses' => 0,
                'total_bundles' => 0,
                'total_boxes' => 0,
                'storage_used' => 0,
                'recent_documents' => [],
                'recent_activities' => []
            ];
        }

        try {
            // Get document count by storage type
            $documentStats = $this->db->fetchAll(
                "SELECT storage_type, COUNT(*) as count, SUM(file_size) as total_size
                 FROM documents
                 WHERE company_id = ? AND status != 'deleted'
                 GROUP BY storage_type",
                [$this->user['company_id']]
            );

            $stats['total_documents'] = 0;
            $stats['physical_documents'] = 0;
            $stats['online_documents'] = 0;
            $stats['physical_storage_size'] = 0;
            $stats['online_storage_size'] = 0;

            foreach ($documentStats as $docStat) {
                $count = $docStat['count'] ?? 0;
                $size = $docStat['total_size'] ?? 0;
                $stats['total_documents'] += $count;

                if ($docStat['storage_type'] === 'physical') {
                    $stats['physical_documents'] = $count;
                    $stats['physical_storage_size'] = $size;
                } else {
                    $stats['online_documents'] = $count;
                    $stats['online_storage_size'] = $size;
                }
            }
            
            // Get warehouse count
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM warehouses WHERE company_id = ? AND status = 'active'",
                [$this->user['company_id']]
            );
            $stats['total_warehouses'] = $result['count'] ?? 0;
            
            // Get bundle count
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM bundles WHERE company_id = ? AND status != 'archived'",
                [$this->user['company_id']]
            );
            $stats['total_bundles'] = $result['count'] ?? 0;

            // Get box count by storage type
            $boxStats = $this->db->fetchAll(
                "SELECT storage_type, COUNT(*) as count
                 FROM boxes
                 WHERE company_id = ? AND status != 'archived'
                 GROUP BY storage_type",
                [$this->user['company_id']]
            );

            $stats['total_boxes'] = 0;
            $stats['physical_boxes'] = 0;
            $stats['online_boxes'] = 0;

            foreach ($boxStats as $boxStat) {
                $count = $boxStat['count'] ?? 0;
                $stats['total_boxes'] += $count;

                if ($boxStat['storage_type'] === 'physical') {
                    $stats['physical_boxes'] = $count;
                } else {
                    $stats['online_boxes'] = $count;
                }
            }

            // Get intake count (new statistic for documentation compliance)
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM document_intake WHERE company_id = ?",
                [$this->user['company_id']]
            );
            $stats['total_intakes'] = $result['count'] ?? 0;

            // Get recent document uploads for Phase 2 features
            $stats['recent_uploads'] = $this->db->fetchAll(
                "SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.company_id = ? AND d.status != 'archived'
                 ORDER BY d.created_at DESC
                 LIMIT 10",
                [$this->user['company_id']]
            );

            // Get storage used
            $result = $this->db->fetch(
                "SELECT SUM(file_size) as total_size FROM documents WHERE company_id = ? AND status != 'archived'",
                [$this->user['company_id']]
            );
            $stats['storage_used'] = $result['total_size'] ?? 0;
            
            // Get recent documents
            $stats['recent_documents'] = $this->db->fetchAll(
                "SELECT id, title, file_name, file_size, created_at, created_by 
                 FROM documents 
                 WHERE company_id = ? AND status != 'archived' 
                 ORDER BY created_at DESC 
                 LIMIT 5",
                [$this->user['company_id']]
            );
            
            // Get recent activities from multiple sources
            $stats['recent_activities'] = $this->getRecentActivities();
            
        } catch (\Exception $e) {
            // If database queries fail, just return empty stats
            logMessage("Dashboard stats query failed: " . $e->getMessage(), 'error');
        }
        
        return $stats;
    }

    /**
     * Get recent activities from multiple sources
     */
    private function getRecentActivities()
    {
        $activities = [];

        try {
            // Get recent document uploads
            $documentActivities = $this->db->fetchAll(
                "SELECT
                    d.id as entity_id,
                    'document' as entity_type,
                    'upload' as action,
                    d.title as entity_name,
                    d.created_at,
                    u.first_name,
                    u.last_name,
                    'Uploaded document' as description
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.company_id = ? AND d.status != 'deleted'
                 ORDER BY d.created_at DESC
                 LIMIT 5",
                [$this->user['company_id']]
            );

            // Get recent bundle activities
            $bundleActivities = $this->db->fetchAll(
                "SELECT
                    b.id as entity_id,
                    'bundle' as entity_type,
                    'create' as action,
                    b.name as entity_name,
                    b.created_at,
                    u.first_name,
                    u.last_name,
                    'Created bundle' as description
                 FROM bundles b
                 LEFT JOIN users u ON b.created_by = u.id
                 WHERE b.company_id = ?
                 ORDER BY b.created_at DESC
                 LIMIT 5",
                [$this->user['company_id']]
            );

            // Get recent box activities
            $boxActivities = $this->db->fetchAll(
                "SELECT
                    b.id as entity_id,
                    'box' as entity_type,
                    'create' as action,
                    b.name as entity_name,
                    b.created_at,
                    u.first_name,
                    u.last_name,
                    'Created box' as description
                 FROM boxes b
                 LEFT JOIN users u ON b.created_by = u.id
                 WHERE b.company_id = ?
                 ORDER BY b.created_at DESC
                 LIMIT 5",
                [$this->user['company_id']]
            );

            // Get recent intake activities
            $intakeActivities = $this->db->fetchAll(
                "SELECT
                    i.id as entity_id,
                    'intake' as entity_type,
                    'create' as action,
                    i.reference_number as entity_name,
                    i.created_at,
                    u.first_name,
                    u.last_name,
                    'Created intake form' as description
                 FROM document_intake i
                 LEFT JOIN users u ON i.created_by = u.id
                 WHERE i.company_id = ?
                 ORDER BY i.created_at DESC
                 LIMIT 5",
                [$this->user['company_id']]
            );

            // Combine all activities
            $activities = array_merge($documentActivities, $bundleActivities, $boxActivities, $intakeActivities);

            // Sort by created_at descending
            usort($activities, function($a, $b) {
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });

            // Return top 10 activities
            return array_slice($activities, 0, 10);

        } catch (\Exception $e) {
            logMessage("Failed to fetch recent activities: " . $e->getMessage(), 'error');
            return [];
        }
    }
}
