<?php

namespace App\Controllers;

/**
 * Category Controller - Phase 2 Implementation
 * 
 * Handles document category management for better organization
 */
class CategoryController extends BaseController
{
    /**
     * Display categories list
     */
    public function index()
    {
        $this->requireAuth();
        
        try {
            // Get categories with document counts
            try {
                $categories = $this->db->fetchAll(
                    "SELECT c.*,
                            COUNT(d.id) as document_count,
                            u.first_name, u.last_name
                     FROM categories c
                     LEFT JOIN documents d ON c.id = d.category_id AND d.status != 'deleted'
                     LEFT JOIN users u ON c.created_by = u.id
                     WHERE c.company_id = ? AND c.status = 'active'
                     GROUP BY c.id
                     ORDER BY c.name",
                    [$this->user['company_id']]
                );
            } catch (\Exception $e) {
                // Fallback to is_active column if status column doesn't exist
                $categories = $this->db->fetchAll(
                    "SELECT c.*,
                            COUNT(d.id) as document_count,
                            u.first_name, u.last_name
                     FROM categories c
                     LEFT JOIN documents d ON c.id = d.category_id AND d.status != 'deleted'
                     LEFT JOIN users u ON c.created_by = u.id
                     WHERE c.company_id = ? AND c.is_active = 1
                     GROUP BY c.id
                     ORDER BY c.name",
                    [$this->user['company_id']]
                );
            }

            $this->view('categories/index', [
                'title' => 'Document Categories',
                'categories' => $categories
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading categories: ' . $e->getMessage(), 'error');
            $this->redirect('/app/dashboard');
        }
    }

    /**
     * Create new category
     */
    public function store()
    {
        $this->requireAuth();

        // For AJAX requests, ensure clean output
        if ($this->isAjaxRequest()) {
            while (ob_get_level()) {
                ob_end_clean(); // Clear all output buffers
            }
            ob_start(); // Start fresh output buffering
        }

        try {
            // Validate input
            $errors = $this->validate($_POST, [
                'name' => 'required|max:100',
                'description' => 'max:500',
                'color' => 'max:7'
            ]);

            // Check for validation errors
            if (!empty($errors)) {
                $errorMessages = [];
                foreach ($errors as $field => $fieldErrors) {
                    $errorMessages = array_merge($errorMessages, $fieldErrors);
                }
                throw new \Exception('Validation failed: ' . implode(', ', $errorMessages));
            }

            // Extract validated data
            $data = [
                'name' => $_POST['name'],
                'description' => $_POST['description'] ?? null,
                'color' => $_POST['color'] ?? '#3B82F6'
            ];

            // Check for duplicate name
            try {
                $existing = $this->db->fetch(
                    "SELECT id FROM categories WHERE name = ? AND company_id = ? AND status = 'active'",
                    [$data['name'], $this->user['company_id']]
                );
            } catch (\Exception $e) {
                // Fallback to is_active column
                $existing = $this->db->fetch(
                    "SELECT id FROM categories WHERE name = ? AND company_id = ? AND is_active = 1",
                    [$data['name'], $this->user['company_id']]
                );
            }

            if ($existing) {
                throw new \Exception('Category name already exists');
            }

            // Create category
            try {
                $categoryId = $this->db->execute(
                    "INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')",
                    [
                        $this->user['company_id'],
                        $data['name'],
                        $data['description'] ?? null,
                        $data['color'] ?? '#3B82F6',
                        $this->user['id']
                    ]
                );
            } catch (\Exception $e) {
                // Fallback to is_active column
                $categoryId = $this->db->execute(
                    "INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)",
                    [
                        $this->user['company_id'],
                        $data['name'],
                        $data['description'] ?? null,
                        $data['color'] ?? '#3B82F6',
                        $this->user['id']
                    ]
                );
            }

            // Log activity
            $this->logActivity('create', 'category', $categoryId, "Created category: {$data['name']}");

            $this->setFlashMessage('Category created successfully', 'success');
            
            // Return JSON for AJAX requests
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Category created successfully',
                    'category' => [
                        'id' => $categoryId,
                        'name' => $data['name'],
                        'description' => $data['description'] ?? null,
                        'color' => $data['color'] ?? '#3B82F6'
                    ]
                ]);
                exit;
            }

            $this->redirect('/app/categories');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create category: ' . $e->getMessage(), 'error');
            
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            }

            $this->redirect('/app/categories');
        }
    }

    /**
     * Update category
     */
    public function update($id)
    {
        $this->requireAuth();
        
        try {
            // Check if category exists and belongs to company
            $category = $this->db->fetch(
                "SELECT * FROM categories WHERE id = ? AND company_id = ? AND status = 'active'",
                [$id, $this->user['company_id']]
            );

            if (!$category) {
                throw new \Exception('Category not found');
            }

            // Validate input
            $errors = $this->validate($_POST, [
                'name' => 'required|max:100',
                'description' => 'max:500',
                'color' => 'max:7'
            ]);

            // Check for validation errors
            if (!empty($errors)) {
                $errorMessages = [];
                foreach ($errors as $field => $fieldErrors) {
                    $errorMessages = array_merge($errorMessages, $fieldErrors);
                }
                throw new \Exception('Validation failed: ' . implode(', ', $errorMessages));
            }

            // Extract validated data
            $data = [
                'name' => $_POST['name'],
                'description' => $_POST['description'] ?? null,
                'color' => $_POST['color'] ?? '#3B82F6'
            ];

            // Check for duplicate name (excluding current category)
            $existing = $this->db->fetch(
                "SELECT id FROM categories WHERE name = ? AND company_id = ? AND id != ? AND status = 'active'",
                [$data['name'], $this->user['company_id'], $id]
            );

            if ($existing) {
                throw new \Exception('Category name already exists');
            }

            // Update category
            $this->db->execute(
                "UPDATE categories SET name = ?, description = ?, color = ?, updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [
                    $data['name'],
                    $data['description'] ?? null,
                    $data['color'] ?? '#3B82F6',
                    $id,
                    $this->user['company_id']
                ]
            );

            // Log activity
            $this->logActivity('update', 'category', $id, "Updated category: {$data['name']}");

            $this->setFlashMessage('Category updated successfully', 'success');
            
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Category updated successfully'
                ]);
                exit;
            }

            $this->redirect('/app/categories');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to update category: ' . $e->getMessage(), 'error');
            
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            }

            $this->redirect('/app/categories');
        }
    }

    /**
     * Delete category (soft delete)
     */
    public function delete($id)
    {
        $this->requireAuth();
        
        try {
            // Check if category exists and belongs to company
            $category = $this->db->fetch(
                "SELECT * FROM categories WHERE id = ? AND company_id = ? AND status = 'active'",
                [$id, $this->user['company_id']]
            );

            if (!$category) {
                throw new \Exception('Category not found');
            }

            // Check if category has documents
            $documentCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM documents WHERE category_id = ? AND status != 'deleted'",
                [$id]
            );

            if ($documentCount > 0) {
                throw new \Exception("Cannot delete category. It contains {$documentCount} document(s). Please move or delete the documents first.");
            }

            // Soft delete category
            $this->db->execute(
                "UPDATE categories SET status = 'deleted', updated_at = NOW() WHERE id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Log activity
            $this->logActivity('delete', 'category', $id, "Deleted category: {$category['name']}");

            $this->setFlashMessage('Category deleted successfully', 'success');
            
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Category deleted successfully'
                ]);
                exit;
            }

            $this->redirect('/app/categories');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to delete category: ' . $e->getMessage(), 'error');
            
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            }

            $this->redirect('/app/categories');
        }
    }

    /**
     * Get categories for API/AJAX requests
     */
    public function getCategories()
    {
        $this->requireAuth();

        // For AJAX requests, ensure clean output
        while (ob_get_level()) {
            ob_end_clean(); // Clear all output buffers
        }
        ob_start(); // Start fresh output buffering

        try {
            try {
                $categories = $this->db->fetchAll(
                    "SELECT id, name, description, color FROM categories
                     WHERE company_id = ? AND status = 'active'
                     ORDER BY name",
                    [$this->user['company_id']]
                );
            } catch (\Exception $e) {
                // Fallback to is_active column
                $categories = $this->db->fetchAll(
                    "SELECT id, name, description, color FROM categories
                     WHERE company_id = ? AND is_active = 1
                     ORDER BY name",
                    [$this->user['company_id']]
                );
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'categories' => $categories
            ]);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }

    /**
     * Check if request is AJAX
     */
    private function isAjaxRequest()
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
