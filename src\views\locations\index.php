<?php
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="container mx-auto px-6 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        Storage Locations
                    </h1>
                    <p class="text-gray-600">Manage storage locations in <strong><?= e($warehouse['name']) ?></strong></p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/warehouses') ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Warehouses
                    </a>
                    <a href="<?= url('/app/warehouses/' . $warehouse['id'] . '/locations/create') ?>" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Location
                    </a>
                </div>
            </div>
        </div>

        <!-- Warehouse Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold text-blue-900"><?= e($warehouse['name']) ?></h3>
                        <p class="text-blue-700 text-sm"><?= e($warehouse['city']) ?>, <?= e($warehouse['state']) ?></p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm text-blue-700">Total Locations</p>
                    <p class="text-2xl font-bold text-blue-900"><?= count($locations) ?></p>
                </div>
            </div>
        </div>

        <!-- Locations Grid -->
        <?php if (empty($locations)): ?>
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-gray-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No storage locations found</h3>
                <p class="text-gray-600 mb-6">Get started by creating your first storage location in this warehouse.</p>
                <a href="<?= url('/app/warehouses/' . $warehouse['id'] . '/locations/create') ?>" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create First Location
                </a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php foreach ($locations as $location): ?>
                    <div class="group bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <!-- Location Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                    <a href="<?= url('/app/locations/' . $location['id']) ?>">
                                        <?= e($location['name']) ?>
                                    </a>
                                </h3>
                                <p class="text-sm text-gray-600 mt-1 capitalize">
                                    <?= e($location['location_type'] ?? 'shelf') ?>
                                </p>
                            </div>
                            <div class="flex items-center space-x-1">
                                <?php
                                $statusColors = [
                                    'active' => 'bg-green-100 text-green-800',
                                    'maintenance' => 'bg-yellow-100 text-yellow-800',
                                    'full' => 'bg-red-100 text-red-800'
                                ];
                                $statusColor = $statusColors[$location['status']] ?? 'bg-gray-100 text-gray-800';
                                ?>
                                <span class="px-2 py-1 text-xs font-medium rounded-full <?= $statusColor ?>">
                                    <?= ucfirst($location['status']) ?>
                                </span>
                            </div>
                        </div>

                        <!-- Location Stats -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center p-3 bg-blue-50 rounded-lg">
                                <p class="text-xs text-blue-600 font-medium">Documents</p>
                                <p class="text-lg font-bold text-blue-900"><?= $location['document_count'] ?? 0 ?></p>
                            </div>
                            <div class="text-center p-3 bg-purple-50 rounded-lg">
                                <p class="text-xs text-purple-600 font-medium">Capacity</p>
                                <p class="text-lg font-bold text-purple-900"><?= $location['capacity'] ?? 0 ?></p>
                            </div>
                        </div>

                        <!-- Description -->
                        <?php if (!empty($location['description'])): ?>
                            <div class="mb-4">
                                <p class="text-sm text-gray-600 line-clamp-2">
                                    <?= e($location['description']) ?>
                                </p>
                            </div>
                        <?php endif; ?>

                        <!-- Actions -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                            <div class="flex items-center space-x-2">
                                <a href="<?= url('/app/locations/' . $location['id']) ?>" 
                                   class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                   title="View Details">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                                <a href="<?= url('/app/locations/' . $location['id'] . '/edit') ?>" 
                                   class="p-2 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors"
                                   title="Edit">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </a>
                            </div>
                            <div class="text-xs text-gray-500">
                                Created <?= date('M j, Y', strtotime($location['created_at'])) ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
