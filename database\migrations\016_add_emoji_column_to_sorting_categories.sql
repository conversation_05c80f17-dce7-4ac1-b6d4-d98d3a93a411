-- Add emoji column to document_sorting_categories table
-- This migration fixes the missing emoji column error when creating custom sorting categories

-- Add emoji column
ALTER TABLE document_sorting_categories 
ADD COLUMN emoji VARCHAR(10) NULL AFTER color_code;

-- Update existing categories with default emojis
UPDATE document_sorting_categories 
SET emoji = CASE category_code 
    WHEN 'HR' THEN '🏢' 
    WHEN 'FIN' THEN '💰' 
    WHEN 'LEG' THEN '⚖️' 
    WHEN 'OPS' THEN '🔧' 
    WHEN 'MKT' THEN '📢' 
    WHEN 'GEN' THEN '📁' 
    ELSE '📋' 
END 
WHERE emoji IS NULL;
