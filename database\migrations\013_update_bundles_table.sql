-- Update bundles table to match controller expectations
-- Drop and recreate bundles table with proper structure

DROP TABLE IF EXISTS bundles;

CREATE TABLE bundles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    reference_number VARCHAR(50) UNIQUE,
    category VARCHAR(100) DEFAULT 'general',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    retention_period INT DEFAULT 7 COMMENT 'Retention period in years',
    access_level ENUM('public', 'private', 'restricted') DEFAULT 'private',
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_bundles_company (company_id),
    INDEX idx_bundles_reference (reference_number),
    INDEX idx_bundles_category (category),
    INDEX idx_bundles_priority (priority),
    INDEX idx_bundles_status (status),
    INDEX idx_bundles_created (created_at)
);

-- Add bundle_id to documents table if not exists
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS bundle_id INT NULL AFTER company_id,
ADD FOREIGN KEY IF NOT EXISTS fk_documents_bundle (bundle_id) REFERENCES bundles(id) ON DELETE SET NULL,
ADD INDEX IF NOT EXISTS idx_documents_bundle (bundle_id);

-- Insert sample bundles
INSERT INTO bundles (company_id, name, description, reference_number, category, priority, retention_period, access_level, created_by) VALUES
(1, 'Q1 2024 Financial Documents', 'First quarter financial statements and reports', 'BUN-2024-000001', 'financial', 'high', 7, 'private', 1),
(1, 'HR Onboarding Documents', 'New employee onboarding paperwork and forms', 'BUN-2024-000002', 'hr', 'medium', 10, 'restricted', 1),
(1, 'Legal Contracts 2024', 'Client contracts and legal agreements for 2024', 'BUN-2024-000003', 'legal', 'urgent', 15, 'restricted', 1),
(1, 'Marketing Materials', 'Brochures, presentations and marketing collateral', 'BUN-2024-000004', 'marketing', 'low', 3, 'public', 1);
