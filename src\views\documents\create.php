<?php
$title = 'Upload Documents';
ob_start();
?>

<!-- Enhanced Document Upload - Phase 2 Implementation -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- <PERSON> Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4">
                <?php if (!empty($bundle)): ?>
                    <a href="<?= url('/app/bundles/' . $bundle['id']) ?>" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                <?php else: ?>
                    <a href="<?= url('/app/documents') ?>" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                <?php endif; ?>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        Upload Documents
                        <?php if (!empty($bundle)): ?>
                            to Bundle
                        <?php endif; ?>
                    </h1>
                    <p class="text-gray-600 mt-2">
                        <?php if (!empty($bundle)): ?>
                            Upload documents to bundle: <strong><?= e($bundle['name']) ?></strong> (<?= e($bundle['reference_number']) ?>)
                        <?php else: ?>
                            Add new documents to your system with advanced metadata
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Upload Form -->
        <div class="bg-white/80 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md">
            <form action="<?= url('/app/documents') ?>" method="POST" enctype="multipart/form-data" id="uploadForm">

                <!-- Hidden bundle_id field -->
                <?php if (!empty($bundle_id)): ?>
                    <input type="hidden" name="bundle_id" value="<?= e($bundle_id) ?>">
                <?php endif; ?>
                
                <!-- File Upload Area -->
                <div class="mb-8">
                    <label class="block text-lg font-semibold text-gray-900 mb-4">Select Documents</label>
                    
                    <!-- Drag & Drop Area -->
                    <div id="dropZone" class="relative border-2 border-dashed border-gray-300 rounded-2xl p-12 text-center hover:border-blue-400 transition-colors duration-200 bg-gray-50/50">
                        <div class="space-y-4">
                            <div class="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto">
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-xl font-medium text-gray-900">Drop files here or click to browse</p>
                                <p class="text-gray-500 mt-2">Support for PDF, Word, Excel, PowerPoint, Images, Videos, and more</p>
                                <p class="text-sm text-gray-400 mt-1">Maximum file size: 100MB per file</p>
                            </div>
                            <input type="file" name="documents[]" id="fileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.jpg,.jpeg,.png,.gif,.webp,.svg,.mp4,.avi,.mov,.mp3,.wav,.zip,.rar" class="hidden">
                            <button type="button" onclick="document.getElementById('fileInput').click()" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-xl hover:bg-blue-700 transition-colors duration-200">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Choose Files
                            </button>
                        </div>
                    </div>

                    <!-- Selected Files Preview -->
                    <div id="filePreview" class="mt-6 space-y-3 hidden">
                        <h4 class="font-medium text-gray-900">Selected Files:</h4>
                        <div id="fileList" class="space-y-2"></div>
                    </div>
                </div>

                <!-- Document Metadata -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <!-- Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Document Title</label>
                        <input type="text" name="title" id="title" 
                               class="block w-full px-4 py-3 border border-gray-200 rounded-xl bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="Enter document title (optional - will use filename if empty)">
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category_id" id="category_id" 
                                class="block w-full px-4 py-3 border border-gray-200 rounded-xl bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="">Select Category (Optional)</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>"><?= e($category['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea name="description" id="description" rows="4" 
                              class="block w-full px-4 py-3 border border-gray-200 rounded-xl bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                              placeholder="Enter document description (optional)"></textarea>
                </div>

                <!-- Tags -->
                <div class="mb-6">
                    <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                    <input type="text" name="tags" id="tags" 
                           class="block w-full px-4 py-3 border border-gray-200 rounded-xl bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                           placeholder="Enter tags separated by commas (e.g., contract, legal, 2024)">
                    <p class="text-sm text-gray-500 mt-1">Tags help organize and search documents</p>
                </div>

                <!-- Storage Type Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4">Storage Type</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Physical Storage Option -->
                        <div class="relative">
                            <input type="radio" name="storage_type" id="storage_physical" value="physical" checked
                                   class="peer sr-only" onchange="updateStorageOptions()">
                            <label for="storage_physical"
                                   class="flex items-center p-4 bg-gradient-to-br from-amber-50 to-orange-100 border-2 border-amber-200 rounded-xl cursor-pointer hover:border-amber-300 peer-checked:border-amber-500 peer-checked:bg-gradient-to-br peer-checked:from-amber-100 peer-checked:to-orange-200 transition-all duration-200">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">📦 Physical Storage</h4>
                                        <p class="text-sm text-gray-600">Store in warehouse boxes</p>
                                    </div>
                                </div>
                                <div class="ml-auto">
                                    <div class="w-5 h-5 border-2 border-amber-400 rounded-full peer-checked:bg-amber-500 peer-checked:border-amber-500 transition-all duration-200"></div>
                                </div>
                            </label>
                        </div>

                        <!-- Online Storage Option -->
                        <div class="relative">
                            <input type="radio" name="storage_type" id="storage_online" value="online"
                                   class="peer sr-only" onchange="updateStorageOptions()">
                            <label for="storage_online"
                                   class="flex items-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-xl cursor-pointer hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-gradient-to-br peer-checked:from-blue-100 peer-checked:to-indigo-200 transition-all duration-200">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">☁️ Online Storage</h4>
                                        <p class="text-sm text-gray-600">Store digitally only</p>
                                    </div>
                                </div>
                                <div class="ml-auto">
                                    <div class="w-5 h-5 border-2 border-blue-400 rounded-full peer-checked:bg-blue-500 peer-checked:border-blue-500 transition-all duration-200"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Physical Storage Location (shown only for physical storage) -->
                <div id="physicalStorageSection" class="mb-8">
                    <label for="location_id" class="block text-sm font-medium text-gray-700 mb-2">Physical Storage Location</label>
                    <select name="location_id" id="location_id"
                            class="block w-full px-4 py-3 border border-gray-200 rounded-xl bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-200">
                        <option value="">Select Storage Location (Optional)</option>
                        <?php foreach ($locations as $location): ?>
                            <option value="<?= $location['id'] ?>">
                                <?= e($location['warehouse_name']) ?> - <?= e($location['name']) ?>
                                <?php if (!empty($location['identifier'])): ?>
                                    (<?= e($location['identifier']) ?>)
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-sm text-gray-500 mt-1">Select where physical documents will be stored</p>
                </div>

                <!-- Online Storage Path (shown only for online storage) -->
                <div id="onlineStorageSection" class="mb-8 hidden">
                    <label for="online_storage_path" class="block text-sm font-medium text-gray-700 mb-2">Online Storage Path</label>
                    <input type="text" name="online_storage_path" id="online_storage_path"
                           class="block w-full px-4 py-3 border border-gray-200 rounded-xl bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                           placeholder="e.g., /cloud/documents/2025/ (optional)">
                    <p class="text-sm text-gray-500 mt-1">Specify cloud storage path or leave empty for auto-generation</p>
                </div>

                <!-- Upload Progress -->
                <div id="uploadProgress" class="mb-6 hidden">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Uploading...</span>
                        <span id="progressPercent" class="text-sm text-gray-500">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <a href="<?= url('/app/documents') ?>" class="inline-flex items-center px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Cancel
                    </a>
                    <button type="submit" id="uploadBtn" class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        Upload Documents
                    </button>
                </div>
            </form>
        </div>

        <!-- Upload Tips -->
        <div class="mt-8 bg-blue-50/50 border border-blue-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">Upload Tips</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                <div class="flex items-start space-x-2">
                    <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>Supported formats: PDF, Word, Excel, PowerPoint, Images, Videos, Audio, Archives</span>
                </div>
                <div class="flex items-start space-x-2">
                    <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>Maximum file size: 100MB per file</span>
                </div>
                <div class="flex items-start space-x-2">
                    <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>Drag and drop multiple files for batch upload</span>
                </div>
                <div class="flex items-start space-x-2">
                    <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>Automatic duplicate detection and thumbnail generation</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript for File Upload -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    const filePreview = document.getElementById('filePreview');
    const fileList = document.getElementById('fileList');
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const progressPercent = document.getElementById('progressPercent');

    // Drag and drop functionality
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('border-blue-400', 'bg-blue-50');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-blue-400', 'bg-blue-50');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-blue-400', 'bg-blue-50');
        
        const files = e.dataTransfer.files;
        fileInput.files = files;
        displaySelectedFiles(files);
    });

    // File input change
    fileInput.addEventListener('change', function() {
        displaySelectedFiles(this.files);
    });

    // Display selected files
    function displaySelectedFiles(files) {
        if (files.length === 0) {
            filePreview.classList.add('hidden');
            return;
        }

        filePreview.classList.remove('hidden');
        fileList.innerHTML = '';

        Array.from(files).forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-xl';
            
            const fileInfo = document.createElement('div');
            fileInfo.className = 'flex items-center space-x-3';
            
            const fileIcon = getFileIcon(file.type);
            const fileName = document.createElement('span');
            fileName.className = 'font-medium text-gray-900';
            fileName.textContent = file.name;
            
            const fileSize = document.createElement('span');
            fileSize.className = 'text-sm text-gray-500';
            fileSize.textContent = formatBytes(file.size);
            
            fileInfo.appendChild(fileIcon);
            fileInfo.appendChild(fileName);
            fileInfo.appendChild(fileSize);
            
            const removeBtn = document.createElement('button');
            removeBtn.type = 'button';
            removeBtn.className = 'text-red-500 hover:text-red-700 transition-colors';
            removeBtn.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
            removeBtn.onclick = () => removeFile(index);
            
            fileItem.appendChild(fileInfo);
            fileItem.appendChild(removeBtn);
            fileList.appendChild(fileItem);
        });

        updateUploadButton();
    }

    // Get file icon based on type
    function getFileIcon(mimeType) {
        const icon = document.createElement('div');
        icon.className = 'w-8 h-8 flex items-center justify-center rounded-lg';
        
        if (mimeType.startsWith('image/')) {
            icon.className += ' bg-green-100 text-green-600';
            icon.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>';
        } else if (mimeType.includes('pdf')) {
            icon.className += ' bg-red-100 text-red-600';
            icon.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>';
        } else {
            icon.className += ' bg-blue-100 text-blue-600';
            icon.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>';
        }
        
        return icon;
    }

    // Format file size
    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Remove file from selection
    function removeFile(index) {
        const dt = new DataTransfer();
        const files = Array.from(fileInput.files);
        
        files.splice(index, 1);
        files.forEach(file => dt.items.add(file));
        
        fileInput.files = dt.files;
        displaySelectedFiles(fileInput.files);
    }

    // Update upload button state
    function updateUploadButton() {
        uploadBtn.disabled = fileInput.files.length === 0;
    }

    // Form submission with progress
    uploadForm.addEventListener('submit', function(e) {
        if (fileInput.files.length === 0) {
            e.preventDefault();
            alert('Please select at least one file to upload.');
            return;
        }

        uploadBtn.disabled = true;
        uploadProgress.classList.remove('hidden');
        
        // Simulate progress (in real implementation, use XMLHttpRequest for actual progress)
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            
            progressBar.style.width = progress + '%';
            progressPercent.textContent = Math.round(progress) + '%';
            
            if (progress >= 90) {
                clearInterval(interval);
            }
        }, 200);
    });

    // Initialize
    updateUploadButton();
});

// Storage type switching function
function updateStorageOptions() {
    const physicalRadio = document.getElementById('storage_physical');
    const onlineRadio = document.getElementById('storage_online');
    const physicalSection = document.getElementById('physicalStorageSection');
    const onlineSection = document.getElementById('onlineStorageSection');

    if (physicalRadio.checked) {
        physicalSection.classList.remove('hidden');
        onlineSection.classList.add('hidden');
        // Clear online storage path when switching to physical
        document.getElementById('online_storage_path').value = '';
    } else if (onlineRadio.checked) {
        physicalSection.classList.add('hidden');
        onlineSection.classList.remove('hidden');
        // Clear location when switching to online
        document.getElementById('location_id').value = '';
    }
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
