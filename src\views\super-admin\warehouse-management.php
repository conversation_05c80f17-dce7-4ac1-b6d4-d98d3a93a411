<?php
$title = $title ?? 'Warehouse Management';
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
    
    <!-- Header -->
    <div class="bg-white/90 backdrop-blur-sm border-b border-white/30 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Advanced Warehouse Management</h1>
                    <p class="text-gray-600 mt-1">3D visualization, capacity planning, and operational analytics</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">System Status</p>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <p class="font-medium text-green-600">All Systems Operational</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Quick Actions -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="<?= url('/super-admin/warehouse-management/layout') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 00-2 2v2a2 2 0 002 2m0 0h14m-14 0a2 2 0 002 2v2a2 2 0 01-2 2"></path>
                    </svg>
                    3D Layout Viewer
                </a>
                
                <a href="<?= url('/super-admin/warehouse-management/capacity') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Capacity Analytics
                </a>
                
                <a href="<?= url('/super-admin/warehouse-management/movements') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    Movement Tracking
                </a>
                
                <a href="<?= url('/super-admin/warehouse-management/efficiency') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    Efficiency Reports
                </a>
            </div>
        </div>

        <!-- Warehouse Overview Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Total Warehouses -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Warehouses</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($warehouseStats['active_warehouses']) ?></p>
                        <p class="text-xs text-green-600"><?= number_format($warehouseStats['total_warehouses']) ?> total</p>
                    </div>
                </div>
            </div>

            <!-- Total Boxes -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Boxes</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($warehouseStats['total_boxes']) ?></p>
                        <p class="text-xs text-green-600"><?= number_format($warehouseStats['occupied_boxes']) ?> occupied</p>
                    </div>
                </div>
            </div>

            <!-- Storage Capacity -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Avg Capacity Usage</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($warehouseStats['avg_capacity_usage'], 1) ?>%</p>
                        <p class="text-xs text-blue-600">Across all warehouses</p>
                    </div>
                </div>
            </div>

            <!-- Total Documents -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Documents</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($warehouseStats['total_documents']) ?></p>
                        <p class="text-xs text-orange-600"><?= number_format($warehouseStats['total_bundles']) ?> bundles</p>
                    </div>
                </div>
            </div>

        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Warehouse Capacity Overview -->
            <div class="lg:col-span-2 bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Warehouse Capacity Overview</h3>
                    <a href="<?= url('/super-admin/warehouse-management/capacity') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View detailed analytics →
                    </a>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Warehouse</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Capacity</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Boxes</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Utilization</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($capacityData, 0, 8) as $warehouse): ?>
                                <tr class="border-b border-gray-100 hover:bg-gray-50">
                                    <td class="py-3 px-4">
                                        <div>
                                            <p class="font-medium text-gray-900"><?= e($warehouse['warehouse_name']) ?></p>
                                            <p class="text-sm text-gray-500"><?= number_format($warehouse['total_boxes']) ?> total boxes</p>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-<?= $warehouse['capacity_percentage'] > 80 ? 'red' : ($warehouse['capacity_percentage'] > 60 ? 'yellow' : 'green') ?>-500 h-2 rounded-full" style="width: <?= min(100, $warehouse['capacity_percentage']) ?>%"></div>
                                            </div>
                                            <span class="text-xs text-gray-600"><?= round($warehouse['capacity_percentage'], 1) ?>%</span>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="text-sm">
                                            <p class="text-green-600"><?= number_format($warehouse['occupied_boxes']) ?> occupied</p>
                                            <p class="text-gray-500"><?= number_format($warehouse['empty_boxes']) ?> empty</p>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $warehouse['capacity_percentage'] > 80 ? 'red' : ($warehouse['capacity_percentage'] > 60 ? 'yellow' : 'green') ?>-100 text-<?= $warehouse['capacity_percentage'] > 80 ? 'red' : ($warehouse['capacity_percentage'] > 60 ? 'yellow' : 'green') ?>-800">
                                            <?= $warehouse['capacity_percentage'] > 80 ? 'High' : ($warehouse['capacity_percentage'] > 60 ? 'Medium' : 'Low') ?>
                                        </span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Activity & Efficiency -->
            <div class="space-y-6">
                
                <!-- Recent Movements -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Movements</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <?= count($recentMovements) ?> today
                        </span>
                    </div>
                    
                    <div class="space-y-3">
                        <?php if (!empty($recentMovements)): ?>
                            <?php foreach (array_slice($recentMovements, 0, 5) as $movement): ?>
                                <div class="flex items-start p-3 bg-gray-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <h4 class="text-sm font-medium text-gray-900"><?= e($movement['box_id']) ?></h4>
                                        <p class="text-sm text-gray-600 mt-1"><?= e($movement['from_location']) ?> → <?= e($movement['to_location']) ?></p>
                                        <p class="text-xs text-gray-500 mt-1">by <?= e($movement['first_name'] . ' ' . $movement['last_name']) ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-gray-500 text-sm">No recent movements</p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mt-4">
                        <a href="<?= url('/super-admin/warehouse-management/movements') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View all movements →
                        </a>
                    </div>
                </div>

                <!-- Efficiency Metrics -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Today's Efficiency</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Total Movements</span>
                            <span class="font-medium text-gray-900"><?= number_format($efficiencyMetrics['total_movements_today']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Active Staff</span>
                            <span class="font-medium text-gray-900"><?= number_format($efficiencyMetrics['active_staff_today']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Movements (Last Hour)</span>
                            <span class="font-medium text-gray-900"><?= number_format($efficiencyMetrics['movements_last_hour']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Avg Processing Time</span>
                            <span class="font-medium text-green-600"><?= round($efficiencyMetrics['avg_movement_time'], 1) ?> min</span>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>
</div>

<script>
// Auto-refresh dashboard every 2 minutes for real-time updates
setTimeout(() => {
    location.reload();
}, 120000);

// Initialize any charts or interactive elements here
document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript for interactive features
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
