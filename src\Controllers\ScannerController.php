<?php

namespace App\Controllers;

/**
 * Scanner Controller
 * 
 * Handles barcode scanning functionality
 */
class ScannerController extends BaseController
{
    /**
     * Show barcode scanner interface
     */
    public function index()
    {
        $this->requireAuth();
        
        $this->view('scanner/index', [
            'title' => 'Barcode Scanner'
        ]);
    }
    
    /**
     * Process barcode scan
     */
    public function scan()
    {
        $this->requireAuth();
        
        try {
            $barcode = $_POST['barcode'] ?? '';
            $scanType = $_POST['scan_type'] ?? 'lookup'; // lookup, assign, verify
            
            if (empty($barcode)) {
                throw new \Exception('Barcode is required.');
            }
            
            // Clean and validate barcode
            $barcode = trim($barcode);
            
            switch ($scanType) {
                case 'lookup':
                    $result = $this->lookupBarcode($barcode);
                    break;
                case 'assign':
                    $result = $this->assignBarcode($barcode);
                    break;
                case 'verify':
                    $result = $this->verifyBarcode($barcode);
                    break;
                default:
                    throw new \Exception('Invalid scan type.');
            }
            
            $this->jsonResponse([
                'success' => true,
                'barcode' => $barcode,
                'scan_type' => $scanType,
                'result' => $result
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Lookup barcode to find associated item
     */
    private function lookupBarcode($barcode)
    {
        $result = [
            'found' => false,
            'type' => null,
            'item' => null
        ];
        
        // Check documents
        $document = $this->db->fetch(
            "SELECT d.*, c.name as category_name, w.name as warehouse_name, b.name as bundle_name, box.name as box_name
             FROM documents d
             LEFT JOIN categories c ON d.category_id = c.id
             LEFT JOIN warehouses w ON d.warehouse_id = w.id
             LEFT JOIN bundles b ON d.bundle_id = b.id
             LEFT JOIN boxes box ON d.box_id = box.id
             WHERE d.barcode = ? AND d.company_id = ?",
            [$barcode, $this->user['company_id']]
        );
        
        if ($document) {
            $result['found'] = true;
            $result['type'] = 'document';
            $result['item'] = $document;
            return $result;
        }
        
        // Check boxes
        $box = $this->db->fetch(
            "SELECT b.*, w.name as warehouse_name, bun.name as bundle_name,
                    COUNT(d.id) as document_count
             FROM boxes b
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN bundles bun ON b.bundle_id = bun.id
             LEFT JOIN documents d ON b.id = d.box_id
             WHERE b.barcode = ? AND b.company_id = ?
             GROUP BY b.id",
            [$barcode, $this->user['company_id']]
        );
        
        if ($box) {
            $result['found'] = true;
            $result['type'] = 'box';
            $result['item'] = $box;
            return $result;
        }
        
        // Check bundles
        $bundle = $this->db->fetch(
            "SELECT b.*, w.name as warehouse_name,
                    COUNT(DISTINCT box.id) as box_count,
                    COUNT(DISTINCT d.id) as document_count
             FROM bundles b
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN boxes box ON b.id = box.bundle_id
             LEFT JOIN documents d ON b.id = d.bundle_id
             WHERE b.barcode = ? AND b.company_id = ?
             GROUP BY b.id",
            [$barcode, $this->user['company_id']]
        );
        
        if ($bundle) {
            $result['found'] = true;
            $result['type'] = 'bundle';
            $result['item'] = $bundle;
            return $result;
        }
        
        // Check warehouses
        $warehouse = $this->db->fetch(
            "SELECT w.*,
                    COUNT(DISTINCT b.id) as bundle_count,
                    COUNT(DISTINCT box.id) as box_count,
                    COUNT(DISTINCT d.id) as document_count
             FROM warehouses w
             LEFT JOIN bundles b ON w.id = b.warehouse_id
             LEFT JOIN boxes box ON w.id = box.warehouse_id
             LEFT JOIN documents d ON w.id = d.warehouse_id
             WHERE w.barcode = ? AND w.company_id = ?
             GROUP BY w.id",
            [$barcode, $this->user['company_id']]
        );
        
        if ($warehouse) {
            $result['found'] = true;
            $result['type'] = 'warehouse';
            $result['item'] = $warehouse;
            return $result;
        }
        
        return $result;
    }
    
    /**
     * Assign barcode to an item
     */
    private function assignBarcode($barcode)
    {
        $itemType = $_POST['item_type'] ?? '';
        $itemId = $_POST['item_id'] ?? '';
        
        if (empty($itemType) || empty($itemId)) {
            throw new \Exception('Item type and ID are required for barcode assignment.');
        }
        
        // Check if barcode is already in use
        $existing = $this->lookupBarcode($barcode);
        if ($existing['found']) {
            throw new \Exception('Barcode is already assigned to a ' . $existing['type'] . '.');
        }
        
        // Assign barcode based on item type
        switch ($itemType) {
            case 'document':
                $this->assignBarcodeToDocument($barcode, $itemId);
                break;
            case 'box':
                $this->assignBarcodeToBox($barcode, $itemId);
                break;
            case 'bundle':
                $this->assignBarcodeToBundle($barcode, $itemId);
                break;
            case 'warehouse':
                $this->assignBarcodeToWarehouse($barcode, $itemId);
                break;
            default:
                throw new \Exception('Invalid item type for barcode assignment.');
        }
        
        return [
            'assigned' => true,
            'item_type' => $itemType,
            'item_id' => $itemId
        ];
    }
    
    /**
     * Verify barcode matches expected item
     */
    private function verifyBarcode($barcode)
    {
        $expectedType = $_POST['expected_type'] ?? '';
        $expectedId = $_POST['expected_id'] ?? '';
        
        if (empty($expectedType) || empty($expectedId)) {
            throw new \Exception('Expected type and ID are required for verification.');
        }
        
        $lookup = $this->lookupBarcode($barcode);
        
        if (!$lookup['found']) {
            return [
                'verified' => false,
                'message' => 'Barcode not found in system.'
            ];
        }
        
        $verified = ($lookup['type'] === $expectedType && $lookup['item']['id'] == $expectedId);
        
        return [
            'verified' => $verified,
            'found_type' => $lookup['type'],
            'found_id' => $lookup['item']['id'],
            'expected_type' => $expectedType,
            'expected_id' => $expectedId,
            'message' => $verified ? 'Barcode verified successfully.' : 'Barcode does not match expected item.'
        ];
    }
    
    /**
     * Assign barcode to document
     */
    private function assignBarcodeToDocument($barcode, $documentId)
    {
        // Verify document exists and belongs to company
        $document = $this->db->fetch(
            "SELECT id FROM documents WHERE id = ? AND company_id = ?",
            [$documentId, $this->user['company_id']]
        );
        
        if (!$document) {
            throw new \Exception('Document not found.');
        }
        
        $this->db->update('documents', 
            ['barcode' => $barcode, 'updated_at' => date('Y-m-d H:i:s')], 
            ['id' => $documentId]
        );
    }
    
    /**
     * Assign barcode to box
     */
    private function assignBarcodeToBox($barcode, $boxId)
    {
        // Verify box exists and belongs to company
        $box = $this->db->fetch(
            "SELECT id FROM boxes WHERE id = ? AND company_id = ?",
            [$boxId, $this->user['company_id']]
        );
        
        if (!$box) {
            throw new \Exception('Box not found.');
        }
        
        $this->db->update('boxes', 
            ['barcode' => $barcode, 'updated_at' => date('Y-m-d H:i:s')], 
            ['id' => $boxId]
        );
    }
    
    /**
     * Assign barcode to bundle
     */
    private function assignBarcodeToBundle($barcode, $bundleId)
    {
        // Verify bundle exists and belongs to company
        $bundle = $this->db->fetch(
            "SELECT id FROM bundles WHERE id = ? AND company_id = ?",
            [$bundleId, $this->user['company_id']]
        );
        
        if (!$bundle) {
            throw new \Exception('Bundle not found.');
        }
        
        $this->db->update('bundles', 
            ['barcode' => $barcode, 'updated_at' => date('Y-m-d H:i:s')], 
            ['id' => $bundleId]
        );
    }
    
    /**
     * Assign barcode to warehouse
     */
    private function assignBarcodeToWarehouse($barcode, $warehouseId)
    {
        // Verify warehouse exists and belongs to company
        $warehouse = $this->db->fetch(
            "SELECT id FROM warehouses WHERE id = ? AND company_id = ?",
            [$warehouseId, $this->user['company_id']]
        );
        
        if (!$warehouse) {
            throw new \Exception('Warehouse not found.');
        }
        
        $this->db->update('warehouses', 
            ['barcode' => $barcode, 'updated_at' => date('Y-m-d H:i:s')], 
            ['id' => $warehouseId]
        );
    }
}
