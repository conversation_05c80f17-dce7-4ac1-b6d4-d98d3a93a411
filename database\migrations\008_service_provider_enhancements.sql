-- Service Provider Model Enhancements
-- Updates to support third-party document storage service provider business model

-- Add storage type to documents table (physical vs online)
ALTER TABLE documents 
ADD COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER location_id,
ADD COLUMN online_storage_path VARCHAR(500) NULL AFTER storage_type,
ADD COLUMN physical_location_notes TEXT NULL AFTER online_storage_path,
ADD INDEX idx_storage_type (storage_type),
ADD INDEX idx_company_storage (company_id, storage_type);

-- Add storage type to storage_locations table
ALTER TABLE storage_locations 
ADD COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER type,
ADD COLUMN online_storage_config JSON NULL AFTER storage_type,
ADD INDEX idx_location_storage_type (storage_type);

-- Add service provider specific fields to companies table
ALTER TABLE companies 
ADD COLUMN service_plan ENUM('basic', 'standard', 'premium', 'enterprise') DEFAULT 'basic' AFTER status,
ADD COLUMN physical_storage_limit INT DEFAULT 100 COMMENT 'Number of boxes allowed',
ADD COLUMN online_storage_limit BIGINT DEFAULT ********** COMMENT 'Online storage limit in bytes (1GB default)',
ADD COLUMN monthly_fee DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN contract_start_date DATE NULL,
ADD COLUMN contract_end_date DATE NULL,
ADD COLUMN billing_contact_email VARCHAR(255) NULL,
ADD COLUMN storage_preferences JSON NULL COMMENT 'Client storage preferences and settings';

-- Create service provider settings table
CREATE TABLE IF NOT EXISTS service_provider_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'decimal', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key)
);

-- Insert default service provider settings
INSERT INTO service_provider_settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'SecureDoc Storage Services', 'string', 'Service provider company name'),
('physical_storage_rate', '5.00', 'decimal', 'Monthly rate per physical box'),
('online_storage_rate', '0.10', 'decimal', 'Monthly rate per GB of online storage'),
('setup_fee', '50.00', 'decimal', 'One-time setup fee for new clients'),
('max_warehouses', '10', 'integer', 'Maximum number of warehouses'),
('default_retention_period', '7', 'integer', 'Default document retention period in years'),
('backup_frequency', '24', 'integer', 'Backup frequency in hours'),
('security_level', 'high', 'string', 'Security level for document storage');

-- Create client billing table
CREATE TABLE IF NOT EXISTS client_billing (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    billing_period_start DATE NOT NULL,
    billing_period_end DATE NOT NULL,
    
    -- Physical storage charges
    physical_boxes_used INT DEFAULT 0,
    physical_storage_rate DECIMAL(10,2) DEFAULT 0.00,
    physical_storage_total DECIMAL(10,2) DEFAULT 0.00,
    
    -- Online storage charges
    online_storage_used BIGINT DEFAULT 0 COMMENT 'Bytes used',
    online_storage_rate DECIMAL(10,4) DEFAULT 0.00 COMMENT 'Rate per GB',
    online_storage_total DECIMAL(10,2) DEFAULT 0.00,
    
    -- Additional services
    additional_services_total DECIMAL(10,2) DEFAULT 0.00,
    
    -- Total billing
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    
    -- Billing status
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    invoice_number VARCHAR(50) UNIQUE,
    invoice_date DATE,
    due_date DATE,
    paid_date DATE NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_company_billing (company_id, billing_period_start),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_billing_status (status)
);

-- Create storage location tracking table
CREATE TABLE IF NOT EXISTS storage_location_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    location_id INT NOT NULL,
    document_id INT NULL,
    action ENUM('assign', 'remove', 'move', 'scan') NOT NULL,
    previous_location_id INT NULL,
    notes TEXT NULL,
    performed_by INT NOT NULL,
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (previous_location_id) REFERENCES storage_locations(id) ON DELETE SET NULL,
    FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_location_tracking (location_id, performed_at),
    INDEX idx_document_tracking (document_id, performed_at)
);

-- Update existing data to set default storage types
UPDATE documents SET storage_type = 'physical' WHERE storage_type IS NULL;
UPDATE storage_locations SET storage_type = 'physical' WHERE storage_type IS NULL;

-- Create view for service provider dashboard statistics
CREATE OR REPLACE VIEW service_provider_stats AS
SELECT 
    (SELECT COUNT(*) FROM companies WHERE status = 'active') as total_clients,
    (SELECT COUNT(*) FROM documents WHERE status != 'deleted') as total_documents,
    (SELECT COUNT(*) FROM documents WHERE storage_type = 'physical' AND status != 'deleted') as physical_documents,
    (SELECT COUNT(*) FROM documents WHERE storage_type = 'online' AND status != 'deleted') as online_documents,
    (SELECT COUNT(*) FROM storage_locations sl JOIN warehouses w ON sl.warehouse_id = w.id WHERE sl.type = 'box' AND sl.storage_type = 'physical' AND sl.status = 'active') as total_physical_boxes,
    (SELECT SUM(file_size) FROM documents WHERE storage_type = 'online' AND status != 'deleted') as total_online_storage,
    (SELECT COUNT(*) FROM document_intake WHERE status = 'pending') as pending_intakes,
    (SELECT SUM(total_amount) FROM client_billing WHERE status = 'paid' AND MONTH(paid_date) = MONTH(CURDATE()) AND YEAR(paid_date) = YEAR(CURDATE())) as monthly_revenue;

-- Create view for client storage summary
CREATE OR REPLACE VIEW client_storage_summary AS
SELECT 
    c.id as company_id,
    c.name as company_name,
    c.service_plan,
    c.physical_storage_limit,
    c.online_storage_limit,
    COUNT(DISTINCT CASE WHEN d.storage_type = 'physical' THEN d.id END) as physical_document_count,
    COUNT(DISTINCT CASE WHEN d.storage_type = 'online' THEN d.id END) as online_document_count,
    COUNT(DISTINCT CASE WHEN sl.storage_type = 'physical' AND sl.type = 'box' THEN sl.id END) as physical_boxes_used,
    COALESCE(SUM(CASE WHEN d.storage_type = 'online' THEN d.file_size END), 0) as online_storage_used,
    COUNT(DISTINCT CASE WHEN di.status = 'pending' THEN di.id END) as pending_intakes
FROM companies c
LEFT JOIN documents d ON c.id = d.company_id AND d.status != 'deleted'
LEFT JOIN warehouses w ON c.id = w.company_id
LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
LEFT JOIN document_intake di ON c.id = di.company_id
WHERE c.status = 'active'
GROUP BY c.id, c.name, c.service_plan, c.physical_storage_limit, c.online_storage_limit;

-- Sample data for demonstration
-- Update existing companies with service provider details
UPDATE companies SET 
    service_plan = 'standard',
    physical_storage_limit = 50,
    online_storage_limit = **********, -- 5GB
    monthly_fee = 150.00,
    contract_start_date = '2024-01-01',
    contract_end_date = '2024-12-31',
    billing_contact_email = '<EMAIL>'
WHERE id = 1;

-- Add some sample physical and online storage locations
INSERT INTO storage_locations (warehouse_id, name, identifier, type, storage_type, capacity, status) VALUES
(1, 'Physical Box A-001', 'PB-A001', 'box', 'physical', 100, 'active'),
(1, 'Physical Box A-002', 'PB-A002', 'box', 'physical', 100, 'active'),
(1, 'Online Storage Vault 1', 'OSV-001', 'box', 'online', **********, 'active'), -- 1GB capacity
(1, 'Online Storage Vault 2', 'OSV-002', 'box', 'online', **********, 'active'); -- 1GB capacity
