<?php
/**
 * Simple Test Script
 * 
 * This script tests basic functionality without complex routing
 */

echo "<h1>Simple DMS Test</h1>";

// Test if we can access the application files
echo "<h2>File Access Test</h2>";

$appRoot = dirname(__DIR__);
echo "<p><strong>App Root:</strong> " . $appRoot . "</p>";

// Test autoloader
if (file_exists($appRoot . '/src/autoload.php')) {
    echo "<p>✅ Autoloader found</p>";
    require_once $appRoot . '/src/autoload.php';
    echo "<p>✅ Autoloader loaded</p>";
} else {
    echo "<p>❌ Autoloader not found</p>";
    exit;
}

// Test database connection
try {
    $db = \App\Core\Database::getInstance();
    echo "<p>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test if SuperAdminController exists
if (class_exists('App\\Controllers\\SuperAdminController')) {
    echo "<p>✅ SuperAdminController class exists</p>";
    
    try {
        $controller = new \App\Controllers\SuperAdminController();
        echo "<p>✅ SuperAdminController instantiated</p>";
        
        if (method_exists($controller, 'dashboard')) {
            echo "<p>✅ dashboard method exists</p>";
        } else {
            echo "<p>❌ dashboard method missing</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error with controller: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ SuperAdminController class not found</p>";
}

// Test router
try {
    $router = new \App\Core\Router();
    echo "<p>✅ Router instantiated</p>";
    
    // Load routes
    require_once $appRoot . '/src/routes.php';
    echo "<p>✅ Routes loaded</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Router error: " . $e->getMessage() . "</p>";
}

// Test session and authentication
session_start();
echo "<h2>Authentication Test</h2>";

if (isset($_SESSION['user_id'])) {
    echo "<p>✅ User logged in (ID: {$_SESSION['user_id']})</p>";
    if (isset($_SESSION['user']['role'])) {
        echo "<p>✅ User role: {$_SESSION['user']['role']}</p>";
    }
} else {
    echo "<p>❌ User not logged in</p>";
    
    // Try to get super admin user from database
    try {
        $superAdmin = $db->fetch("SELECT * FROM users WHERE role = 'super_admin' LIMIT 1");
        if ($superAdmin) {
            echo "<p>✅ Super admin user exists in database</p>";
            echo "<p><strong>Username:</strong> {$superAdmin['username']}</p>";
            echo "<p><strong>Email:</strong> {$superAdmin['email']}</p>";
            
            // Simulate login
            $_SESSION['user_id'] = $superAdmin['id'];
            $_SESSION['user'] = $superAdmin;
            $_SESSION['authenticated'] = true;
            
            echo "<p>✅ Simulated login for testing</p>";
        } else {
            echo "<p>❌ No super admin user found</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Database query error: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>Direct Controller Test</h2>";

// Try to call the dashboard method directly
if (isset($_SESSION['user_id'])) {
    try {
        // Set up the environment like the controller expects
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_URI'] = '/super-admin/dashboard';
        
        $controller = new \App\Controllers\SuperAdminController();
        
        // Call dashboard method directly
        ob_start();
        $controller->dashboard();
        $output = ob_get_clean();
        
        if (!empty($output)) {
            echo "<p>✅ Dashboard method executed successfully</p>";
            echo "<p><strong>Output length:</strong> " . strlen($output) . " characters</p>";
            
            // Show first 500 characters of output
            echo "<h3>Dashboard Output Preview:</h3>";
            echo "<div style='border: 1px solid #ccc; padding: 10px; max-height: 300px; overflow: auto;'>";
            echo "<pre>" . htmlspecialchars(substr($output, 0, 500)) . "...</pre>";
            echo "</div>";
        } else {
            echo "<p>❌ Dashboard method returned no output</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Dashboard method error: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
} else {
    echo "<p>⚠️ Cannot test dashboard - no user logged in</p>";
}

echo "<h2>Manual Links</h2>";
echo "<p>Try these links manually:</p>";
echo "<ul>";
echo "<li><a href='/dms/' target='_blank'>Home</a></li>";
echo "<li><a href='/dms/login' target='_blank'>Login</a></li>";
echo "<li><a href='/dms/dashboard' target='_blank'>Dashboard</a></li>";
echo "<li><a href='/dms/super-admin/dashboard' target='_blank'>Super Admin Dashboard</a></li>";
echo "</ul>";
?>
