    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <span class="text-xl font-bold text-gray-900">DMS Client Portal</span>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">
                        Secure document management and storage services. Access your documents anytime, anywhere with our professional document management system.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-gray-600">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-600">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-600">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-600">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="<?= url('/client/dashboard') ?>" class="text-gray-600 hover:text-gray-900 text-sm">
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="<?= url('/client/documents') ?>" class="text-gray-600 hover:text-gray-900 text-sm">
                                My Documents
                            </a>
                        </li>
                        <li>
                            <a href="<?= url('/client/requests') ?>" class="text-gray-600 hover:text-gray-900 text-sm">
                                Requests
                            </a>
                        </li>
                        <li>
                            <a href="<?= url('/client/create-request') ?>" class="text-gray-600 hover:text-gray-900 text-sm">
                                New Request
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="<?= url('/client/help') ?>" class="text-gray-600 hover:text-gray-900 text-sm">
                                Help Center
                            </a>
                        </li>
                        <li>
                            <a href="<?= url('/client/contact') ?>" class="text-gray-600 hover:text-gray-900 text-sm">
                                Contact Support
                            </a>
                        </li>
                        <li>
                            <a href="<?= url('/client/faq') ?>" class="text-gray-600 hover:text-gray-900 text-sm">
                                FAQ
                            </a>
                        </li>
                        <li>
                            <a href="<?= url('/client/privacy') ?>" class="text-gray-600 hover:text-gray-900 text-sm">
                                Privacy Policy
                            </a>
                        </li>
                    </ul>
                </div>

            </div>

            <!-- Bottom Section -->
            <div class="border-t border-gray-200 pt-8 mt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-500 text-sm">
                        © <?= date('Y') ?> Document Management System. All rights reserved.
                    </div>
                    <div class="flex items-center space-x-6 mt-4 md:mt-0">
                        <span class="text-gray-500 text-sm">
                            Secure • Reliable • Professional
                        </span>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-green-600 text-sm font-medium">System Online</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="text-gray-700 font-medium">Loading...</span>
        </div>
    </div>

    <!-- Global JavaScript -->
    <script>
        // Show loading overlay
        function showLoading() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
        }

        // Hide loading overlay
        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Show confirmation dialog
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }

        // AJAX helper function
        function makeRequest(url, method = 'GET', data = null) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open(method, url);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                
                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('Network error'));
                };
                
                if (data) {
                    xhr.send(JSON.stringify(data));
                } else {
                    xhr.send();
                }
            });
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 max-w-sm bg-${type === 'error' ? 'red' : (type === 'warning' ? 'yellow' : type === 'success' ? 'green' : 'blue')}-100 border border-${type === 'error' ? 'red' : (type === 'warning' ? 'yellow' : type === 'success' ? 'green' : 'blue')}-400 text-${type === 'error' ? 'red' : (type === 'warning' ? 'yellow' : type === 'success' ? 'green' : 'blue')}-700 px-4 py-3 rounded-lg shadow-lg`;
            
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-${type === 'error' ? 'red' : (type === 'warning' ? 'yellow' : type === 'success' ? 'green' : 'blue')}-500 hover:text-${type === 'error' ? 'red' : (type === 'warning' ? 'yellow' : type === 'success' ? 'green' : 'blue')}-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Initialize tooltips and other UI enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Add loading states to forms
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function() {
                    const submitButton = form.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                    }
                });
            });

            // Add confirmation to delete actions
            document.querySelectorAll('[data-confirm]').forEach(element => {
                element.addEventListener('click', function(e) {
                    const message = this.getAttribute('data-confirm');
                    if (!confirm(message)) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>

</body>
</html>
