<?php
$title = $bundle['name'] ?? 'Bundle Details';
ob_start();
?>

<!-- Bundle Details -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Simple Page Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?= e($bundle['name']) ?></h1>
                <div class="flex items-center mt-2 space-x-4">
                    <span class="text-blue-600 font-mono text-sm"><?= e($bundle['reference_number']) ?></span>
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium
                        <?php
                        switch ($bundle['status']) {
                            case 'active': echo 'bg-green-100 text-green-800'; break;
                            case 'open': echo 'bg-blue-100 text-blue-800'; break;
                            case 'closed': echo 'bg-gray-100 text-gray-800'; break;
                            default: echo 'bg-gray-100 text-gray-800';
                        }
                        ?>">
                        <?= ucfirst($bundle['status']) ?>
                    </span>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="<?= url('/app/bundles') ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back
                </a>

                <!-- Generation Actions Dropdown -->
                <div class="relative inline-block text-left">
                    <button type="button" onclick="toggleGenerateMenu()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Generate
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <div id="generateMenu" class="hidden absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                        <div class="py-1">
                            <button onclick="generateBarcode()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                </svg>
                                Generate Barcode
                            </button>
                            <button onclick="generateQRCode()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                Generate QR Code
                            </button>
                            <button onclick="generateLabel()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Generate Label
                            </button>
                            <button onclick="printBundleInfo()" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                                Print Bundle Info
                            </button>
                        </div>
                    </div>
                </div>

                <a href="<?= url('/app/documents/create?bundle_id=' . $bundle['id']) ?>" class="inline-flex items-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Upload Documents
                </a>

                <a href="<?= url('/app/bundles/' . $bundle['id'] . '/edit') ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                </a>
            </div>
        </div>
    </div>

    <!-- Bundle Information -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">Bundle Information</h2>
            <div class="flex items-center space-x-3">
                <?php if (!empty($boxes)): ?>
                    <a href="<?= url('/app/boxes/' . $boxes[0]['id']) ?>"
                       class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        View Box
                    </a>
                <?php else: ?>
                    <a href="<?= url('/app/boxes?assign_bundle=' . $bundle['id']) ?>"
                       class="inline-flex items-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Assign to Box
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full">
                <tbody class="divide-y divide-gray-200">
                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500 w-1/4">Category</td>
                        <td class="px-4 py-3">
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium bg-blue-100 text-blue-800">
                                <?= ucfirst($bundle['category']) ?>
                            </span>
                        </td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500 w-1/4">Priority</td>
                        <td class="px-4 py-3">
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium
                                <?php
                                switch ($bundle['priority']) {
                                    case 'urgent': echo 'bg-red-100 text-red-800'; break;
                                    case 'high': echo 'bg-orange-100 text-orange-800'; break;
                                    case 'medium': echo 'bg-yellow-100 text-yellow-800'; break;
                                    case 'low': echo 'bg-green-100 text-green-800'; break;
                                }
                                ?>">
                                <?= ucfirst($bundle['priority']) ?>
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Retention Period</td>
                        <td class="px-4 py-3 text-sm text-gray-900"><?= $bundle['retention_period'] ?> years</td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Access Level</td>
                        <td class="px-4 py-3">
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium
                                <?php
                                switch ($bundle['access_level'] ?? 'public') {
                                    case 'public': echo 'bg-green-100 text-green-800'; break;
                                    case 'private': echo 'bg-yellow-100 text-yellow-800'; break;
                                    case 'restricted': echo 'bg-red-100 text-red-800'; break;
                                    default: echo 'bg-gray-100 text-gray-800';
                                }
                                ?>">
                                <?= ucfirst($bundle['access_level'] ?? 'Public') ?>
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Current Document Count</td>
                        <td class="px-4 py-3">
                            <span class="text-sm font-medium text-blue-600"><?= number_format($stats['total_documents'] ?? 0) ?> documents</span>
                        </td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Storage Boxes</td>
                        <td class="px-4 py-3">
                            <span class="text-sm font-medium text-green-600"><?= count($boxes) ?> boxes</span>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Company</td>
                        <td class="px-4 py-3 text-sm text-gray-900"><?= e($bundle['company_name'] ?? 'Unknown') ?></td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500"></td>
                        <td class="px-4 py-3"></td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Created Date</td>
                        <td class="px-4 py-3 text-sm text-gray-900"><?= date('M j, Y g:i A', strtotime($bundle['created_at'])) ?></td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Last Updated</td>
                        <td class="px-4 py-3 text-sm text-gray-900"><?= date('M j, Y g:i A', strtotime($bundle['updated_at'])) ?></td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Created By</td>
                        <td class="px-4 py-3 text-sm text-gray-900">
                            <?php if (!empty($bundle['first_name']) || !empty($bundle['last_name'])): ?>
                                <?= e(trim($bundle['first_name'] . ' ' . $bundle['last_name'])) ?>
                            <?php else: ?>
                                Unknown User
                            <?php endif; ?>
                        </td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Workflow Status</td>
                        <td class="px-4 py-3">
                            <?php
                            // Determine workflow status based on bundle state
                            $workflowStatus = 'Created';
                            $statusColor = 'bg-blue-100 text-blue-800';

                            if (count($documents) > 0) {
                                $workflowStatus = 'Has Documents';
                                $statusColor = 'bg-yellow-100 text-yellow-800';
                            }

                            if (!empty($boxes)) {
                                $workflowStatus = 'Assigned to Box';
                                $statusColor = 'bg-green-100 text-green-800';
                            }

                            if ($bundle['status'] === 'archived') {
                                $workflowStatus = 'Archived';
                                $statusColor = 'bg-gray-100 text-gray-800';
                            }
                            ?>
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium <?= $statusColor ?>">
                                <?= $workflowStatus ?>
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Bundle Status</td>
                        <td class="px-4 py-3">
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium
                                <?php
                                switch ($bundle['status']) {
                                    case 'active': echo 'bg-green-100 text-green-800'; break;
                                    case 'inactive': echo 'bg-yellow-100 text-yellow-800'; break;
                                    case 'archived': echo 'bg-gray-100 text-gray-800'; break;
                                    default: echo 'bg-gray-100 text-gray-800';
                                }
                                ?>">
                                <?= ucfirst($bundle['status']) ?>
                            </span>
                        </td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Next Action</td>
                        <td class="px-4 py-3 text-sm text-gray-600">
                            <?php
                            if (count($documents) === 0) {
                                echo 'Upload documents';
                            } elseif (empty($boxes)) {
                                echo 'Assign to storage box';
                            } elseif ($bundle['status'] === 'active') {
                                echo 'Bundle ready for use';
                            } else {
                                echo 'No action needed';
                            }
                            ?>
                        </td>
                    </tr>

                    <?php if (!empty($bundle['description'])): ?>
                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500 align-top">Description</td>
                        <td colspan="3" class="px-4 py-3 text-sm text-gray-700"><?= e($bundle['description']) ?></td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Intake Information -->
    <?php if ($intake): ?>
    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">Original Intake Information</h2>
            <a href="<?= url('/app/intake/' . $intake['id']) ?>"
               class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Full Intake
            </a>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full">
                <tbody class="divide-y divide-gray-200">
                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500 w-1/4">Intake Reference</td>
                        <td class="px-4 py-3">
                            <span class="text-sm font-medium text-blue-600 font-mono"><?= e($intake['reference_number']) ?></span>
                        </td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500 w-1/4">Client Name</td>
                        <td class="px-4 py-3 text-sm text-gray-900"><?= e($intake['client_name']) ?></td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Source</td>
                        <td class="px-4 py-3">
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium bg-gray-100 text-gray-800">
                                <?= ucfirst(str_replace('_', ' ', $intake['source'])) ?>
                            </span>
                        </td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Document Type</td>
                        <td class="px-4 py-3 text-sm text-gray-900"><?= e($intake['document_type']) ?></td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Expected Count</td>
                        <td class="px-4 py-3">
                            <span class="text-sm font-medium text-orange-600"><?= number_format($intake['expected_count']) ?> documents</span>
                        </td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Actual Count</td>
                        <td class="px-4 py-3">
                            <span class="text-sm font-medium <?= $intake['actual_count'] == $intake['expected_count'] ? 'text-green-600' : 'text-red-600' ?>">
                                <?= number_format($intake['actual_count']) ?> documents
                                <?php if ($intake['actual_count'] != $intake['expected_count']): ?>
                                    <span class="text-xs text-gray-500 ml-1">(Mismatch!)</span>
                                <?php endif; ?>
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Intake Priority</td>
                        <td class="px-4 py-3">
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium
                                <?php
                                switch ($intake['priority']) {
                                    case 'urgent': echo 'bg-red-100 text-red-800'; break;
                                    case 'high': echo 'bg-orange-100 text-orange-800'; break;
                                    case 'medium': echo 'bg-yellow-100 text-yellow-800'; break;
                                    case 'low': echo 'bg-green-100 text-green-800'; break;
                                }
                                ?>">
                                <?= ucfirst($intake['priority']) ?>
                            </span>
                        </td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Intake Status</td>
                        <td class="px-4 py-3">
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium
                                <?php
                                switch ($intake['status']) {
                                    case 'completed': echo 'bg-green-100 text-green-800'; break;
                                    case 'processing': echo 'bg-blue-100 text-blue-800'; break;
                                    case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                    case 'cancelled': echo 'bg-red-100 text-red-800'; break;
                                }
                                ?>">
                                <?= ucfirst($intake['status']) ?>
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Intake Created</td>
                        <td class="px-4 py-3 text-sm text-gray-900"><?= date('M j, Y g:i A', strtotime($intake['created_at'])) ?></td>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500">Created By</td>
                        <td class="px-4 py-3 text-sm text-gray-900">
                            <?php if (!empty($intake['created_by_first_name']) || !empty($intake['created_by_last_name'])): ?>
                                <?= e(trim($intake['created_by_first_name'] . ' ' . $intake['created_by_last_name'])) ?>
                            <?php else: ?>
                                Unknown User
                            <?php endif; ?>
                        </td>
                    </tr>

                    <?php if (!empty($intake['description'])): ?>
                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500 align-top">Intake Description</td>
                        <td colspan="3" class="px-4 py-3 text-sm text-gray-700"><?= e($intake['description']) ?></td>
                    </tr>
                    <?php endif; ?>

                    <?php if (!empty($intake['notes'])): ?>
                    <tr>
                        <td class="px-4 py-3 text-sm font-medium text-gray-500 align-top">Intake Notes</td>
                        <td colspan="3" class="px-4 py-3 text-sm text-gray-700"><?= e($intake['notes']) ?></td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>

    <!-- Files in Bundle -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">Files in Bundle (<?= count($documents) ?>)</h2>
            <a href="<?= url('/app/documents/create?bundle_id=' . $bundle['id']) ?>"
               class="inline-flex items-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Upload
            </a>
        </div>

        <?php if (empty($documents)): ?>
            <div class="text-center py-8">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-sm font-medium text-gray-900 mb-1">No files yet</h3>
                <p class="text-sm text-gray-600">This bundle is ready for files. Start by uploading your first document.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">File</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Storage Location</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Size</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Added</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($documents as $document): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?= e($document['title']) ?></div>
                                        <div class="text-sm text-gray-500"><?= e($document['file_name']) ?></div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                        <?= ucfirst($document['document_type']) ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    <?php if ($document['location_name']): ?>
                                        <div>
                                            <div class="text-sm text-gray-900"><?= e($document['location_name']) ?></div>
                                            <div class="text-xs text-gray-500"><?= e($document['warehouse_name']) ?></div>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-gray-400 text-sm">No location</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-600">
                                    <?= formatFileSize($document['file_size']) ?>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-600">
                                    <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                </td>
                                <td class="px-4 py-3">
                                    <a href="<?= url('/app/documents/' . $document['id']) ?>"
                                       class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                        View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

</div>

<script>
// Bundle management functions
function removeBundleFromBox(bundleId, boxId) {
    if (confirm('Are you sure you want to remove this bundle from the box? This will not delete the bundle or its files.')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= url('/app/bundles/') ?>${bundleId}/remove-from-box`;

        const boxIdInput = document.createElement('input');
        boxIdInput.type = 'hidden';
        boxIdInput.name = 'box_id';
        boxIdInput.value = boxId;

        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = '<?= $_SESSION['csrf_token'] ?? '' ?>';

        form.appendChild(boxIdInput);
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Generation menu functions
function toggleGenerateMenu() {
    const menu = document.getElementById('generateMenu');
    menu.classList.toggle('hidden');
}

// Close menu when clicking outside
document.addEventListener('click', function(event) {
    const menu = document.getElementById('generateMenu');
    const button = event.target.closest('button');

    if (!button || !button.onclick || button.onclick.toString().indexOf('toggleGenerateMenu') === -1) {
        menu.classList.add('hidden');
    }
});

// Generation functions
function generateBarcode() {
    const bundleRef = '<?= e($bundle['reference_number']) ?>';
    const bundleName = '<?= e($bundle['name']) ?>';

    // Create a new window with barcode generation
    const barcodeWindow = window.open('', '_blank', 'width=600,height=400');
    barcodeWindow.document.write(`
        <html>
            <head>
                <title>Bundle Barcode - ` + bundleRef + `</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                    .barcode-container { margin: 20px 0; }
                    .bundle-info { margin-bottom: 20px; }
                    .print-btn { background: #3B82F6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
                </style>
            </head>
            <body>
                <div class="bundle-info">
                    <h2>Bundle Barcode</h2>
                    <p><strong>Reference:</strong> ` + bundleRef + `</p>
                    <p><strong>Name:</strong> ` + bundleName + `</p>
                </div>
                <div class="barcode-container">
                    <svg id="barcode"></svg>
                </div>
                <button class="print-btn" onclick="window.print()">Print Barcode</button>
                <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"><\/script>
                <script>
                    JsBarcode("#barcode", "` + bundleRef + `", {
                        format: "CODE128",
                        width: 2,
                        height: 100,
                        displayValue: true
                    });
                <\/script>
            </body>
        </html>
    `);

    toggleGenerateMenu();
}

function generateQRCode() {
    const bundleRef = '<?= e($bundle['reference_number']) ?>';
    const bundleName = '<?= e($bundle['name']) ?>';
    const bundleUrl = window.location.href;

    // Create QR code data
    const qrData = JSON.stringify({
        type: 'bundle',
        reference: bundleRef,
        name: bundleName,
        url: bundleUrl,
        id: <?= $bundle['id'] ?>
    });

    // Create a new window with QR code generation
    const qrWindow = window.open('', '_blank', 'width=600,height=500');
    qrWindow.document.write(`
        <html>
            <head>
                <title>Bundle QR Code - ` + bundleRef + `</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                    .qr-container { margin: 20px 0; }
                    .bundle-info { margin-bottom: 20px; }
                    .print-btn { background: #3B82F6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
                </style>
            </head>
            <body>
                <div class="bundle-info">
                    <h2>Bundle QR Code</h2>
                    <p><strong>Reference:</strong> ` + bundleRef + `</p>
                    <p><strong>Name:</strong> ` + bundleName + `</p>
                </div>
                <div class="qr-container">
                    <div id="qrcode"></div>
                </div>
                <button class="print-btn" onclick="window.print()">Print QR Code</button>
                <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"><\/script>
                <script>
                    QRCode.toCanvas(document.getElementById('qrcode'), ` + JSON.stringify(qrData) + `, {
                        width: 256,
                        margin: 2
                    });
                <\/script>
            </body>
        </html>
    `);

    toggleGenerateMenu();
}

function generateLabel() {
    const bundleData = {
        reference: '<?= e($bundle['reference_number']) ?>',
        name: '<?= e($bundle['name']) ?>',
        category: '<?= e($bundle['category']) ?>',
        priority: '<?= e($bundle['priority']) ?>',
        company: '<?= e($bundle['company_name'] ?? '') ?>',
        created: '<?= date('M j, Y', strtotime($bundle['created_at'])) ?>',
        files: '<?= $stats['total_documents'] ?? 0 ?>',
        size: '<?= formatFileSize($stats['total_size'] ?? 0) ?>'
    };

    // Create a new window with label generation
    const labelWindow = window.open('', '_blank', 'width=800,height=600');
    labelWindow.document.write(`
        <html>
            <head>
                <title>Bundle Label - ` + bundleData.reference + `</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    .label { border: 2px solid #000; padding: 15px; margin: 20px 0; max-width: 400px; }
                    .label-header { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
                    .label-row { margin: 5px 0; }
                    .label-row strong { display: inline-block; width: 80px; }
                    .print-btn { background: #3B82F6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 0; }
                    @media print { .print-btn { display: none; } }
                </style>
            </head>
            <body>
                <button class="print-btn" onclick="window.print()">Print Label</button>
                <div class="label">
                    <div class="label-header">` + bundleData.reference + `</div>
                    <div class="label-row"><strong>Name:</strong> ` + bundleData.name + `</div>
                    <div class="label-row"><strong>Category:</strong> ` + bundleData.category + `</div>
                    <div class="label-row"><strong>Priority:</strong> ` + bundleData.priority + `</div>
                    <div class="label-row"><strong>Company:</strong> ` + bundleData.company + `</div>
                    <div class="label-row"><strong>Created:</strong> ` + bundleData.created + `</div>
                    <div class="label-row"><strong>Files:</strong> ` + bundleData.files + `</div>
                    <div class="label-row"><strong>Size:</strong> ` + bundleData.size + `</div>
                </div>
            </body>
        </html>
    `);

    toggleGenerateMenu();
}

function printBundleInfo() {
    window.print();
    toggleGenerateMenu();
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
