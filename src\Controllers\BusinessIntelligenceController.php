<?php

namespace App\Controllers;

/**
 * Advanced Business Intelligence Controller
 * 
 * Comprehensive analytics, reporting, and predictive insights for super admin
 * Provides revenue analytics, client insights, and operational intelligence
 */
class BusinessIntelligenceController extends BaseController
{
    /**
     * Business Intelligence Dashboard
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get business overview metrics
            $businessOverview = $this->getBusinessOverview();
            
            // Get revenue analytics
            $revenueAnalytics = $this->getRevenueAnalytics();
            
            // Get client insights
            $clientInsights = $this->getClientInsights();
            
            // Get operational metrics
            $operationalMetrics = $this->getOperationalMetrics();
            
            // Get predictive insights
            $predictiveInsights = $this->getPredictiveInsights();

            $this->view('super-admin/business-intelligence', [
                'title' => 'Business Intelligence Dashboard',
                'businessOverview' => $businessOverview,
                'revenueAnalytics' => $revenueAnalytics,
                'clientInsights' => $clientInsights,
                'operationalMetrics' => $operationalMetrics,
                'predictiveInsights' => $predictiveInsights
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading business intelligence: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/dashboard');
        }
    }

    /**
     * Revenue Analytics Dashboard
     */
    public function revenue()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get detailed revenue analytics
            $revenueByPeriod = $this->getRevenueByPeriod();
            $revenueByClient = $this->getRevenueByClient();
            $revenueByService = $this->getRevenueByService();
            $revenueForecasts = $this->getRevenueForecasts();
            $profitabilityAnalysis = $this->getProfitabilityAnalysis();

            $this->view('super-admin/revenue-analytics', [
                'title' => 'Revenue Analytics',
                'revenueByPeriod' => $revenueByPeriod,
                'revenueByClient' => $revenueByClient,
                'revenueByService' => $revenueByService,
                'revenueForecasts' => $revenueForecasts,
                'profitabilityAnalysis' => $profitabilityAnalysis
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading revenue analytics: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/business-intelligence');
        }
    }

    /**
     * Client Analytics Dashboard
     */
    public function clients()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get client analytics
            $clientGrowth = $this->getClientGrowthAnalytics();
            $clientSegmentation = $this->getClientSegmentation();
            $clientRetention = $this->getClientRetentionAnalytics();
            $clientSatisfaction = $this->getClientSatisfactionMetrics();
            $clientLifetimeValue = $this->getClientLifetimeValue();

            $this->view('super-admin/client-analytics', [
                'title' => 'Client Analytics',
                'clientGrowth' => $clientGrowth,
                'clientSegmentation' => $clientSegmentation,
                'clientRetention' => $clientRetention,
                'clientSatisfaction' => $clientSatisfaction,
                'clientLifetimeValue' => $clientLifetimeValue
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading client analytics: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/business-intelligence');
        }
    }

    /**
     * Operational Intelligence Dashboard
     */
    public function operations()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get operational intelligence
            $efficiencyMetrics = $this->getOperationalEfficiencyMetrics();
            $capacityAnalysis = $this->getCapacityAnalysis();
            $resourceUtilization = $this->getResourceUtilizationAnalytics();
            $processOptimization = $this->getProcessOptimizationInsights();
            $costAnalysis = $this->getCostAnalysis();

            $this->view('super-admin/operational-intelligence', [
                'title' => 'Operational Intelligence',
                'efficiencyMetrics' => $efficiencyMetrics,
                'capacityAnalysis' => $capacityAnalysis,
                'resourceUtilization' => $resourceUtilization,
                'processOptimization' => $processOptimization,
                'costAnalysis' => $costAnalysis
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading operational intelligence: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/business-intelligence');
        }
    }

    /**
     * Predictive Analytics Dashboard
     */
    public function predictive()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get predictive analytics
            $demandForecasting = $this->getDemandForecasting();
            $capacityPredictions = $this->getCapacityPredictions();
            $churnPrediction = $this->getChurnPrediction();
            $growthProjections = $this->getGrowthProjections();
            $riskAssessment = $this->getRiskAssessment();

            $this->view('super-admin/predictive-analytics', [
                'title' => 'Predictive Analytics',
                'demandForecasting' => $demandForecasting,
                'capacityPredictions' => $capacityPredictions,
                'churnPrediction' => $churnPrediction,
                'growthProjections' => $growthProjections,
                'riskAssessment' => $riskAssessment
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading predictive analytics: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/business-intelligence');
        }
    }

    /**
     * Get business overview metrics
     */
    private function getBusinessOverview()
    {
        return $this->db->fetch(
            "SELECT 
                COUNT(DISTINCT c.id) as total_clients,
                COUNT(DISTINCT CASE WHEN c.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN c.id END) as new_clients_30d,
                COUNT(DISTINCT d.id) as total_documents,
                COUNT(DISTINCT CASE WHEN d.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN d.id END) as new_documents_30d,
                SUM(c.storage_used) as total_storage_used,
                AVG(c.storage_used / c.storage_limit * 100) as avg_storage_utilization,
                COUNT(DISTINCT b.id) as total_boxes,
                COUNT(DISTINCT bun.id) as total_bundles,
                COUNT(DISTINCT w.id) as total_warehouses
             FROM companies c
             LEFT JOIN documents d ON c.id = d.company_id AND d.status != 'deleted'
             LEFT JOIN boxes b ON c.id = b.company_id
             LEFT JOIN bundles bun ON c.id = bun.company_id AND bun.status = 'active'
             LEFT JOIN warehouses w ON w.status = 'active'
             WHERE c.status = 'active'",
            []
        );
    }

    /**
     * Get revenue analytics
     */
    private function getRevenueAnalytics()
    {
        // Calculate revenue based on storage usage and subscription plans
        return $this->db->fetchAll(
            "SELECT 
                c.subscription_plan,
                COUNT(c.id) as client_count,
                SUM(
                    CASE 
                        WHEN c.subscription_plan = 'basic' THEN 29.99
                        WHEN c.subscription_plan = 'premium' THEN 79.99
                        WHEN c.subscription_plan = 'enterprise' THEN 199.99
                        ELSE 0
                    END
                ) as monthly_revenue,
                AVG(c.storage_used) as avg_storage_used,
                SUM(c.storage_used) as total_storage_used
             FROM companies c
             WHERE c.status = 'active'
             GROUP BY c.subscription_plan
             ORDER BY monthly_revenue DESC",
            []
        );
    }

    /**
     * Get client insights
     */
    private function getClientInsights()
    {
        return $this->db->fetchAll(
            "SELECT 
                c.id,
                c.name,
                c.subscription_plan,
                c.storage_used,
                c.storage_limit,
                (c.storage_used / c.storage_limit * 100) as storage_percentage,
                COUNT(DISTINCT d.id) as document_count,
                COUNT(DISTINCT b.id) as box_count,
                COUNT(DISTINCT bun.id) as bundle_count,
                COUNT(DISTINCT u.id) as user_count,
                DATEDIFF(CURDATE(), c.created_at) as days_as_client,
                MAX(d.created_at) as last_document_upload
             FROM companies c
             LEFT JOIN documents d ON c.id = d.company_id AND d.status != 'deleted'
             LEFT JOIN boxes b ON c.id = b.company_id
             LEFT JOIN bundles bun ON c.id = bun.company_id AND bun.status = 'active'
             LEFT JOIN users u ON c.id = u.company_id AND u.status = 'active'
             WHERE c.status = 'active'
             GROUP BY c.id
             ORDER BY storage_percentage DESC, document_count DESC
             LIMIT 20",
            []
        );
    }

    /**
     * Get operational metrics
     */
    private function getOperationalMetrics()
    {
        return $this->db->fetch(
            "SELECT 
                COUNT(DISTINCT CASE WHEN DATE(d.created_at) = CURDATE() THEN d.id END) as documents_processed_today,
                COUNT(DISTINCT CASE WHEN DATE(d.created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN d.id END) as documents_processed_7d,
                COUNT(DISTINCT CASE WHEN DATE(d.created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN d.id END) as documents_processed_30d,
                AVG(TIMESTAMPDIFF(HOUR, d.created_at, d.updated_at)) as avg_processing_time_hours,
                COUNT(DISTINCT CASE WHEN DATE(bm.movement_date) = CURDATE() THEN bm.id END) as box_movements_today,
                COUNT(DISTINCT CASE WHEN DATE(bc.generated_at) = CURDATE() THEN bc.id END) as barcodes_generated_today,
                COUNT(DISTINCT CASE WHEN DATE(bat.created_at) = CURDATE() THEN bat.id END) as barcode_scans_today
             FROM documents d
             LEFT JOIN box_movements bm ON DATE(bm.movement_date) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
             LEFT JOIN barcodes bc ON DATE(bc.generated_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
             LEFT JOIN barcode_audit_trail bat ON DATE(bat.created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
             WHERE d.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)",
            []
        );
    }

    /**
     * Get predictive insights
     */
    private function getPredictiveInsights()
    {
        // Simple predictive analytics based on trends
        $growthRate = $this->calculateGrowthRate();
        $capacityProjection = $this->calculateCapacityProjection();
        $revenueProjection = $this->calculateRevenueProjection();

        return [
            'growth_rate' => $growthRate,
            'capacity_projection' => $capacityProjection,
            'revenue_projection' => $revenueProjection,
            'recommendations' => $this->generateRecommendations($growthRate, $capacityProjection)
        ];
    }

    /**
     * Calculate growth rate
     */
    private function calculateGrowthRate()
    {
        $currentMonth = $this->db->fetch(
            "SELECT COUNT(*) as count FROM documents WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())",
            []
        );
        
        $previousMonth = $this->db->fetch(
            "SELECT COUNT(*) as count FROM documents WHERE MONTH(created_at) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) AND YEAR(created_at) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))",
            []
        );

        if ($previousMonth['count'] > 0) {
            return (($currentMonth['count'] - $previousMonth['count']) / $previousMonth['count']) * 100;
        }
        
        return 0;
    }

    /**
     * Calculate capacity projection
     */
    private function calculateCapacityProjection()
    {
        $currentCapacity = $this->db->fetch(
            "SELECT AVG(storage_used / storage_limit * 100) as avg_usage FROM companies WHERE status = 'active'",
            []
        );

        $growthRate = $this->calculateGrowthRate();
        
        // Project capacity usage for next 6 months
        $projectedUsage = $currentCapacity['avg_usage'] * (1 + ($growthRate / 100) * 6);
        
        return [
            'current_usage' => round($currentCapacity['avg_usage'], 2),
            'projected_usage_6m' => round($projectedUsage, 2),
            'capacity_risk' => $projectedUsage > 80 ? 'high' : ($projectedUsage > 60 ? 'medium' : 'low')
        ];
    }

    /**
     * Calculate revenue projection
     */
    private function calculateRevenueProjection()
    {
        $currentRevenue = $this->db->fetch(
            "SELECT SUM(
                CASE 
                    WHEN subscription_plan = 'basic' THEN 29.99
                    WHEN subscription_plan = 'premium' THEN 79.99
                    WHEN subscription_plan = 'enterprise' THEN 199.99
                    ELSE 0
                END
            ) as monthly_revenue FROM companies WHERE status = 'active'",
            []
        );

        $growthRate = $this->calculateGrowthRate();
        
        return [
            'current_monthly' => round($currentRevenue['monthly_revenue'], 2),
            'projected_6m' => round($currentRevenue['monthly_revenue'] * (1 + ($growthRate / 100) * 6), 2),
            'projected_12m' => round($currentRevenue['monthly_revenue'] * (1 + ($growthRate / 100) * 12), 2)
        ];
    }

    /**
     * Generate recommendations
     */
    private function generateRecommendations($growthRate, $capacityProjection)
    {
        $recommendations = [];

        if ($growthRate > 20) {
            $recommendations[] = [
                'type' => 'growth',
                'priority' => 'high',
                'title' => 'High Growth Detected',
                'description' => 'Consider expanding warehouse capacity and hiring additional staff.',
                'action' => 'Plan capacity expansion'
            ];
        }

        if ($capacityProjection['capacity_risk'] === 'high') {
            $recommendations[] = [
                'type' => 'capacity',
                'priority' => 'critical',
                'title' => 'Capacity Risk',
                'description' => 'Storage capacity will reach critical levels within 6 months.',
                'action' => 'Immediate capacity planning required'
            ];
        }

        if (empty($recommendations)) {
            $recommendations[] = [
                'type' => 'optimization',
                'priority' => 'low',
                'title' => 'System Optimization',
                'description' => 'System is running efficiently. Consider process improvements.',
                'action' => 'Review operational efficiency'
            ];
        }

        return $recommendations;
    }

    // Additional helper methods for detailed analytics
    private function getRevenueByPeriod() { return []; }
    private function getRevenueByClient() { return []; }
    private function getRevenueByService() { return []; }
    private function getRevenueForecasts() { return []; }
    private function getProfitabilityAnalysis() { return []; }
    private function getClientGrowthAnalytics() { return []; }
    private function getClientSegmentation() { return []; }
    private function getClientRetentionAnalytics() { return []; }
    private function getClientSatisfactionMetrics() { return []; }
    private function getClientLifetimeValue() { return []; }
    private function getOperationalEfficiencyMetrics() { return []; }
    private function getCapacityAnalysis() { return []; }
    private function getResourceUtilizationAnalytics() { return []; }
    private function getProcessOptimizationInsights() { return []; }
    private function getCostAnalysis() { return []; }
    private function getDemandForecasting() { return []; }
    private function getCapacityPredictions() { return []; }
    private function getChurnPrediction() { return []; }
    private function getGrowthProjections() { return []; }
    private function getRiskAssessment() { return []; }
}
