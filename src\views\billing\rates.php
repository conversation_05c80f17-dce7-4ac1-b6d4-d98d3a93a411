<?php
$title = 'Service Rates';
$currentPage = 'billing';
include __DIR__ . '/../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="<?= url('/app/billing') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Billing
                    </a>
                    <h1 class="h3 mb-0 d-inline">Service Rates</h1>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRateModal">
                        <i class="fas fa-plus"></i> Add New Rate
                    </button>
                </div>
            </div>

            <!-- Service Rates by Category -->
            <?php if (empty($rates_by_category)): ?>
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-list fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Service Rates Found</h5>
                        <p class="text-muted">Add your first service rate to get started with billing.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRateModal">
                            <i class="fas fa-plus"></i> Add Service Rate
                        </button>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($rates_by_category as $category => $rates): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-<?= getCategoryIcon($category) ?> me-2"></i>
                                <?= ucfirst(str_replace('_', ' ', $category)) ?> Services
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Service Code</th>
                                            <th>Service Name</th>
                                            <th>Description</th>
                                            <th class="text-end">Rate</th>
                                            <th>Unit</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($rates as $rate): ?>
                                            <tr>
                                                <td>
                                                    <code class="bg-light px-2 py-1 rounded"><?= htmlspecialchars($rate['service_code']) ?></code>
                                                </td>
                                                <td>
                                                    <strong><?= htmlspecialchars($rate['service_name']) ?></strong>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?= htmlspecialchars($rate['service_description']) ?></small>
                                                </td>
                                                <td class="text-end">
                                                    <strong class="text-primary">$<?= number_format($rate['rate'], 2) ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?= htmlspecialchars($rate['unit']) ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($rate['is_active']): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary" 
                                                                onclick="editRate(<?= htmlspecialchars(json_encode($rate)) ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php if ($rate['is_active']): ?>
                                                            <button type="button" class="btn btn-outline-warning" 
                                                                    onclick="toggleRateStatus(<?= $rate['id'] ?>, false)">
                                                                <i class="fas fa-pause"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-success" 
                                                                    onclick="toggleRateStatus(<?= $rate['id'] ?>, true)">
                                                                <i class="fas fa-play"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add/Edit Rate Modal -->
<div class="modal fade" id="addRateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form id="rateForm" method="POST" action="/app/billing/rates/save">
                <div class="modal-header">
                    <h5 class="modal-title" id="rateModalTitle">Add New Service Rate</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="rate_id" name="rate_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="service_code" class="form-label">Service Code</label>
                                <input type="text" class="form-control" id="service_code" name="service_code" 
                                       placeholder="e.g., intake.new" required>
                                <div class="form-text">Use lowercase with dots for hierarchy (e.g., category.action)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select category...</option>
                                    <option value="processing">Processing</option>
                                    <option value="handling">Handling</option>
                                    <option value="search">Search</option>
                                    <option value="delivery">Delivery</option>
                                    <option value="storage">Storage</option>
                                    <option value="barcode">Barcode</option>
                                    <option value="supplies">Supplies</option>
                                    <option value="digital">Digital</option>
                                    <option value="special">Special</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="service_name" class="form-label">Service Name</label>
                        <input type="text" class="form-control" id="service_name" name="service_name" 
                               placeholder="e.g., New Document Intake" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="service_description" class="form-label">Description</label>
                        <textarea class="form-control" id="service_description" name="service_description" 
                                  rows="2" placeholder="Brief description of the service"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rate" class="form-label">Rate ($)</label>
                                <input type="number" class="form-control" id="rate" name="rate" 
                                       step="0.01" min="0" placeholder="0.00" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit" class="form-label">Unit</label>
                                <select class="form-select" id="unit" name="unit" required>
                                    <option value="each">Each</option>
                                    <option value="hour">Hour</option>
                                    <option value="day">Day</option>
                                    <option value="week">Week</option>
                                    <option value="month">Month</option>
                                    <option value="kg">Kilogram</option>
                                    <option value="page">Page</option>
                                    <option value="box">Box</option>
                                    <option value="bundle">Bundle</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                Active (available for billing)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Rate
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function getCategoryIcon(category) {
    const icons = {
        'processing': 'cogs',
        'handling': 'hand-paper',
        'search': 'search',
        'delivery': 'truck',
        'storage': 'warehouse',
        'barcode': 'barcode',
        'supplies': 'box',
        'digital': 'digital-tachograph',
        'special': 'star'
    };
    return icons[category] || 'cog';
}

function editRate(rate) {
    document.getElementById('rateModalTitle').textContent = 'Edit Service Rate';
    document.getElementById('rate_id').value = rate.id;
    document.getElementById('service_code').value = rate.service_code;
    document.getElementById('service_name').value = rate.service_name;
    document.getElementById('service_description').value = rate.service_description || '';
    document.getElementById('rate').value = rate.rate;
    document.getElementById('unit').value = rate.unit;
    document.getElementById('category').value = rate.category;
    document.getElementById('is_active').checked = rate.is_active == 1;
    
    const modal = new bootstrap.Modal(document.getElementById('addRateModal'));
    modal.show();
}

function toggleRateStatus(rateId, isActive) {
    const action = isActive ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this service rate?`)) {
        // Here you would make an AJAX call to update the rate status
        // For now, we'll just reload the page
        window.location.href = `<?= url('/app/billing/rates/toggle/') ?>${rateId}?active=${isActive ? 1 : 0}`;
    }
}

// Reset form when modal is closed
document.getElementById('addRateModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('rateForm').reset();
    document.getElementById('rate_id').value = '';
    document.getElementById('rateModalTitle').textContent = 'Add New Service Rate';
});
</script>

<?php
function getCategoryIcon($category) {
    $icons = [
        'processing' => 'cogs',
        'handling' => 'hand-paper',
        'search' => 'search',
        'delivery' => 'truck',
        'storage' => 'warehouse',
        'barcode' => 'barcode',
        'supplies' => 'box',
        'digital' => 'digital-tachograph',
        'special' => 'star'
    ];
    return $icons[$category] ?? 'cog';
}
?>

<?php include __DIR__ . '/../layouts/footer.php'; ?>
