<?php
$title = 'Box Management';
ob_start();
?>

<!-- Box Management -->
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Simple Page Header -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Box Management</h1>
                    <p class="text-gray-600 mt-1">Manage physical and digital storage containers</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/boxes/create') ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Box
                    </a>
                </div>
            </div>
        </div>

        <!-- Comprehensive Box Management System -->
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
            <!-- Archive Status Tabs -->
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-6 lg:space-y-0 mb-6">
                <div class="flex flex-wrap items-center space-x-1 bg-gray-100 rounded-xl p-1">
                    <a href="<?= url('/app/boxes?archive_status=active&date_range=' . ($filters['date_range'] ?? 'last_3_months')) ?>"
                       class="archive-tab px-4 py-2 rounded-lg text-sm font-medium transition-all <?= ($filters['archive_status'] ?? 'active') === 'active' ? 'bg-blue-600 text-white' : 'text-gray-700 hover:bg-gray-200' ?>">
                        📦 Active Boxes
                        <span class="ml-2 px-2 py-1 <?= ($filters['archive_status'] ?? 'active') === 'active' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700' ?> rounded-full text-xs">
                            <?= $archive_stats['active_count'] ?? 0 ?>
                        </span>
                    </a>
                    <a href="<?= url('/app/boxes?archive_status=archived&date_range=all') ?>"
                       class="archive-tab px-4 py-2 rounded-lg text-sm font-medium transition-all <?= ($filters['archive_status'] ?? 'active') === 'archived' ? 'bg-blue-600 text-white' : 'text-gray-700 hover:bg-gray-200' ?>">
                        📁 Archived
                        <span class="ml-2 px-2 py-1 <?= ($filters['archive_status'] ?? 'active') === 'archived' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700' ?> rounded-full text-xs">
                            <?= ($archive_stats['archived_count'] ?? 0) + ($archive_stats['auto_archived_count'] ?? 0) ?>
                        </span>
                    </a>
                </div>

                <!-- Results Summary -->
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <span>Showing <?= count($boxes) ?> of <?= $pagination['total_count'] ?> boxes</span>
                    <?php if (($filters['archive_status'] ?? 'active') === 'active'): ?>
                        <span class="text-blue-600">📅 <?= ucfirst(str_replace('_', ' ', $filters['date_range'] ?? 'last 3 months')) ?></span>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Date Range Quick Filters (only for active boxes) -->
            <?php if (($filters['archive_status'] ?? 'active') === 'active'): ?>
            <div class="flex flex-wrap items-center space-x-2 mb-6 pb-4 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-700 mr-2">📅 Date Range:</span>
                <?php
                $dateRanges = [
                    'today' => 'Today',
                    'last_7_days' => 'Last 7 days',
                    'last_30_days' => 'Last 30 days',
                    'last_3_months' => 'Last 3 months',
                    'last_6_months' => 'Last 6 months',
                    'last_year' => 'Last year',
                    'this_year' => 'This year',
                    'all' => 'All time'
                ];
                foreach ($dateRanges as $range => $label):
                    $isActive = ($filters['date_range'] ?? 'last_3_months') === $range;
                ?>
                    <a href="<?= url('/app/boxes?' . http_build_query(array_merge($filters, ['date_range' => $range, 'page' => 1]))) ?>"
                       class="px-3 py-1 rounded-lg text-xs font-medium transition-all <?= $isActive ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100' ?>">
                        <?= $label ?>
                    </a>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Storage Type & Advanced Filters -->
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 mb-6">
                <!-- Storage Type Filters -->
                <div class="flex flex-wrap items-center space-x-1 bg-gray-50 rounded-xl p-1">
                    <?php
                    $storageTypes = [
                        'all' => ['📦 All', 'gray'],
                        'physical' => ['📦 Physical', 'amber'],
                        'online' => ['☁️ Online', 'blue'],
                        'mixed' => ['🔄 Mixed', 'purple']
                    ];
                    foreach ($storageTypes as $type => $config):
                        $isActive = ($filters['storage_type'] ?? 'all') === $type || (empty($filters['storage_type']) && $type === 'all');
                        $colorClass = $isActive ? 'bg-blue-600 text-white' : 'text-gray-700 hover:bg-gray-200';
                    ?>
                        <a href="<?= url('/app/boxes?' . http_build_query(array_merge($filters, ['storage_type' => $type, 'page' => 1]))) ?>"
                           class="px-3 py-2 rounded-lg text-sm font-medium transition-all <?= $colorClass ?>">
                            <?= $config[0] ?>
                        </a>
                    <?php endforeach; ?>
                </div>

                <!-- Secondary Filters -->
                <div class="flex items-center space-x-2">
                    <form method="GET" class="flex items-center space-x-2">
                        <!-- Preserve existing filters -->
                        <input type="hidden" name="archive_status" value="<?= e($filters['archive_status'] ?? 'active') ?>">
                        <input type="hidden" name="date_range" value="<?= e($filters['date_range'] ?? 'last_3_months') ?>">
                        <input type="hidden" name="storage_type" value="<?= e($filters['storage_type'] ?? '') ?>">

                        <select name="warehouse_id" class="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Warehouses</option>
                            <?php foreach ($warehouses as $warehouse): ?>
                                <option value="<?= $warehouse['id'] ?>" <?= ($filters['warehouse_id'] ?? '') == $warehouse['id'] ? 'selected' : '' ?>>
                                    🏢 <?= e($warehouse['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <select name="status" class="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Status</option>
                            <option value="empty" <?= ($filters['status'] ?? '') === 'empty' ? 'selected' : '' ?>>📭 Empty</option>
                            <option value="partial" <?= ($filters['status'] ?? '') === 'partial' ? 'selected' : '' ?>>📦 Partial</option>
                            <option value="full" <?= ($filters['status'] ?? '') === 'full' ? 'selected' : '' ?>>📫 Full</option>
                        </select>

                        <button type="submit" class="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all text-sm">
                            Apply
                        </button>
                    </form>

                    <a href="<?= url('/app/boxes?archive_status=' . ($filters['archive_status'] ?? 'active') . '&date_range=' . ($filters['date_range'] ?? 'last_3_months')) ?>"
                       class="inline-flex items-center px-3 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-all text-sm">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear
                    </a>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
                <form method="GET" class="flex-1 max-w-lg">
                    <!-- Preserve filters -->
                    <input type="hidden" name="archive_status" value="<?= e($filters['archive_status'] ?? 'active') ?>">
                    <input type="hidden" name="date_range" value="<?= e($filters['date_range'] ?? 'last_3_months') ?>">
                    <input type="hidden" name="storage_type" value="<?= e($filters['storage_type'] ?? '') ?>">
                    <input type="hidden" name="warehouse_id" value="<?= e($filters['warehouse_id'] ?? '') ?>">
                    <input type="hidden" name="status" value="<?= e($filters['status'] ?? '') ?>">

                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text"
                               name="search"
                               value="<?= e($filters['search'] ?? '') ?>"
                               placeholder="Search boxes by ID, name, location, or warehouse..."
                               class="block w-full pl-10 pr-3 py-3 border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white transition-all">
                    </div>
                </form>

                <!-- Per Page Selector -->
                <form method="GET" class="flex items-center space-x-2">
                    <!-- Preserve filters -->
                    <input type="hidden" name="archive_status" value="<?= e($filters['archive_status'] ?? 'active') ?>">
                    <input type="hidden" name="date_range" value="<?= e($filters['date_range'] ?? 'last_3_months') ?>">
                    <input type="hidden" name="storage_type" value="<?= e($filters['storage_type'] ?? '') ?>">
                    <input type="hidden" name="warehouse_id" value="<?= e($filters['warehouse_id'] ?? '') ?>">
                    <input type="hidden" name="status" value="<?= e($filters['status'] ?? '') ?>">
                    <input type="hidden" name="search" value="<?= e($filters['search'] ?? '') ?>">

                    <label class="text-sm text-gray-600">Show:</label>
                    <select name="per_page" onchange="this.form.submit()" class="px-2 py-1 border border-gray-200 rounded text-sm">
                        <option value="10" <?= ($pagination['per_page'] ?? 25) == 10 ? 'selected' : '' ?>>10</option>
                        <option value="25" <?= ($pagination['per_page'] ?? 25) == 25 ? 'selected' : '' ?>>25</option>
                        <option value="50" <?= ($pagination['per_page'] ?? 25) == 50 ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= ($pagination['per_page'] ?? 25) == 100 ? 'selected' : '' ?>>100</option>
                    </select>
                </form>
            </div>
        </div>

        <!-- Boxes Table -->
        <?php if (empty($boxes)): ?>
            <div class="text-center py-16">
                <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No boxes found</h3>
                <p class="text-gray-600 mb-4">Create your first storage box to organize your documents.</p>
                <a href="<?= url('/app/boxes/create') ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create First Box
                </a>
            </div>
        <?php else: ?>
            <!-- Clean Table View -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="boxesTable">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Box Details</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warehouse & Location</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contents</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Capacity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($boxes as $box): ?>
                                <tr class="hover:bg-gray-50 transition-colors box-row"
                                    data-box-status="<?= e($box['status']) ?>"
                                    data-storage-type="<?= e($box['storage_type'] ?? 'physical') ?>"
                                    data-box-name="<?= e(strtolower($box['name'])) ?>"
                                    data-box-id="<?= e(strtolower($box['box_id'])) ?>"
                                    data-warehouse="<?= e(strtolower($box['warehouse_name'] ?? '')) ?>">
                                    <!-- Box Details -->
                                    <td class="px-6 py-4">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <a href="<?= url('/app/boxes/' . $box['id']) ?>" class="hover:text-blue-600">
                                                    <?= e($box['name']) ?>
                                                </a>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                ID: <?= e($box['box_id']) ?>
                                            </div>
                                            <?php if (!empty($box['storage_location_code'])): ?>
                                                <div class="text-xs text-blue-600 font-mono mt-1">
                                                    <?= e($box['storage_location_code']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>

                                    <!-- Warehouse & Location -->
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            <?= e($box['warehouse_name']) ?>
                                        </div>
                                        <?php if (!empty($box['warehouse_location'])): ?>
                                            <div class="text-sm text-gray-500">
                                                <?= e($box['warehouse_location']) ?>
                                            </div>
                                        <?php endif; ?>
                                        <div class="mt-2">
                                            <?php $storageType = $box['storage_type'] ?? 'physical'; ?>
                                            <?php if ($storageType === 'physical'): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                                    📦 Physical Storage
                                                </span>
                                            <?php elseif ($storageType === 'online'): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    ☁️ Online Storage
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                    🔄 Mixed Storage
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </td>

                                    <!-- Contents -->
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            <div class="flex items-center space-x-4">
                                                <span class="text-blue-600 font-medium"><?= number_format($box['document_count']) ?> files</span>
                                                <span class="text-green-600 font-medium"><?= number_format($box['bundle_count']) ?> bundles</span>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">
                                                <?= formatBytes($box['total_size'] ?? 0) ?>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Capacity -->
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            <?php
                                            $utilization = $box['capacity'] > 0 ? round(($box['document_count'] / $box['capacity']) * 100, 1) : 0;
                                            ?>
                                            <div class="flex items-center">
                                                <span class="text-sm font-medium"><?= $utilization ?>%</span>
                                                <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                                    <div class="h-2 rounded-full <?php
                                                        if ($utilization >= 90) echo 'bg-red-500';
                                                        elseif ($utilization >= 75) echo 'bg-yellow-500';
                                                        else echo 'bg-green-500';
                                                        ?>"
                                                         style="width: <?= min(100, $utilization) ?>%"></div>
                                                </div>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">
                                                <?= $box['document_count'] ?> / <?= $box['capacity'] ?>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Created -->
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            <?= date('M j, Y', strtotime($box['created_at'])) ?>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            by <?= e($box['first_name'] . ' ' . $box['last_name']) ?>
                                        </div>
                                    </td>

                                    <!-- Actions -->
                                    <td class="px-6 py-4">
                                        <div class="flex items-center space-x-2">
                                            <a href="<?= url('/app/boxes/' . $box['id']) ?>"
                                               class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                                View
                                            </a>
                                            <a href="<?= url('/app/boxes/' . $box['id'] . '/edit') ?>"
                                               class="text-gray-600 hover:text-gray-700 text-sm">
                                                Edit
                                            </a>
                                            <?php if (!empty($box['barcode_value'])): ?>
                                                <button onclick="alert('Print barcode: <?= e($box['barcode_value']) ?>')"
                                                       class="text-green-600 hover:text-green-700 text-sm">
                                                    Print
                                                </button>
                                            <?php endif; ?>
                                            <?php if (($filters['archive_status'] ?? 'active') === 'active'): ?>
                                                <a href="<?= url('/app/boxes/' . $box['id'] . '/archive') ?>"
                                                   onclick="return confirm('Archive this box? It will be moved to archived boxes.')"
                                                   class="text-orange-600 hover:text-orange-700 text-sm">
                                                    Archive
                                                </a>
                                            <?php else: ?>
                                                <a href="<?= url('/app/boxes/' . $box['id'] . '/unarchive') ?>"
                                                   onclick="return confirm('Unarchive this box? It will be moved back to active boxes.')"
                                                   class="text-green-600 hover:text-green-700 text-sm">
                                                    Unarchive
                                                </a>
                                            <?php endif; ?>
                                            <button onclick="showDeleteBoxModal(<?= $box['id'] ?>, '<?= e($box['name']) ?>', <?= $box['bundle_count'] ?? 0 ?>)"
                                                    class="text-red-600 hover:text-red-700 text-sm">
                                                Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
            <!-- Pagination Controls -->
            <?php if ($pagination['total_pages'] > 1): ?>
            <div class="bg-white px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="flex items-center text-sm text-gray-700">
                    <span>
                        Showing <?= (($pagination['current_page'] - 1) * $pagination['per_page']) + 1 ?> to
                        <?= min($pagination['current_page'] * $pagination['per_page'], $pagination['total_count']) ?> of
                        <?= $pagination['total_count'] ?> results
                    </span>
                </div>

                <div class="flex items-center space-x-2">
                    <!-- Previous Page -->
                    <?php if ($pagination['has_prev']): ?>
                        <a href="<?= url('/app/boxes?' . http_build_query(array_merge($filters, ['page' => $pagination['current_page'] - 1]))) ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Previous
                        </a>
                    <?php endif; ?>

                    <!-- Page Numbers -->
                    <?php
                    $startPage = max(1, $pagination['current_page'] - 2);
                    $endPage = min($pagination['total_pages'], $pagination['current_page'] + 2);

                    if ($startPage > 1): ?>
                        <a href="<?= url('/app/boxes?' . http_build_query(array_merge($filters, ['page' => 1]))) ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">1</a>
                        <?php if ($startPage > 2): ?>
                            <span class="px-3 py-2 text-sm font-medium text-gray-500">...</span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                        <?php if ($i == $pagination['current_page']): ?>
                            <span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">
                                <?= $i ?>
                            </span>
                        <?php else: ?>
                            <a href="<?= url('/app/boxes?' . http_build_query(array_merge($filters, ['page' => $i]))) ?>"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                <?= $i ?>
                            </a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <?php if ($endPage < $pagination['total_pages']): ?>
                        <?php if ($endPage < $pagination['total_pages'] - 1): ?>
                            <span class="px-3 py-2 text-sm font-medium text-gray-500">...</span>
                        <?php endif; ?>
                        <a href="<?= url('/app/boxes?' . http_build_query(array_merge($filters, ['page' => $pagination['total_pages']]))) ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            <?= $pagination['total_pages'] ?>
                        </a>
                    <?php endif; ?>

                    <!-- Next Page -->
                    <?php if ($pagination['has_next']): ?>
                        <a href="<?= url('/app/boxes?' . http_build_query(array_merge($filters, ['page' => $pagination['current_page'] + 1]))) ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Next
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>








    </div>
</div>

<!-- Delete Box Confirmation Modal -->
<div id="deleteBoxModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
        <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Delete Box</h3>
            <p class="text-gray-600 mb-4">Are you sure you want to delete box <strong id="boxName"></strong>?</p>

            <!-- Warning about contents -->
            <div id="boxContents" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 text-left">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-yellow-800">⚠️ This box contains:</p>
                        <ul class="text-sm text-yellow-700 mt-2 space-y-1">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span id="bundleCount"></span>
                            </li>
                        </ul>
                        <div class="mt-3 p-3 bg-yellow-100 rounded-lg">
                            <p class="text-xs font-medium text-yellow-800">📋 Required Actions:</p>
                            <p class="text-xs text-yellow-700 mt-1">
                                1. Move all bundles to other boxes<br>
                                2. Or delete bundles individually first<br>
                                3. Ensure all bundles are properly relocated
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty box confirmation -->
            <div id="emptyBoxConfirm" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 text-left" style="display: none;">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-green-800">✅ This box is empty</p>
                        <p class="text-xs text-green-700 mt-1">
                            No bundles will be affected by this deletion.
                        </p>
                    </div>
                </div>
            </div>

            <p class="text-sm text-gray-500 mb-6">This action cannot be undone.</p>

            <div class="flex items-center justify-center space-x-4">
                <button onclick="closeDeleteBoxModal()"
                        class="px-6 py-2 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <form id="deleteBoxForm" method="POST" class="inline">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit"
                            class="px-6 py-2 bg-red-600 text-white font-medium rounded-xl hover:bg-red-700 transition-colors">
                        Delete Box
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced Box Filtering System
let activeFilters = {
    storageType: 'all',
    status: '',
    search: ''
};

document.addEventListener('DOMContentLoaded', function() {
    initializeFilters();
    updateFilterCounts();
});

function initializeFilters() {
    // Search functionality
    const searchInput = document.getElementById('searchBoxes');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            activeFilters.search = this.value.toLowerCase();
            applyFilters();
        });
    }

    // Status filter
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            activeFilters.status = this.value;
            applyFilters();
        });
    }

    // Initialize storage type filter styles
    updateStorageTypeButtons();
}

function filterByStorageType(type) {
    activeFilters.storageType = type;
    updateStorageTypeButtons();
    applyFilters();
    updateActiveFilterIndicator();
}

function updateStorageTypeButtons() {
    const buttons = document.querySelectorAll('.storage-filter-tab');
    buttons.forEach(button => {
        const isActive = button.id === `filter-${activeFilters.storageType}`;
        if (isActive) {
            button.classList.add('active', 'bg-blue-600', 'text-white');
            button.classList.remove('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
        } else {
            button.classList.remove('active', 'bg-blue-600', 'text-white');
            button.classList.add('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
        }
    });
}

function applyFilters() {
    const rows = document.querySelectorAll('.box-row');
    let visibleCount = 0;

    rows.forEach(row => {
        const storageType = row.getAttribute('data-storage-type');
        const status = row.getAttribute('data-box-status');
        const boxName = row.getAttribute('data-box-name') || '';
        const boxId = row.getAttribute('data-box-id') || '';
        const warehouse = row.getAttribute('data-warehouse') || '';

        // Storage type filter
        const matchesStorageType = activeFilters.storageType === 'all' || storageType === activeFilters.storageType;

        // Status filter
        const matchesStatus = !activeFilters.status || status === activeFilters.status;

        // Search filter
        const searchTerm = activeFilters.search;
        const matchesSearch = !searchTerm ||
            boxName.includes(searchTerm) ||
            boxId.includes(searchTerm) ||
            warehouse.includes(searchTerm);

        if (matchesStorageType && matchesStatus && matchesSearch) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    updateFilterCounts();
    updateActiveFilterIndicator(visibleCount);
}

function updateFilterCounts() {
    const rows = document.querySelectorAll('.box-row');
    const counts = {
        all: 0,
        physical: 0,
        online: 0,
        mixed: 0
    };

    rows.forEach(row => {
        const storageType = row.getAttribute('data-storage-type');
        counts.all++;
        if (counts[storageType] !== undefined) {
            counts[storageType]++;
        }
    });

    // Update count displays
    Object.keys(counts).forEach(type => {
        const countElement = document.getElementById(`count-${type}`);
        if (countElement) {
            countElement.textContent = counts[type];
        }
    });
}

function updateActiveFilterIndicator(visibleCount = null) {
    const indicator = document.getElementById('activeFilterIndicator');
    const filterText = document.getElementById('activeFilterText');
    const countElement = document.getElementById('filteredCount');

    if (!indicator || !filterText) return;

    const hasActiveFilters = activeFilters.storageType !== 'all' || activeFilters.status || activeFilters.search;

    if (hasActiveFilters) {
        indicator.classList.remove('hidden');

        let filterDescription = [];

        if (activeFilters.storageType !== 'all') {
            const typeNames = {
                'physical': '📦 Physical',
                'online': '☁️ Online',
                'mixed': '🔄 Mixed'
            };
            filterDescription.push(typeNames[activeFilters.storageType] || activeFilters.storageType);
        }

        if (activeFilters.status) {
            filterDescription.push(activeFilters.status + ' boxes');
        }

        if (activeFilters.search) {
            filterDescription.push(`"${activeFilters.search}"`);
        }

        filterText.textContent = `Showing ${filterDescription.join(' + ')}`;

        if (visibleCount !== null && countElement) {
            countElement.textContent = `(${visibleCount} boxes)`;
        }
    } else {
        indicator.classList.add('hidden');
    }
}

function clearAllFilters() {
    activeFilters = {
        storageType: 'all',
        status: '',
        search: ''
    };

    // Reset form elements
    const searchInput = document.getElementById('searchBoxes');
    const statusFilter = document.getElementById('statusFilter');

    if (searchInput) searchInput.value = '';
    if (statusFilter) statusFilter.value = '';

    // Update UI
    updateStorageTypeButtons();
    applyFilters();
    updateActiveFilterIndicator();
}

// Delete Box Modal Functions
function showDeleteBoxModal(boxId, boxName, bundleCount) {
    document.getElementById('boxName').textContent = boxName;
    document.getElementById('bundleCount').textContent = `${bundleCount} bundle(s)`;

    // Show/hide appropriate sections based on bundle count
    const boxContents = document.getElementById('boxContents');
    const emptyBoxConfirm = document.getElementById('emptyBoxConfirm');

    if (bundleCount > 0) {
        boxContents.style.display = 'block';
        emptyBoxConfirm.style.display = 'none';
    } else {
        boxContents.style.display = 'none';
        emptyBoxConfirm.style.display = 'block';
    }

    // Set form action
    document.getElementById('deleteBoxForm').action = `<?= url('/app/boxes/') ?>${boxId}`;

    // Show modal
    document.getElementById('deleteBoxModal').classList.remove('hidden');
    document.getElementById('deleteBoxModal').classList.add('flex');
}

function closeDeleteBoxModal() {
    document.getElementById('deleteBoxModal').classList.add('hidden');
    document.getElementById('deleteBoxModal').classList.remove('flex');
}

// Close modal when clicking outside
document.getElementById('deleteBoxModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteBoxModal();
    }
});

// CSS for active filter tabs
const style = document.createElement('style');
style.textContent = `
    .storage-filter-tab {
        transition: all 0.2s ease;
    }
    .storage-filter-tab:hover:not(.active) {
        background-color: #f3f4f6;
    }
    .storage-filter-tab.active {
        background-color: #2563eb !important;
        color: white !important;
    }
`;
document.head.appendChild(style);
</script>




<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
