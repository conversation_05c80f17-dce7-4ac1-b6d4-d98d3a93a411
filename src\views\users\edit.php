<?php
$title = 'Edit User - ' . $viewUser['first_name'] . ' ' . $viewUser['last_name'];
ob_start();
?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit User</h1>
                <p class="text-gray-600 mt-1">Update user information and permissions</p>
            </div>
            <a href="<?= url("/app/users/{$viewUser['id']}") ?>"
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to User
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-2xl shadow-lg p-8">
        <form method="POST" action="<?= url("/app/users/{$viewUser['id']}") ?>" class="space-y-8">
            <input type="hidden" name="_method" value="PUT">
            
            <!-- Personal Information -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                        <input type="text" 
                               id="first_name" 
                               name="first_name" 
                               required 
                               value="<?= e($_POST['first_name'] ?? $viewUser['first_name']) ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter first name">
                    </div>

                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                        <input type="text" 
                               id="last_name" 
                               name="last_name" 
                               required 
                               value="<?= e($_POST['last_name'] ?? $viewUser['last_name']) ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter last name">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               required 
                               value="<?= e($_POST['email'] ?? $viewUser['email']) ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               value="<?= e($_POST['phone'] ?? $viewUser['phone']) ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="+****************">
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                        <input type="text" 
                               id="username" 
                               name="username" 
                               required 
                               value="<?= e($_POST['username'] ?? $viewUser['username']) ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter username">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                        <input type="password" 
                               id="password" 
                               name="password" 
                               minlength="8"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Leave blank to keep current password">
                        <p class="text-sm text-gray-500 mt-1">Leave blank to keep current password</p>
                    </div>

                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Role *</label>
                        <select id="role" 
                                name="role" 
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select a role</option>
                            <?php if ($user['role'] === 'super_admin'): ?>
                                <option value="company_admin" <?= ($_POST['role'] ?? $viewUser['role']) === 'company_admin' ? 'selected' : '' ?>>Company Admin</option>
                            <?php endif; ?>
                            <option value="manager" <?= ($_POST['role'] ?? $viewUser['role']) === 'manager' ? 'selected' : '' ?>>Manager</option>
                            <option value="editor" <?= ($_POST['role'] ?? $viewUser['role']) === 'editor' ? 'selected' : '' ?>>Editor</option>
                            <option value="viewer" <?= ($_POST['role'] ?? $viewUser['role']) === 'viewer' ? 'selected' : '' ?>>Viewer</option>
                            <option value="client" <?= ($_POST['role'] ?? $viewUser['role']) === 'client' ? 'selected' : '' ?>>Client</option>
                        </select>
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="status" 
                                name="status"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="active" <?= ($_POST['status'] ?? $viewUser['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= ($_POST['status'] ?? $viewUser['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            <option value="suspended" <?= ($_POST['status'] ?? $viewUser['status']) === 'suspended' ? 'selected' : '' ?>>Suspended</option>
                        </select>
                    </div>

                    <?php if ($user['role'] === 'super_admin' && !empty($companies)): ?>
                    <div class="md:col-span-2">
                        <label for="company_id" class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                        <select id="company_id" 
                                name="company_id"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select a company</option>
                            <?php foreach ($companies as $company): ?>
                                <option value="<?= $company['id'] ?>" <?= ($_POST['company_id'] ?? $viewUser['company_id']) == $company['id'] ? 'selected' : '' ?>>
                                    <?= e($company['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Account Details -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Details</h3>
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Member Since</label>
                            <p class="text-gray-900"><?= date('F j, Y', strtotime($user['created_at'])) ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Last Updated</label>
                            <p class="text-gray-900"><?= date('F j, Y g:i A', strtotime($user['updated_at'])) ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Last Login</label>
                            <p class="text-gray-900"><?= $user['last_login'] ? date('F j, Y g:i A', strtotime($user['last_login'])) : 'Never' ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Email Verified</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                <?= $user['email_verified'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                <?= $user['email_verified'] ? 'Verified' : 'Not Verified' ?>
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Current Company</label>
                            <p class="text-gray-900"><?= e($user['company_name'] ?? 'N/A') ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">User ID</label>
                            <p class="text-gray-900 font-mono text-sm"><?= $user['id'] ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="<?= url("/app/users/{$viewUser['id']}") ?>"
                   class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Update User
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    if (password && password.length < 8) {
        e.preventDefault();
        alert('Password must be at least 8 characters long');
        return false;
    }
});

// Auto-generate username from email if empty
document.getElementById('email').addEventListener('blur', function() {
    const usernameField = document.getElementById('username');
    if (!usernameField.value) {
        const email = this.value;
        if (email) {
            const username = email.split('@')[0];
            usernameField.value = username;
        }
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
