<?php
/**
 * Create Super Admin User Script
 * 
 * This script creates a super admin user for testing the super admin functionality
 */

require_once 'src/autoload.php';

use App\Core\Database;

$db = Database::getInstance();

echo "=== CREATING SUPER ADMIN USER ===\n";

try {
    // Check if super admin user already exists
    $existingAdmin = $db->fetch(
        "SELECT * FROM users WHERE role = 'super_admin' LIMIT 1"
    );
    
    if ($existingAdmin) {
        echo "✓ Super admin user already exists:\n";
        echo "  ID: {$existingAdmin['id']}\n";
        echo "  Username: {$existingAdmin['username']}\n";
        echo "  Email: {$existingAdmin['email']}\n";
        echo "  Role: {$existingAdmin['role']}\n";
        echo "  Status: {$existingAdmin['status']}\n\n";
        
        // Update password to known value for testing
        $newPassword = 'admin123';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

        $db->query(
            "UPDATE users SET password_hash = ?, status = 'active' WHERE id = ?",
            [$hashedPassword, $existingAdmin['id']]
        );
        
        echo "✓ Updated super admin password to: {$newPassword}\n";
        echo "✓ Ensured super admin status is active\n\n";
        
    } else {
        echo "Creating new super admin user...\n";
        
        // Get or create a company for the super admin
        $company = $db->fetch("SELECT * FROM companies LIMIT 1");
        
        if (!$company) {
            echo "Creating system company...\n";
            $stmt = $db->query(
                "INSERT INTO companies (name, email, subscription_plan, storage_limit, status, created_at, updated_at)
                 VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                [
                    'System Administration',
                    '<EMAIL>',
                    'enterprise',
                    1073741824000, // 1TB
                    'active'
                ]
            );
            $companyId = $db->getConnection()->lastInsertId();
            echo "✓ Created system company with ID: {$companyId}\n";
        } else {
            $companyId = $company['id'];
            echo "✓ Using existing company: {$company['name']} (ID: {$companyId})\n";
        }
        
        // Create super admin user
        $username = 'superadmin';
        $email = '<EMAIL>';
        $password = 'admin123';
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $db->query(
            "INSERT INTO users (
                company_id, first_name, last_name, email, username, password_hash,
                role, status, email_verified, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $companyId,
                'Super',
                'Admin',
                $email,
                $username,
                $hashedPassword,
                'super_admin',
                'active',
                1
            ]
        );
        $userId = $db->getConnection()->lastInsertId();
        
        echo "✓ Created super admin user:\n";
        echo "  ID: {$userId}\n";
        echo "  Username: {$username}\n";
        echo "  Email: {$email}\n";
        echo "  Password: {$password}\n";
        echo "  Role: super_admin\n";
        echo "  Status: active\n\n";
    }
    
    // Check if we have sample companies with subscription data
    $companiesWithSubs = $db->fetch(
        "SELECT COUNT(*) as count FROM companies WHERE monthly_fee > 0"
    );
    
    if ($companiesWithSubs['count'] == 0) {
        echo "Creating sample companies with subscription data...\n";
        
        // Create sample companies
        $sampleCompanies = [
            [
                'name' => 'Acme Corporation',
                'email' => '<EMAIL>',
                'subscription_plan' => 'premium',
                'monthly_fee' => 299.00,
                'storage_limit' => 53687091200 // 50GB
            ],
            [
                'name' => 'TechStart Inc',
                'email' => '<EMAIL>',
                'subscription_plan' => 'basic',
                'monthly_fee' => 99.00,
                'storage_limit' => 5368709120 // 5GB
            ],
            [
                'name' => 'Enterprise Solutions Ltd',
                'email' => '<EMAIL>',
                'subscription_plan' => 'enterprise',
                'monthly_fee' => 999.00,
                'storage_limit' => 536870912000 // 500GB
            ]
        ];
        
        foreach ($sampleCompanies as $company) {
            $stmt = $db->query(
                "INSERT INTO companies (
                    name, email, subscription_plan, storage_limit, monthly_fee,
                    contract_start_date, contract_end_date, payment_status,
                    last_payment_date, next_payment_date, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
                [
                    $company['name'],
                    $company['email'],
                    $company['subscription_plan'],
                    $company['storage_limit'],
                    $company['monthly_fee'],
                    date('Y-m-d', strtotime('-6 months')),
                    date('Y-m-d', strtotime('+6 months')),
                    'active',
                    date('Y-m-d', strtotime('-1 month')),
                    date('Y-m-d', strtotime('+1 month')),
                    'active'
                ]
            );
            $companyId = $db->getConnection()->lastInsertId();
            
            echo "✓ Created sample company: {$company['name']} (ID: {$companyId})\n";
            
            // Create a company admin for each company
            $stmt = $db->query(
                "INSERT INTO users (
                    company_id, first_name, last_name, email, username, password_hash,
                    role, status, email_verified, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
                [
                    $companyId,
                    'Company',
                    'Admin',
                    str_replace('@', '+admin@', $company['email']),
                    strtolower(str_replace(' ', '', $company['name'])) . '_admin',
                    password_hash('admin123', PASSWORD_DEFAULT),
                    'company_admin',
                    'active',
                    1
                ]
            );
            $adminUserId = $db->getConnection()->lastInsertId();
            
            echo "  ✓ Created company admin (ID: {$adminUserId})\n";
        }
        
        echo "\n";
    }
    
    // Display login information
    echo "=== LOGIN INFORMATION ===\n";
    echo "Super Admin Login:\n";
    echo "  URL: http://localhost/dms/login\n";
    echo "  Username: superadmin (or existing super admin username)\n";
    echo "  Password: admin123\n\n";
    
    echo "After login, access super admin dashboard at:\n";
    echo "  http://localhost/dms/super-admin/dashboard\n\n";
    
    echo "Super admin setup completed successfully!\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
