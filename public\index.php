<?php
/**
 * Document Management System - Main Entry Point
 * 
 * This file serves as the main entry point for the DMS application.
 * It handles routing, authentication, and request processing.
 */

// Start session
session_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 0); // Disable display_errors for AJAX requests
ini_set('log_errors', 1); // Log errors instead

// Enable debug mode for routing (but only for non-AJAX requests)
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
          strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
define('DEBUG_ROUTING', !$isAjax);

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');
define('UPLOAD_PATH', PUBLIC_ROOT . '/uploads');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load constants
require_once CONFIG_PATH . '/constants.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

// Initialize the application
try {
    // Create router instance
    $router = new \App\Core\Router();
    
    // Load routes
    require_once APP_ROOT . '/src/routes.php';
    
    // Get current request
    $requestUri = $_SERVER['REQUEST_URI'];
    $requestMethod = $_SERVER['REQUEST_METHOD'];

    // Handle HTTP method override for forms
    if ($requestMethod === 'POST' && isset($_POST['_method'])) {
        $requestMethod = strtoupper($_POST['_method']);
    }
    
    // Remove query string from URI
    $requestUri = strtok($requestUri, '?');
    
    // Remove base path if application is in subdirectory
    $scriptName = $_SERVER['SCRIPT_NAME'];

    // Detect base path more reliably
    $basePath = '';
    if (strpos($scriptName, '/dms/public/') !== false) {
        $basePath = '/dms/public';
    } elseif (strpos($scriptName, '/dms/') !== false) {
        $basePath = '/dms';
    } elseif (dirname($scriptName) !== '/') {
        $basePath = dirname($scriptName);
    }

    // Remove base path from request URI
    if ($basePath && strpos($requestUri, $basePath) === 0) {
        $requestUri = substr($requestUri, strlen($basePath));
    }

    // Ensure URI starts with /
    if (!$requestUri || $requestUri[0] !== '/') {
        $requestUri = '/' . $requestUri;
    }

    // Store base path for URL generation
    define('BASE_PATH', $basePath === '/' ? '' : $basePath);

    // Debug output
    if (defined('DEBUG_ROUTING') && DEBUG_ROUTING) {
        error_log("Index.php: Processing {$requestMethod} {$requestUri} (base: {$basePath})");
    }

    // Handle the request
    $router->handleRequest($requestMethod, $requestUri);
    
} catch (Exception $e) {
    // Log error
    error_log("Application Error: " . $e->getMessage());
    
    // Show error page
    http_response_code(500);
    
    if ($config['debug']) {
        echo "<h1>Application Error</h1>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        echo "<h1>Internal Server Error</h1>";
        echo "<p>Something went wrong. Please try again later.</p>";
    }
}
