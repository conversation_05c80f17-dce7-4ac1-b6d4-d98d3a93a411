<?php
$title = 'Edit Company - ' . ($company['name'] ?? 'Unknown');
ob_start();
?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Company</h1>
                <p class="text-gray-600 mt-1">Update company information and settings</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="<?= url("/app/companies/{$company['id']}") ?>" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Company
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-2xl shadow-lg p-8">
        <form method="POST" action="<?= url("/app/companies/{$company['id']}") ?>" class="space-y-8">
            <input type="hidden" name="_method" value="PUT">
            
            <!-- Company Information -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Company Name *</label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               required 
                               value="<?= e($company['name'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter company name">
                    </div>

                    <div>
                        <label for="domain" class="block text-sm font-medium text-gray-700 mb-2">Domain</label>
                        <input type="text" 
                               id="domain" 
                               name="domain" 
                               value="<?= e($company['domain'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="company.com">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               required 
                               value="<?= e($company['email'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               value="<?= e($company['phone'] ?? '') ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="+****************">
                    </div>

                    <div class="md:col-span-2">
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <textarea id="address" 
                                  name="address" 
                                  rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Enter company address"><?= e($company['address'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Subscription Settings (Super Admin Only) -->
            <?php if ($user['role'] === 'super_admin'): ?>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription Settings</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    
                    <div>
                        <label for="subscription_plan" class="block text-sm font-medium text-gray-700 mb-2">Plan</label>
                        <select id="subscription_plan" 
                                name="subscription_plan"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="basic" <?= ($company['subscription_plan'] ?? '') === 'basic' ? 'selected' : '' ?>>Basic</option>
                            <option value="premium" <?= ($company['subscription_plan'] ?? '') === 'premium' ? 'selected' : '' ?>>Premium</option>
                            <option value="enterprise" <?= ($company['subscription_plan'] ?? '') === 'enterprise' ? 'selected' : '' ?>>Enterprise</option>
                        </select>
                    </div>

                    <div>
                        <label for="storage_limit" class="block text-sm font-medium text-gray-700 mb-2">Storage Limit (GB)</label>
                        <input type="number" 
                               id="storage_limit" 
                               name="storage_limit" 
                               min="1"
                               value="<?= round(($company['storage_limit'] ?? 0) / 1073741824, 2) ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="5">
                        <p class="text-sm text-gray-500 mt-1">Storage limit in gigabytes</p>
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="status" 
                                name="status"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="active" <?= ($company['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="suspended" <?= ($company['status'] ?? '') === 'suspended' ? 'selected' : '' ?>>Suspended</option>
                            <option value="inactive" <?= ($company['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Current Usage -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Current Usage</h3>
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Storage Used</label>
                            <p class="text-lg font-semibold text-gray-900"><?= formatFileSize($company['storage_used'] ?? 0) ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Storage Limit</label>
                            <p class="text-lg font-semibold text-gray-900"><?= formatFileSize($company['storage_limit'] ?? 0) ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Usage Percentage</label>
                            <?php 
                            $percentage = $company['storage_limit'] > 0 ? 
                                min(100, ($company['storage_used'] / $company['storage_limit']) * 100) : 0;
                            ?>
                            <p class="text-lg font-semibold text-gray-900"><?= number_format($percentage, 1) ?>%</p>
                        </div>

                        <div class="md:col-span-3">
                            <label class="block text-sm font-medium text-gray-500 mb-2">Storage Usage Bar</label>
                            <div class="w-full bg-gray-200 rounded-full h-4">
                                <div class="bg-blue-600 h-4 rounded-full transition-all duration-300" style="width: <?= $percentage ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Company Metadata -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Metadata</h3>
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Created</label>
                            <p class="text-gray-900"><?= date('F j, Y g:i A', strtotime($company['created_at'])) ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Last Updated</label>
                            <p class="text-gray-900"><?= date('F j, Y g:i A', strtotime($company['updated_at'])) ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Company ID</label>
                            <p class="text-gray-900 font-mono"><?= $company['id'] ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Current Plan</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                <?php if ($company['subscription_plan'] === 'enterprise'): ?>
                                    bg-purple-100 text-purple-800
                                <?php elseif ($company['subscription_plan'] === 'premium'): ?>
                                    bg-blue-100 text-blue-800
                                <?php else: ?>
                                    bg-green-100 text-green-800
                                <?php endif; ?>">
                                <?= ucfirst($company['subscription_plan'] ?? 'basic') ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="<?= url("/app/companies/{$company['id']}") ?>" 
                   class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Update Company
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-fill storage limit based on plan selection (Super Admin only)
<?php if ($user['role'] === 'super_admin'): ?>
document.getElementById('subscription_plan').addEventListener('change', function() {
    const storageInput = document.getElementById('storage_limit');
    const planLimits = {
        'basic': 5,
        'premium': 50,
        'enterprise': 500
    };
    
    if (planLimits[this.value]) {
        storageInput.value = planLimits[this.value];
    }
});
<?php endif; ?>
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
