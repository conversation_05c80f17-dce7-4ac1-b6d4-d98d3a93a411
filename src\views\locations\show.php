<?php
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="container mx-auto px-6 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        <?= e($location['name']) ?>
                    </h1>
                    <p class="text-gray-600">Storage location in <strong><?= e($location['warehouse_name']) ?></strong></p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/warehouses/' . $location['warehouse_id'] . '/locations') ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Locations
                    </a>
                    <a href="<?= url('/app/locations/' . $location['id'] . '/edit') ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Location
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Location Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Location Information
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-1">Location Name</label>
                            <p class="text-lg text-gray-900"><?= e($location['name']) ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-1">Location Type</label>
                            <div class="flex items-center">
                                <?php
                                $typeIcons = [
                                    'shelf' => '📚',
                                    'room' => '🏠',
                                    'aisle' => '🛤️',
                                    'cabinet' => '🗄️',
                                    'vault' => '🔒',
                                    'box' => '📦',
                                    'rack' => '🏗️',
                                    'floor' => '🏢',
                                    'building' => '🏭'
                                ];
                                $icon = $typeIcons[$location['type']] ?? '📍';
                                ?>
                                <span class="text-2xl mr-2"><?= $icon ?></span>
                                <span class="text-lg text-gray-900 capitalize"><?= e($location['type']) ?></span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-1">Identifier</label>
                            <p class="text-lg font-mono text-gray-900 bg-gray-100 px-3 py-1 rounded"><?= e($location['identifier']) ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-1">Status</label>
                            <?php
                            $statusColors = [
                                'active' => 'bg-green-100 text-green-800',
                                'maintenance' => 'bg-yellow-100 text-yellow-800',
                                'full' => 'bg-red-100 text-red-800',
                                'inactive' => 'bg-gray-100 text-gray-800'
                            ];
                            $statusColor = $statusColors[$location['status']] ?? 'bg-gray-100 text-gray-800';
                            ?>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?= $statusColor ?>">
                                <?= ucfirst($location['status']) ?>
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-1">Capacity</label>
                            <p class="text-lg text-gray-900"><?= number_format($location['capacity'] ?? 0) ?> documents</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-1">Used Capacity</label>
                            <p class="text-lg text-gray-900"><?= number_format($location['used_capacity'] ?? 0, 2) ?></p>
                        </div>
                    </div>
                    
                    <?php if (!empty($location['description'])): ?>
                        <div class="mt-6">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Description</label>
                            <p class="text-gray-900 bg-gray-50 p-4 rounded-lg"><?= e($location['description']) ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Documents in Location -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Documents (<?= count($documents) ?>)
                        </h2>
                        <a href="<?= url('/app/locations/' . $location['id'] . '/documents') ?>" class="text-blue-600 hover:text-blue-800 font-medium">
                            View All →
                        </a>
                    </div>
                    
                    <?php if (empty($documents)): ?>
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No documents found</h3>
                            <p class="text-gray-600">This storage location doesn't contain any documents yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach (array_slice($documents, 0, 5) as $document): ?>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-900"><?= e($document['title'] ?? $document['filename'] ?? 'Untitled') ?></h4>
                                            <p class="text-sm text-gray-600">
                                                Added by <?= e($document['first_name'] ?? 'Unknown') ?> <?= e($document['last_name'] ?? '') ?>
                                                on <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                            </p>
                                        </div>
                                    </div>
                                    <a href="<?= url('/app/documents/' . $document['id']) ?>" class="text-blue-600 hover:text-blue-800">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                            
                            <?php if (count($documents) > 5): ?>
                                <div class="text-center pt-4">
                                    <a href="<?= url('/app/locations/' . $location['id'] . '/documents') ?>" class="text-blue-600 hover:text-blue-800 font-medium">
                                        View all <?= count($documents) ?> documents →
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Stats -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Stats</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Documents</span>
                            <span class="font-semibold text-gray-900"><?= count($documents) ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Capacity</span>
                            <span class="font-semibold text-gray-900"><?= number_format($location['capacity'] ?? 0) ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Used</span>
                            <span class="font-semibold text-gray-900"><?= number_format($location['used_capacity'] ?? 0, 2) ?></span>
                        </div>
                        <?php if (($location['capacity'] ?? 0) > 0): ?>
                            <div class="pt-2">
                                <?php $percentage = (($location['used_capacity'] ?? 0) / $location['capacity']) * 100; ?>
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm text-gray-600">Utilization</span>
                                    <span class="text-sm font-semibold text-gray-900"><?= number_format($percentage, 1) ?>%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: <?= min($percentage, 100) ?>%"></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Warehouse Info -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Warehouse</h3>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900"><?= e($location['warehouse_name']) ?></h4>
                            <a href="<?= url('/app/warehouses/' . $location['warehouse_id']) ?>" class="text-blue-600 hover:text-blue-800 text-sm">
                                View warehouse →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="<?= url('/app/locations/' . $location['id'] . '/edit') ?>" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Location
                        </a>
                        <a href="<?= url('/app/locations/' . $location['id'] . '/documents') ?>" class="w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            View Documents
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
