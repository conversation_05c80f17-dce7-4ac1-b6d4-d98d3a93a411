/**
 * Document Management System - Main JavaScript
 */

// Global DMS object
window.DMS = {
    config: {
        apiUrl: '/api/v1',
        uploadUrl: '/upload',
        maxFileSize: 50 * 1024 * 1024, // 50MB
        allowedTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'gif']
    },
    
    // Utility functions
    utils: {
        // Format file size
        formatFileSize: function(bytes) {
            const units = ['B', 'KB', 'MB', 'GB'];
            let size = bytes;
            let unitIndex = 0;
            
            while (size >= 1024 && unitIndex < units.length - 1) {
                size /= 1024;
                unitIndex++;
            }
            
            return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
        },
        
        // Get file extension
        getFileExtension: function(filename) {
            return filename.split('.').pop().toLowerCase();
        },
        
        // Check if file type is allowed
        isAllowedFileType: function(filename) {
            const ext = this.getFileExtension(filename);
            return DMS.config.allowedTypes.includes(ext);
        },
        
        // Generate UUID
        generateUUID: function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },
        
        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // Show notification
        showNotification: function(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 max-w-sm bg-${type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue'}-500 text-white px-6 py-4 rounded-lg shadow-lg fade-in`;
            
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    },
    
    // API functions
    api: {
        // Make API request
        request: async function(endpoint, options = {}) {
            const url = DMS.config.apiUrl + endpoint;
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            const config = { ...defaultOptions, ...options };
            
            try {
                const response = await fetch(url, config);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Request failed');
                }
                
                return data;
            } catch (error) {
                console.error('API request failed:', error);
                throw error;
            }
        },
        
        // GET request
        get: function(endpoint) {
            return this.request(endpoint, { method: 'GET' });
        },
        
        // POST request
        post: function(endpoint, data) {
            return this.request(endpoint, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        },
        
        // PUT request
        put: function(endpoint, data) {
            return this.request(endpoint, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
        },
        
        // DELETE request
        delete: function(endpoint) {
            return this.request(endpoint, { method: 'DELETE' });
        }
    },
    
    // File upload functionality
    upload: {
        // Upload single file
        uploadFile: async function(file, options = {}) {
            const formData = new FormData();
            formData.append('file', file);
            
            // Add additional options
            Object.keys(options).forEach(key => {
                formData.append(key, options[key]);
            });
            
            try {
                const response = await fetch(DMS.config.uploadUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Upload failed');
                }
                
                return data;
            } catch (error) {
                console.error('Upload failed:', error);
                throw error;
            }
        },
        
        // Upload multiple files
        uploadFiles: async function(files, options = {}) {
            const uploads = Array.from(files).map(file => 
                this.uploadFile(file, options)
            );
            
            return Promise.all(uploads);
        }
    },
    
    // Search functionality
    search: {
        // Perform search
        search: async function(query, filters = {}) {
            const params = new URLSearchParams({
                q: query,
                ...filters
            });
            
            return DMS.api.get('/search?' + params.toString());
        },
        
        // Get search suggestions
        getSuggestions: async function(query) {
            if (query.length < 3) return [];
            
            try {
                const response = await DMS.api.get('/search/suggestions?q=' + encodeURIComponent(query));
                return response.suggestions || [];
            } catch (error) {
                console.error('Failed to get suggestions:', error);
                return [];
            }
        }
    },
    
    // Barcode functionality
    barcode: {
        scanner: null,
        
        // Initialize barcode scanner
        initScanner: function(videoElement, onScan) {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('Camera access not supported');
            }
            
            // This would integrate with a barcode scanning library like QuaggaJS
            // For now, just a placeholder
            console.log('Barcode scanner initialized');
        },
        
        // Generate barcode
        generateBarcode: async function(entityType, entityId, barcodeType = 'qr') {
            return DMS.api.post('/barcodes/generate', {
                entity_type: entityType,
                entity_id: entityId,
                barcode_type: barcodeType
            });
        },
        
        // Scan barcode
        scanBarcode: async function(barcodeValue) {
            return DMS.api.post('/barcodes/scan', {
                barcode_value: barcodeValue
            });
        }
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize drag and drop
    DMS.initDragAndDrop();
    
    // Initialize search
    DMS.initSearch();
    
    // Initialize forms
    DMS.initForms();
});

// Drag and drop initialization
DMS.initDragAndDrop = function() {
    const dropZones = document.querySelectorAll('.drop-zone');
    
    dropZones.forEach(zone => {
        zone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        zone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        zone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                DMS.handleFileUpload(files, this);
            }
        });
        
        zone.addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = DMS.config.allowedTypes.map(ext => '.' + ext).join(',');
            
            input.addEventListener('change', function() {
                if (this.files.length > 0) {
                    DMS.handleFileUpload(this.files, zone);
                }
            });
            
            input.click();
        });
    });
};

// Search initialization
DMS.initSearch = function() {
    const searchInput = document.getElementById('search');
    if (!searchInput) return;
    
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className = 'search-suggestions hidden';
    searchInput.parentNode.appendChild(suggestionsContainer);
    
    const debouncedSearch = DMS.utils.debounce(async function(query) {
        if (query.length < 3) {
            suggestionsContainer.classList.add('hidden');
            return;
        }
        
        try {
            const suggestions = await DMS.search.getSuggestions(query);
            DMS.displaySearchSuggestions(suggestions, suggestionsContainer);
        } catch (error) {
            console.error('Search suggestions failed:', error);
        }
    }, 300);
    
    searchInput.addEventListener('input', function() {
        debouncedSearch(this.value);
    });
    
    searchInput.addEventListener('blur', function() {
        // Hide suggestions after a delay to allow clicking
        setTimeout(() => {
            suggestionsContainer.classList.add('hidden');
        }, 200);
    });
};

// Display search suggestions
DMS.displaySearchSuggestions = function(suggestions, container) {
    if (suggestions.length === 0) {
        container.classList.add('hidden');
        return;
    }
    
    container.innerHTML = suggestions.map(suggestion => 
        `<div class="search-suggestion" data-value="${suggestion.value}">
            <div class="font-medium">${suggestion.title}</div>
            <div class="text-sm text-gray-500">${suggestion.type}</div>
        </div>`
    ).join('');
    
    container.classList.remove('hidden');
    
    // Add click handlers
    container.querySelectorAll('.search-suggestion').forEach(item => {
        item.addEventListener('click', function() {
            const searchInput = document.getElementById('search');
            searchInput.value = this.dataset.value;
            container.classList.add('hidden');
            
            // Trigger search
            const form = searchInput.closest('form');
            if (form) {
                form.submit();
            }
        });
    });
};

// Form initialization
DMS.initForms = function() {
    // Add CSRF token to all forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        if (!form.querySelector('input[name="csrf_token"]')) {
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'csrf_token';
                input.value = csrfToken.content;
                form.appendChild(input);
            }
        }
    });
    
    // Handle AJAX forms
    const ajaxForms = document.querySelectorAll('form[data-ajax="true"]');
    ajaxForms.forEach(form => {
        form.addEventListener('submit', DMS.handleAjaxForm);
    });
};

// Handle AJAX form submission
DMS.handleAjaxForm = async function(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Show loading state
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.classList.add('loading');
    }
    
    try {
        const response = await fetch(form.action, {
            method: form.method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            if (data.redirect) {
                window.location.href = data.redirect;
            } else if (data.message) {
                DMS.utils.showNotification(data.message, 'success');
            }
        } else {
            if (data.errors) {
                DMS.displayFormErrors(form, data.errors);
            } else {
                DMS.utils.showNotification(data.message || 'An error occurred', 'error');
            }
        }
    } catch (error) {
        console.error('Form submission failed:', error);
        DMS.utils.showNotification('An error occurred. Please try again.', 'error');
    } finally {
        // Remove loading state
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.classList.remove('loading');
        }
    }
};

// Display form errors
DMS.displayFormErrors = function(form, errors) {
    // Clear existing errors
    form.querySelectorAll('.form-error').forEach(error => error.remove());
    form.querySelectorAll('.error').forEach(input => input.classList.remove('error'));
    
    // Display new errors
    Object.keys(errors).forEach(field => {
        const input = form.querySelector(`[name="${field}"]`);
        if (input) {
            input.classList.add('error');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'form-error';
            errorDiv.textContent = errors[field][0]; // Show first error
            
            input.parentNode.appendChild(errorDiv);
        }
    });
};

// Handle file upload
DMS.handleFileUpload = async function(files, dropZone) {
    const validFiles = Array.from(files).filter(file => {
        if (file.size > DMS.config.maxFileSize) {
            DMS.utils.showNotification(`File ${file.name} is too large`, 'error');
            return false;
        }
        
        if (!DMS.utils.isAllowedFileType(file.name)) {
            DMS.utils.showNotification(`File type not allowed: ${file.name}`, 'error');
            return false;
        }
        
        return true;
    });
    
    if (validFiles.length === 0) return;
    
    // Show upload progress
    const progressContainer = document.createElement('div');
    progressContainer.className = 'upload-progress mt-4';
    dropZone.appendChild(progressContainer);
    
    try {
        const uploads = await DMS.upload.uploadFiles(validFiles);
        DMS.utils.showNotification(`${uploads.length} file(s) uploaded successfully`, 'success');
        
        // Refresh page or update UI
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        
    } catch (error) {
        DMS.utils.showNotification('Upload failed: ' + error.message, 'error');
    } finally {
        progressContainer.remove();
    }
};
