<?php

namespace App\Controllers;

/**
 * User Controller
 * 
 * Manages user accounts, roles, and permissions
 * Super admin can manage all users, company admins manage users in their company
 */
class UserController extends BaseController
{
    /**
     * Display users list
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager']);
        
        try {
            // Get filter parameters
            $role = $_GET['role'] ?? '';
            $status = $_GET['status'] ?? '';
            $company = $_GET['company'] ?? '';
            $search = $_GET['search'] ?? '';
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 20;
            $offset = ($page - 1) * $limit;

            // Build query based on user role
            $where = [];
            $params = [];

            // Super admin can see all users, others only their company
            if ($this->user['role'] !== 'super_admin') {
                $where[] = "u.company_id = ?";
                $params[] = $this->user['company_id'];
            }

            if (!empty($role)) {
                $where[] = "u.role = ?";
                $params[] = $role;
            }

            if (!empty($status)) {
                $where[] = "u.status = ?";
                $params[] = $status;
            }

            if (!empty($company) && $this->user['role'] === 'super_admin') {
                $where[] = "u.company_id = ?";
                $params[] = $company;
            }

            if (!empty($search)) {
                $where[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ? OR u.username LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

            // Get users with company information
            $users = $this->db->fetchAll(
                "SELECT u.*, c.name as company_name,
                        COUNT(DISTINCT al.id) as activity_count,
                        MAX(al.created_at) as last_activity
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 LEFT JOIN activity_logs al ON u.id = al.user_id
                 {$whereClause}
                 GROUP BY u.id
                 ORDER BY u.created_at DESC
                 LIMIT ? OFFSET ?",
                array_merge($params, [$limit, $offset])
            );

            // Get total count
            $totalCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM users u {$whereClause}",
                $params
            );

            // Get companies for filter (super admin only)
            $companies = [];
            if ($this->user['role'] === 'super_admin') {
                $companies = $this->db->fetchAll(
                    "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name"
                );
            }

            // Get user statistics
            $stats = $this->getUserStats();

            $this->view('users/index', [
                'title' => 'User Management',
                'users' => $users,
                'totalCount' => $totalCount,
                'currentPage' => $page,
                'totalPages' => ceil($totalCount / $limit),
                'companies' => $companies,
                'stats' => $stats,
                'filters' => [
                    'role' => $role,
                    'status' => $status,
                    'company' => $company,
                    'search' => $search
                ]
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading users: ' . $e->getMessage(), 'error');
            $this->redirect('/dashboard');
        }
    }

    /**
     * Show user details
     */
    public function show($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin', 'manager']);
        
        try {
            $user = $this->getUserById($id);
            if (!$user) {
                throw new \Exception('User not found');
            }

            // Check access permissions
            if ($this->user['role'] !== 'super_admin' && $user['company_id'] != $this->user['company_id']) {
                throw new \Exception('Access denied');
            }

            // Get user activity
            $activity = $this->getUserActivity($id);
            
            // Get user statistics
            $userStats = $this->getUserStatsById($id);

            $this->view('users/show', [
                'title' => $user['first_name'] . ' ' . $user['last_name'],
                'viewUser' => $user,
                'activity' => $activity,
                'userStats' => $userStats
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading user: ' . $e->getMessage(), 'error');
            $this->redirect('/app/users');
        }
    }

    /**
     * Show create user form
     */
    public function create()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin']);
        
        // Get companies (super admin only)
        $companies = [];
        if ($this->user['role'] === 'super_admin') {
            $companies = $this->db->fetchAll(
                "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name"
            );
        }

        $this->view('users/create', [
            'title' => 'Create New User',
            'companies' => $companies
        ]);
    }

    /**
     * Store new user
     */
    public function store()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin']);
        
        try {
            // Validate input
            $data = $this->validate($_POST, [
                'first_name' => 'required|max:100',
                'last_name' => 'required|max:100',
                'email' => 'required|email|max:255',
                'username' => 'required|max:50',
                'password' => 'required|min:8',
                'phone' => 'max:20',
                'role' => 'required|in:company_admin,manager,editor,viewer,client',
                'company_id' => 'integer',
                'status' => 'in:active,inactive,suspended'
            ]);

            // Check if email is unique
            $existing = $this->db->fetch(
                "SELECT id FROM users WHERE email = ?",
                [$data['email']]
            );
            if ($existing) {
                throw new \Exception('Email already exists');
            }

            // Check if username is unique
            $existing = $this->db->fetch(
                "SELECT id FROM users WHERE username = ?",
                [$data['username']]
            );
            if ($existing) {
                throw new \Exception('Username already exists');
            }

            // Determine company ID
            if ($this->user['role'] === 'super_admin') {
                $companyId = $data['company_id'] ?? $this->user['company_id'];
            } else {
                $companyId = $this->user['company_id'];
            }

            // Validate role permissions
            if (!$this->canAssignRole($data['role'])) {
                throw new \Exception('You cannot assign this role');
            }

            // Create user
            $userId = $this->db->execute(
                "INSERT INTO users (
                    company_id, first_name, last_name, email, username, password_hash,
                    phone, role, status, email_verified, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())",
                [
                    $companyId,
                    $data['first_name'],
                    $data['last_name'],
                    $data['email'],
                    $data['username'],
                    password_hash($data['password'], PASSWORD_DEFAULT),
                    $data['phone'] ?? null,
                    $data['role'],
                    $data['status'] ?? 'active'
                ]
            );

            // Log activity
            $this->logActivity('create', 'user', $userId, "Created user: {$data['email']}");

            $this->setFlashMessage('User created successfully', 'success');
            $this->redirect("/app/users/{$userId}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create user: ' . $e->getMessage(), 'error');
            $this->redirect('/app/users/create');
        }
    }

    /**
     * Show edit user form
     */
    public function edit($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin']);
        
        try {
            $user = $this->getUserById($id);
            if (!$user) {
                throw new \Exception('User not found');
            }

            // Check access permissions
            if ($this->user['role'] !== 'super_admin' && $user['company_id'] != $this->user['company_id']) {
                throw new \Exception('Access denied');
            }

            // Get companies (super admin only)
            $companies = [];
            if ($this->user['role'] === 'super_admin') {
                $companies = $this->db->fetchAll(
                    "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name"
                );
            }

            $this->view('users/edit', [
                'title' => 'Edit User - ' . $user['first_name'] . ' ' . $user['last_name'],
                'viewUser' => $user,
                'companies' => $companies
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading user: ' . $e->getMessage(), 'error');
            $this->redirect('/app/users');
        }
    }

    /**
     * Update user
     */
    public function update($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin']);
        
        try {
            $user = $this->getUserById($id);
            if (!$user) {
                throw new \Exception('User not found');
            }

            // Check access permissions
            if ($this->user['role'] !== 'super_admin' && $user['company_id'] != $this->user['company_id']) {
                throw new \Exception('Access denied');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'first_name' => 'required|max:100',
                'last_name' => 'required|max:100',
                'email' => 'required|email|max:255',
                'username' => 'required|max:50',
                'phone' => 'max:20',
                'role' => 'required|in:company_admin,manager,editor,viewer,client',
                'company_id' => 'integer',
                'status' => 'in:active,inactive,suspended',
                'password' => 'min:8' // Optional
            ]);

            // Check if email is unique (excluding current user)
            if ($data['email'] !== $user['email']) {
                $existing = $this->db->fetch(
                    "SELECT id FROM users WHERE email = ? AND id != ?",
                    [$data['email'], $id]
                );
                if ($existing) {
                    throw new \Exception('Email already exists');
                }
            }

            // Check if username is unique (excluding current user)
            if ($data['username'] !== $user['username']) {
                $existing = $this->db->fetch(
                    "SELECT id FROM users WHERE username = ? AND id != ?",
                    [$data['username'], $id]
                );
                if ($existing) {
                    throw new \Exception('Username already exists');
                }
            }

            // Validate role permissions
            if (!$this->canAssignRole($data['role'])) {
                throw new \Exception('You cannot assign this role');
            }

            // Build update query
            $updateFields = [
                'first_name = ?', 'last_name = ?', 'email = ?', 'username = ?', 
                'phone = ?', 'role = ?', 'status = ?', 'updated_at = NOW()'
            ];
            $updateParams = [
                $data['first_name'], $data['last_name'], $data['email'], $data['username'],
                $data['phone'] ?? null, $data['role'], $data['status'] ?? $user['status']
            ];

            // Update company if super admin
            if ($this->user['role'] === 'super_admin' && isset($data['company_id'])) {
                $updateFields[] = 'company_id = ?';
                $updateParams[] = $data['company_id'];
            }

            // Update password if provided
            if (!empty($data['password'])) {
                $updateFields[] = 'password_hash = ?';
                $updateParams[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }

            $updateParams[] = $id;

            $this->db->execute(
                "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?",
                $updateParams
            );

            // Log activity
            $this->logActivity('update', 'user', $id, "Updated user: {$data['email']}");

            $this->setFlashMessage('User updated successfully', 'success');
            $this->redirect("/app/users/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to update user: ' . $e->getMessage(), 'error');
            $this->redirect("/app/users/{$id}/edit");
        }
    }

    /**
     * Delete user
     */
    public function delete($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'company_admin']);
        
        try {
            $user = $this->getUserById($id);
            if (!$user) {
                throw new \Exception('User not found');
            }

            // Check access permissions
            if ($this->user['role'] !== 'super_admin' && $user['company_id'] != $this->user['company_id']) {
                throw new \Exception('Access denied');
            }

            // Cannot delete yourself
            if ($id == $this->user['id']) {
                throw new \Exception('You cannot delete yourself');
            }

            // Soft delete - mark as inactive
            $this->db->execute(
                "UPDATE users SET status = 'inactive', updated_at = NOW() WHERE id = ?",
                [$id]
            );

            // Log activity
            $this->logActivity('delete', 'user', $id, "Deleted user: {$user['email']}");

            $this->setFlashMessage('User deleted successfully', 'success');
            
            if (isAjax()) {
                $this->jsonResponse(['success' => true, 'message' => 'User deleted successfully']);
            } else {
                $this->redirect('/app/users');
            }

        } catch (\Exception $e) {
            if (isAjax()) {
                $this->jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                $this->setFlashMessage('Failed to delete user: ' . $e->getMessage(), 'error');
                $this->redirect('/app/users');
            }
        }
    }

    /**
     * Check if current user can assign a specific role
     */
    private function canAssignRole($role)
    {
        if ($this->user['role'] === 'super_admin') {
            return true; // Super admin can assign any role
        }

        if ($this->user['role'] === 'company_admin') {
            // Company admin can assign roles below their level
            return in_array($role, ['manager', 'editor', 'viewer', 'client']);
        }

        return false;
    }

    /**
     * Get user by ID
     */
    private function getUserById($id)
    {
        return $this->db->fetch(
            "SELECT u.*, c.name as company_name
             FROM users u
             LEFT JOIN companies c ON u.company_id = c.id
             WHERE u.id = ?",
            [$id]
        );
    }

    /**
     * Get user statistics
     */
    private function getUserStats()
    {
        $stats = [];

        try {
            $whereClause = '';
            $params = [];

            // Limit to company for non-super admins
            if ($this->user['role'] !== 'super_admin') {
                $whereClause = 'WHERE company_id = ?';
                $params[] = $this->user['company_id'];
            }

            // Total users
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM users {$whereClause}", $params);
            $stats['total_users'] = $result['count'] ?? 0;

            // Active users
            $activeWhere = $whereClause ? $whereClause . ' AND status = \'active\'' : 'WHERE status = \'active\'';
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM users {$activeWhere}", $params);
            $stats['active_users'] = $result['count'] ?? 0;

            // Users by role
            $roleStats = $this->db->fetchAll(
                "SELECT role, COUNT(*) as count FROM users {$whereClause} GROUP BY role",
                $params
            );
            $stats['by_role'] = [];
            foreach ($roleStats as $row) {
                $stats['by_role'][$row['role']] = $row['count'];
            }

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get user activity by ID
     */
    private function getUserActivity($userId)
    {
        try {
            return $this->db->fetchAll(
                "SELECT al.*, u.first_name, u.last_name
                 FROM activity_logs al
                 LEFT JOIN users u ON al.user_id = u.id
                 WHERE al.user_id = ?
                 ORDER BY al.created_at DESC
                 LIMIT 20",
                [$userId]
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get user statistics by ID
     */
    private function getUserStatsById($userId)
    {
        $stats = [];

        try {
            // Documents created
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM documents WHERE created_by = ?",
                [$userId]
            );
            $stats['documents_created'] = $result['count'] ?? 0;

            // Last activity
            $result = $this->db->fetch(
                "SELECT MAX(created_at) as last_activity FROM activity_logs WHERE user_id = ?",
                [$userId]
            );
            $stats['last_activity'] = $result['last_activity'] ?? null;

            // Login count (approximate based on activity logs)
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM activity_logs WHERE user_id = ? AND action = 'login'",
                [$userId]
            );
            $stats['login_count'] = $result['count'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }
}
