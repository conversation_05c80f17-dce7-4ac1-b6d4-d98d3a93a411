<?php

namespace App\Controllers;

/**
 * Box Controller - Physical Storage Box Management
 * 
 * Implements Box Storage Process from documentation (Step 2)
 * Manages physical storage boxes with CLIENT01-BOX001 format
 */
class BoxController extends BaseController
{
    /**
     * Display boxes list
     */
    public function index()
    {
        $this->requireAuth();

        try {
            // Get filter parameters with smart defaults
            $page = max(1, (int)($_GET['page'] ?? 1));
            $perPage = min(100, max(10, (int)($_GET['per_page'] ?? 25)));
            $archiveStatus = $_GET['archive_status'] ?? 'active';
            $dateRange = $_GET['date_range'] ?? 'last_3_months';
            $searchTerm = $_GET['search'] ?? '';
            $storageType = $_GET['storage_type'] ?? '';
            $warehouseId = $_GET['warehouse_id'] ?? '';
            $status = $_GET['status'] ?? '';

            // Build WHERE clause with enhanced filtering
            $whereConditions = [
                "b.name NOT LIKE '[DELETED]%'",
                "b.company_id = ?"
            ];
            $params = [$this->user['company_id']];

            // Archive status filter
            if ($archiveStatus === 'archived') {
                $whereConditions[] = "b.archive_status IN ('archived', 'auto_archived')";
            } else {
                $whereConditions[] = "b.archive_status = 'active'";
            }

            // Date range filter (smart defaults)
            $dateCondition = $this->buildDateRangeCondition($dateRange);
            if ($dateCondition) {
                $whereConditions[] = $dateCondition['condition'];
                $params = array_merge($params, $dateCondition['params']);
            }

            // Search filter
            if (!empty($searchTerm)) {
                $whereConditions[] = "(b.name LIKE ? OR b.box_id LIKE ? OR b.storage_location_code LIKE ? OR w.name LIKE ?)";
                $searchParam = '%' . $searchTerm . '%';
                $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
            }

            // Storage type filter
            if (!empty($storageType) && $storageType !== 'all') {
                $whereConditions[] = "b.storage_type = ?";
                $params[] = $storageType;
            }

            // Warehouse filter
            if (!empty($warehouseId)) {
                $whereConditions[] = "b.warehouse_id = ?";
                $params[] = $warehouseId;
            }

            // Status filter
            if (!empty($status)) {
                $whereConditions[] = "b.status = ?";
                $params[] = $status;
            }

            $whereClause = "WHERE " . implode(" AND ", $whereConditions);

            // Get total count for pagination
            $totalCount = $this->db->fetchColumn(
                "SELECT COUNT(DISTINCT b.id) FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 {$whereClause}",
                $params
            );

            // Calculate pagination
            $totalPages = ceil($totalCount / $perPage);
            $offset = ($page - 1) * $perPage;

            // Get boxes with pagination
            $boxes = $this->db->fetchAll(
                "SELECT b.*, w.name as warehouse_name,
                        CONCAT(w.city, ', ', w.state) as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 {$whereClause}
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
                 LIMIT {$perPage} OFFSET {$offset}",
                $params
            );

            // Get archive statistics
            $archiveStats = $this->getArchiveStatistics();

            // Get warehouses for filter dropdown
            $warehouses = $this->db->fetchAll(
                "SELECT id, name FROM warehouses WHERE status = 'active' AND company_id = ? ORDER BY name",
                [$this->user['company_id']]
            );

            $this->view('boxes/index', [
                'title' => 'Box Management',
                'boxes' => $boxes,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_pages' => $totalPages,
                    'total_count' => $totalCount,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $totalPages
                ],
                'filters' => [
                    'archive_status' => $archiveStatus,
                    'date_range' => $dateRange,
                    'search' => $searchTerm,
                    'storage_type' => $storageType,
                    'warehouse_id' => $warehouseId,
                    'status' => $status
                ],
                'archive_stats' => $archiveStats,
                'warehouses' => $warehouses
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading boxes: ' . $e->getMessage(), 'error');
            $this->view('boxes/index', [
                'title' => 'Box Management',
                'boxes' => [],
                'pagination' => ['current_page' => 1, 'total_pages' => 1, 'total_count' => 0],
                'filters' => [],
                'archive_stats' => [],
                'warehouses' => []
            ]);
        }
    }

    /**
     * Get available boxes for intake assignment (AJAX)
     */
    public function getAvailable()
    {
        $this->requireAuth();

        header('Content-Type: application/json');

        try {
            // Get boxes with available capacity
            $boxes = $this->db->fetchAll(
                "SELECT b.id, b.box_id, b.name, b.capacity, b.storage_location_code,
                        COUNT(DISTINCT bb.bundle_id) as current_bundles,
                        ROUND(((b.capacity - COUNT(DISTINCT bb.bundle_id)) / b.capacity) * 100, 1) as available_capacity
                 FROM boxes b
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 WHERE b.status IN ('empty', 'active') AND b.name NOT LIKE '[DELETED]%' AND b.company_id = ?
                 GROUP BY b.id
                 HAVING available_capacity > 10
                 ORDER BY available_capacity DESC, b.created_at DESC",
                [$this->user['company_id']]
            );

            echo json_encode([
                'success' => true,
                'boxes' => $boxes
            ]);

        } catch (\Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to load available boxes: ' . $e->getMessage()
            ]);
        }
        exit;
    }

    /**
     * Show create box form
     */
    public function create()
    {
        $this->requireAuth();
        
        // Get available warehouses
        $warehouses = $this->db->fetchAll(
            "SELECT id, name, city, state FROM warehouses WHERE status = 'active' AND company_id = ? ORDER BY name",
            [$this->user['company_id']]
        );

        $this->view('boxes/create', [
            'title' => 'Create New Box',
            'warehouses' => $warehouses
        ]);
    }

    /**
     * Store new box
     */
    public function store()
    {
        $this->requireAuth();
        
        try {
            // Validate input
            $data = $this->validate($_POST, [
                'identifier' => 'required|max:255',
                'description' => 'max:1000',
                'warehouse_id' => 'required|integer',
                'storage_type' => 'required|in:physical,online,mixed',
                'capacity' => 'integer|min:1',
                'barcode' => 'max:100'
            ]);

            // Extract client prefix from identifier or use default
            $identifier = $data['identifier'];
            if (preg_match('/^([A-Z0-9]+)-/', $identifier, $matches)) {
                $clientPrefix = $matches[1];
            } else {
                $clientPrefix = 'CLIENT01'; // Default prefix
            }

            // Generate box ID according to documentation format (CLIENT01-BOX001)
            $boxNumber = $this->getNextBoxNumber($clientPrefix);
            $boxId = $clientPrefix . '-BOX' . str_pad($boxNumber, 3, '0', STR_PAD_LEFT);
            
            // Generate storage location code (WH-R1-S2-B03)
            $rowNumber = 'R1'; // Default row
            $shelfNumber = 'S1'; // Default shelf
            $positionNumber = 'B' . str_pad($boxNumber, 2, '0', STR_PAD_LEFT);
            $storageLocationCode = $this->generateStorageLocationCode(
                $data['warehouse_id'],
                $rowNumber,
                $shelfNumber,
                $positionNumber
            );

            // Check if box ID is unique
            $existing = $this->db->fetch(
                "SELECT id FROM boxes WHERE box_id = ?",
                [$boxId]
            );

            if ($existing) {
                throw new \Exception('Box ID already exists. Please try again.');
            }

            // Create box
            $newBoxId = $this->db->execute(
                "INSERT INTO boxes (
                    company_id, box_id, client_prefix, box_number, name, description,
                    warehouse_id, storage_type, storage_location_code, row_number, shelf_number, position_number,
                    capacity, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'empty', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $boxId,
                    $clientPrefix,
                    $boxNumber,
                    $identifier, // Use identifier as name
                    $data['description'] ?? null,
                    $data['warehouse_id'],
                    $data['storage_type'] ?? 'physical',
                    $storageLocationCode,
                    $rowNumber,
                    $shelfNumber,
                    $positionNumber,
                    $data['capacity'] ?? 100,
                    $this->user['id']
                ]
            );

            // Handle barcode
            if (!empty($data['barcode'])) {
                // Use provided barcode
                $this->db->execute(
                    "UPDATE boxes SET barcode_value = ?, barcode_generated_at = NOW() WHERE id = ?",
                    [$data['barcode'], $newBoxId]
                );
            } else {
                // Generate barcode for the box
                $this->generateBarcodeForBox($newBoxId, $boxId);
            }

            // Log activity
            $this->logActivity('create', 'box', $newBoxId, "Created box: {$boxId} at {$storageLocationCode}");

            $this->setFlashMessage('Box created successfully', 'success');
            $this->redirect('/app/boxes');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create box: ' . $e->getMessage(), 'error');
            $this->redirect('/app/boxes/create');
        }
    }

    /**
     * Show box details
     */
    public function show($id)
    {
        $this->requireAuth();

        try {
            $box = $this->getBoxById($id);
            if (!$box) {
                $this->setFlashMessage('Box not found', 'error');
                $this->redirect('/app/boxes');
                return;
            }

            // Get bundles in this box
            $bundles = $this->getBoxBundles($id);

            // Get box statistics
            $stats = $this->getBoxStats($id);

            // Get documents in this box (through bundles)
            $documents = $this->getBoxDocuments($id);

            // Get intake entries that led to this box
            $intakeHistory = $this->getBoxIntakeHistory($id);

            // Get activity timeline
            $timeline = $this->getBoxTimeline($id);

            // Get storage location details
            $storageDetails = $this->getBoxStorageDetails($id);

            $this->view('boxes/show', [
                'title' => $box['name'],
                'box' => $box,
                'bundles' => $bundles,
                'documents' => $documents,
                'stats' => $stats,
                'intakeHistory' => $intakeHistory,
                'timeline' => $timeline,
                'storageDetails' => $storageDetails
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading box: ' . $e->getMessage(), 'error');
            $this->redirect('/app/boxes');
        }
    }

    /**
     * Generate barcode for box
     */
    public function generateBarcode($id)
    {
        $this->requireAuth();
        
        try {
            $box = $this->getBoxById($id);
            if (!$box) {
                throw new \Exception('Box not found');
            }

            // Generate new barcode and QR code
            $this->generateBarcodeForBox($id, $box['box_id']);

            $this->setFlashMessage('New barcode generated successfully', 'success');
            $this->redirect("/app/boxes/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to generate barcode: ' . $e->getMessage(), 'error');
            $this->redirect("/app/boxes/{$id}");
        }
    }

    /**
     * Get next box number for client prefix
     */
    private function getNextBoxNumber($clientPrefix)
    {
        $result = $this->db->fetch(
            "SELECT COALESCE(MAX(box_number), 0) + 1 as next_number FROM boxes WHERE client_prefix = ?",
            [$clientPrefix]
        );
        
        return $result['next_number'] ?? 1;
    }

    /**
     * Generate storage location code (WH-R1-S2-B03)
     */
    private function generateStorageLocationCode($warehouseId, $row, $shelf, $position)
    {
        return "WH-{$row}-{$shelf}-{$position}";
    }

    /**
     * Generate barcode and QR code for box
     */
    private function generateBarcodeForBox($boxId, $boxIdentifier)
    {
        try {
            $barcodeValue = 'BOX-' . $boxIdentifier;
            $qrCodeValue = json_encode([
                'type' => 'box',
                'id' => $boxId,
                'box_id' => $boxIdentifier,
                'timestamp' => time()
            ]);

            // Update box with barcode information
            $this->db->execute(
                "UPDATE boxes SET 
                 barcode_value = ?, 
                 qr_code_value = ?, 
                 barcode_generated_at = NOW(), 
                 updated_at = NOW() 
                 WHERE id = ?",
                [$barcodeValue, $qrCodeValue, $boxId]
            );

        } catch (\Exception $e) {
            error_log("Failed to generate barcode for box {$boxId}: " . $e->getMessage());
        }
    }

    /**
     * Get box by ID with enhanced details
     */
    private function getBoxById($id)
    {
        $box = $this->db->fetch(
            "SELECT b.*, w.name as warehouse_name, w.address as warehouse_address,
                    w.city as warehouse_city, w.state as warehouse_state,
                    CONCAT(w.city, ', ', w.state) as warehouse_location,
                    u.first_name, u.last_name, c.name as company_name,
                    CASE
                        WHEN b.current_count >= b.capacity THEN 'full'
                        WHEN b.current_count > 0 THEN 'partial'
                        ELSE 'empty'
                    END as occupancy_status
             FROM boxes b
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN users u ON b.created_by = u.id
             LEFT JOIN companies c ON b.company_id = c.id
             WHERE b.id = ? AND b.company_id = ?",
            [$id, $this->user['company_id']]
        );

        // Ensure required fields have default values if missing
        if ($box) {
            $box['box_id'] = $box['box_id'] ?? 'BOX-' . str_pad($id, 3, '0', STR_PAD_LEFT);
            $box['storage_location_code'] = $box['storage_location_code'] ?? 'WH-R1-S1-B' . str_pad($id, 2, '0', STR_PAD_LEFT);
            $box['barcode_value'] = $box['barcode_value'] ?? $box['box_id'] . '-' . strtoupper(substr(md5($id), 0, 8));
            $box['row_number'] = $box['row_number'] ?? 'R1';
            $box['shelf_number'] = $box['shelf_number'] ?? 'S1';
            $box['position_number'] = $box['position_number'] ?? 'B' . str_pad($id, 2, '0', STR_PAD_LEFT);
        }

        return $box;
    }

    /**
     * Get bundles in box (REWRITTEN - Enhanced with proper junction table)
     */
    private function getBoxBundles($boxId)
    {
        return $this->db->fetchAll(
            "SELECT bun.*,
                    bb.position_in_box,
                    bb.added_at,
                    bb.added_by,
                    COUNT(DISTINCT d.id) as document_count,
                    SUM(d.file_size) as total_size,
                    u.first_name,
                    u.last_name,
                    u2.first_name as added_by_first_name,
                    u2.last_name as added_by_last_name
             FROM box_bundles bb
             JOIN bundles bun ON bb.bundle_id = bun.id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             LEFT JOIN users u ON bun.created_by = u.id
             LEFT JOIN users u2 ON bb.added_by = u2.id
             WHERE bb.box_id = ? AND bun.status IN ('active', 'open')
             GROUP BY bun.id, bb.position_in_box, bb.added_at, bb.added_by
             ORDER BY bb.position_in_box ASC, bb.added_at ASC",
            [$boxId]
        );
    }

    /**
     * Get box statistics (REWRITTEN - Accurate junction table queries)
     */
    private function getBoxStats($boxId)
    {
        $stats = [];

        try {
            // Bundle count from junction table
            $result = $this->db->fetch(
                "SELECT COUNT(DISTINCT bb.bundle_id) as count
                 FROM box_bundles bb
                 JOIN bundles bun ON bb.bundle_id = bun.id
                 WHERE bb.box_id = ? AND bun.status IN ('active', 'open')",
                [$boxId]
            );
            $stats['bundle_count'] = $result['count'] ?? 0;

            // Document count and total size through bundles
            $result = $this->db->fetch(
                "SELECT COUNT(DISTINCT d.id) as count,
                        SUM(d.file_size) as total_size,
                        COUNT(DISTINCT bb.bundle_id) as active_bundles
                 FROM box_bundles bb
                 JOIN bundles bun ON bb.bundle_id = bun.id AND bun.status IN ('active', 'open')
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 WHERE bb.box_id = ?",
                [$boxId]
            );
            $stats['document_count'] = $result['count'] ?? 0;
            $stats['total_size'] = $result['total_size'] ?? 0;
            $stats['active_bundles'] = $result['active_bundles'] ?? 0;

            // Get box capacity and calculate utilization
            $box = $this->db->fetch(
                "SELECT capacity, status FROM boxes WHERE id = ?",
                [$boxId]
            );
            $capacity = $box['capacity'] ?? 100;
            $stats['capacity'] = $capacity;
            $stats['utilization_percentage'] = $capacity > 0 ? min(100, ($stats['document_count'] / $capacity) * 100) : 0;
            $stats['remaining_capacity'] = max(0, $capacity - $stats['document_count']);
            $stats['box_status'] = $box['status'] ?? 'empty';

        } catch (\Exception $e) {
            // Return default stats on error
            $stats = [
                'bundle_count' => 0,
                'document_count' => 0,
                'total_size' => 0,
                'utilization_percentage' => 0,
                'remaining_capacity' => 100,
                'capacity' => 100,
                'box_status' => 'empty',
                'active_bundles' => 0
            ];
        }

        return $stats;
    }

    /**
     * Get documents in box (through bundles)
     */
    private function getBoxDocuments($boxId)
    {
        return $this->db->fetchAll(
            "SELECT d.*, bun.name as bundle_name, bun.reference_number as bundle_ref,
                    u.first_name, u.last_name,
                    CASE
                        WHEN d.mime_type LIKE 'image/%' THEN 'image'
                        WHEN d.mime_type LIKE 'application/pdf' THEN 'pdf'
                        WHEN d.mime_type LIKE 'application/msword%' OR d.mime_type LIKE 'application/vnd.openxmlformats-officedocument.wordprocessingml%' THEN 'document'
                        WHEN d.mime_type LIKE 'application/vnd.ms-excel%' OR d.mime_type LIKE 'application/vnd.openxmlformats-officedocument.spreadsheetml%' THEN 'spreadsheet'
                        ELSE 'file'
                    END as file_type_icon
             FROM box_bundles bb
             JOIN bundles bun ON bb.bundle_id = bun.id
             JOIN documents d ON bun.id = d.bundle_id
             LEFT JOIN users u ON d.created_by = u.id
             WHERE bb.box_id = ? AND d.status != 'deleted'
             ORDER BY d.created_at DESC",
            [$boxId]
        );
    }

    /**
     * Get intake history for this box
     */
    private function getBoxIntakeHistory($boxId)
    {
        return $this->db->fetchAll(
            "SELECT di.*, bun.name as bundle_name,
                    u.first_name, u.last_name
             FROM box_bundles bb
             JOIN bundles bun ON bb.bundle_id = bun.id
             JOIN document_intake di ON bun.id = di.bundle_id
             LEFT JOIN users u ON di.created_by = u.id
             WHERE bb.box_id = ?
             ORDER BY di.created_at DESC",
            [$boxId]
        );
    }

    /**
     * Get box timeline/activity
     */
    private function getBoxTimeline($boxId)
    {
        $timeline = [];

        try {
            // Box creation
            $boxCreation = $this->db->fetch(
                "SELECT b.created_at, u.first_name, u.last_name, 'box_created' as event_type
                 FROM boxes b
                 LEFT JOIN users u ON b.created_by = u.id
                 WHERE b.id = ?",
                [$boxId]
            );
            if ($boxCreation) {
                $timeline[] = $boxCreation;
            }

            // Bundle additions
            $bundleAdditions = $this->db->fetchAll(
                "SELECT bb.added_at as created_at, u.first_name, u.last_name,
                        'bundle_added' as event_type, bun.name as bundle_name
                 FROM box_bundles bb
                 JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN users u ON bb.added_by = u.id
                 WHERE bb.box_id = ?",
                [$boxId]
            );
            $timeline = array_merge($timeline, $bundleAdditions);

            // Document additions (through bundles)
            $documentAdditions = $this->db->fetchAll(
                "SELECT d.created_at, u.first_name, u.last_name,
                        'document_added' as event_type, d.title as document_title
                 FROM box_bundles bb
                 JOIN bundles bun ON bb.bundle_id = bun.id
                 JOIN documents d ON bun.id = d.bundle_id
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE bb.box_id = ? AND d.status != 'deleted'",
                [$boxId]
            );
            $timeline = array_merge($timeline, $documentAdditions);

            // Sort by date
            usort($timeline, function($a, $b) {
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });

        } catch (\Exception $e) {
            // Return empty timeline on error
        }

        return array_slice($timeline, 0, 20); // Limit to 20 most recent events
    }

    /**
     * Get storage location details
     */
    private function getBoxStorageDetails($boxId)
    {
        return $this->db->fetch(
            "SELECT b.storage_location_code, b.row_number, b.shelf_number, b.position_number,
                    w.name as warehouse_name, w.address as warehouse_address,
                    w.city as warehouse_city, w.state as warehouse_state,
                    w.zip_code as warehouse_zip, w.country as warehouse_country
             FROM boxes b
             JOIN warehouses w ON b.warehouse_id = w.id
             WHERE b.id = ?",
            [$boxId]
        );
    }

    /**
     * Build date range condition for filtering
     */
    private function buildDateRangeCondition($dateRange)
    {
        switch ($dateRange) {
            case 'today':
                return [
                    'condition' => 'DATE(b.created_at) = CURDATE()',
                    'params' => []
                ];
            case 'last_7_days':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
                    'params' => []
                ];
            case 'last_30_days':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
                    'params' => []
                ];
            case 'last_3_months':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)',
                    'params' => []
                ];
            case 'last_6_months':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)',
                    'params' => []
                ];
            case 'last_year':
                return [
                    'condition' => 'b.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)',
                    'params' => []
                ];
            case 'this_year':
                return [
                    'condition' => 'YEAR(b.created_at) = YEAR(NOW())',
                    'params' => []
                ];
            case 'custom':
                $startDate = $_GET['start_date'] ?? '';
                $endDate = $_GET['end_date'] ?? '';
                if ($startDate && $endDate) {
                    return [
                        'condition' => 'DATE(b.created_at) BETWEEN ? AND ?',
                        'params' => [$startDate, $endDate]
                    ];
                }
                break;
            case 'all':
            default:
                return null;
        }
        return null;
    }

    /**
     * Get archive statistics
     */
    private function getArchiveStatistics()
    {
        try {
            return $this->db->fetch(
                "SELECT
                    COUNT(CASE WHEN archive_status = 'active' THEN 1 END) as active_count,
                    COUNT(CASE WHEN archive_status = 'archived' THEN 1 END) as archived_count,
                    COUNT(CASE WHEN archive_status = 'auto_archived' THEN 1 END) as auto_archived_count,
                    COUNT(*) as total_count
                 FROM boxes
                 WHERE company_id = ?",
                [$this->user['company_id']]
            );
        } catch (\Exception $e) {
            return [
                'active_count' => 0,
                'archived_count' => 0,
                'auto_archived_count' => 0,
                'total_count' => 0
            ];
        }
    }

    /**
     * Archive box
     */
    public function archive($id)
    {
        $this->requireAuth();

        try {
            $box = $this->getBoxById($id);
            if (!$box) {
                throw new \Exception('Box not found');
            }

            $this->db->execute(
                "UPDATE boxes SET archive_status = 'archived', last_activity_at = NOW() WHERE id = ?",
                [$id]
            );

            $this->logActivity('archive', 'box', $id, "Archived box: {$box['name']}");
            $this->setFlashMessage('Box archived successfully', 'success');

        } catch (\Exception $e) {
            $this->setFlashMessage('Archive failed: ' . $e->getMessage(), 'error');
        }

        $this->redirect('/app/boxes');
    }

    /**
     * Unarchive box
     */
    public function unarchive($id)
    {
        $this->requireAuth();

        try {
            $box = $this->getBoxById($id);
            if (!$box) {
                throw new \Exception('Box not found');
            }

            $this->db->execute(
                "UPDATE boxes SET archive_status = 'active', last_activity_at = NOW() WHERE id = ?",
                [$id]
            );

            $this->logActivity('unarchive', 'box', $id, "Unarchived box: {$box['name']}");
            $this->setFlashMessage('Box unarchived successfully', 'success');

        } catch (\Exception $e) {
            $this->setFlashMessage('Unarchive failed: ' . $e->getMessage(), 'error');
        }

        $this->redirect('/app/boxes');
    }

    /**
     * Delete box (soft delete)
     */
    public function delete($id)
    {
        $this->requireAuth();

        try {
            // Get box details for validation
            $box = $this->db->fetch(
                "SELECT * FROM boxes WHERE id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            if (!$box) {
                throw new \Exception('Box not found or unauthorized access');
            }

            // Check if box has bundles
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM box_bundles WHERE box_id = ?",
                [$id]
            );
            $bundleCount = $result['count'] ?? 0;

            if ($bundleCount > 0) {
                throw new \Exception("Cannot delete box. It contains {$bundleCount} bundle(s). Please move or delete them first.");
            }

            // Soft delete the box
            $this->db->execute(
                "UPDATE boxes SET name = CONCAT('[DELETED] ', name), updated_at = NOW() WHERE id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Log activity
            $this->logActivity('delete', 'box', $id, "Deleted box: {$box['box_id']}");

            $this->setFlashMessage('Box deleted successfully', 'success');
            $this->redirect('/app/boxes');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to delete box: ' . $e->getMessage(), 'error');
            $this->redirect('/app/boxes');
        }
    }
}
