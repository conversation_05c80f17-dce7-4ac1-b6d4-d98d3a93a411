<?php
require_once 'src/autoload.php';

use App\Core\Database;

$db = Database::getInstance();

echo "Running new migrations...\n";

// Run boxes table migration
try {
    $sql = file_get_contents('database/migrations/017_create_boxes_table.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $db->exec($statement);
                echo "✓ Executed statement successfully\n";
            } catch (Exception $e) {
                echo "✗ Statement failed: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "Boxes table migration completed!\n";
} catch (Exception $e) {
    echo "Migration error: " . $e->getMessage() . "\n";
}

// Run bundles update migration
try {
    $sql = file_get_contents('database/migrations/018_update_bundles_for_documentation.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $db->exec($statement);
                echo "✓ Executed bundles update statement successfully\n";
            } catch (Exception $e) {
                echo "✗ Bundles update statement failed: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "Bundles update migration completed!\n";
} catch (Exception $e) {
    echo "Bundles migration error: " . $e->getMessage() . "\n";
}

echo "All migrations completed!\n";
?>
