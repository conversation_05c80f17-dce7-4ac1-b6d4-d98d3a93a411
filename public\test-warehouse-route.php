<?php
/**
 * Test warehouse route registration
 */

// Include the bootstrap
require_once __DIR__ . '/../bootstrap.php';

// Create router instance
$router = new App\Core\Router();

// Load routes
require_once APP_ROOT . '/src/routes.php';

// Test the specific route
$method = 'POST';
$uri = '/app/warehouse-ajax-create';

echo "<h2>Testing Route: {$method} {$uri}</h2>";

// Get all routes
$routes = $router->getRoutes();

echo "<h3>All registered routes:</h3>";
echo "<ul>";
foreach ($routes as $route) {
    if ($route['method'] === 'POST' && strpos($route['path'], 'warehouse') !== false) {
        echo "<li><strong>{$route['method']}</strong> {$route['path']} → {$route['handler']}</li>";
    }
}
echo "</ul>";

// Test route matching
echo "<h3>Route matching test:</h3>";
foreach ($routes as $route) {
    if ($route['method'] === $method) {
        echo "<p>Testing pattern: {$route['pattern']} against {$uri}</p>";
        if (preg_match($route['pattern'], $uri, $matches)) {
            echo "<p style='color: green;'>✓ MATCH FOUND: {$route['path']} → {$route['handler']}</p>";
            break;
        }
    }
}

echo "<h3>Debug route patterns:</h3>";
foreach ($routes as $route) {
    if ($route['method'] === 'POST' && strpos($route['path'], 'warehouse') !== false) {
        echo "<p>{$route['path']} → Pattern: {$route['pattern']}</p>";
    }
}
?>
