<?php
$title = $title ?? 'Real-time System Monitoring';
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
    
    <!-- Header -->
    <div class="bg-white/90 backdrop-blur-sm border-b border-white/30 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Real-time System Monitoring</h1>
                    <p class="text-gray-600 mt-1">Live system health, performance metrics, and intelligent alerts</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">System Status</p>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-<?= $systemHealth['overall_status'] === 'healthy' ? 'green' : ($systemHealth['overall_status'] === 'warning' ? 'yellow' : 'red') ?>-500 rounded-full animate-pulse"></div>
                            <p class="font-medium text-<?= $systemHealth['overall_status'] === 'healthy' ? 'green' : ($systemHealth['overall_status'] === 'warning' ? 'yellow' : 'red') ?>-600">
                                <?= ucfirst($systemHealth['overall_status']) ?>
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Uptime</p>
                        <p class="font-medium text-green-600"><?= $systemHealth['uptime'] ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Quick Actions -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="<?= url('/super-admin/system-monitoring/performance') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Performance Analytics
                </a>
                
                <a href="<?= url('/super-admin/system-monitoring/security') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    Security Monitoring
                </a>
                
                <a href="<?= url('/super-admin/system-monitoring/alerts') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.343 4.343l1.414 1.414m0 0l7.071 7.071m0 0l7.071-7.071m0 0l1.414-1.414M4.343 4.343L12 12l7.657-7.657M12 12l-7.657 7.657"></path>
                    </svg>
                    Alert Management
                    <?php if (count($activeAlerts) > 0): ?>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <?= count($activeAlerts) ?>
                        </span>
                    <?php endif; ?>
                </a>
                
                <button onclick="refreshSystemHealth()" 
                        class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>

        <!-- System Health Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Database Health -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-<?= $systemHealth['database']['status'] === 'healthy' ? 'green' : ($systemHealth['database']['status'] === 'warning' ? 'yellow' : 'red') ?>-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-<?= $systemHealth['database']['status'] === 'healthy' ? 'green' : ($systemHealth['database']['status'] === 'warning' ? 'yellow' : 'red') ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Database</p>
                        <p class="text-2xl font-bold text-gray-900"><?= $systemHealth['database']['response_time'] ?? 'N/A' ?>ms</p>
                        <p class="text-xs text-<?= $systemHealth['database']['status'] === 'healthy' ? 'green' : ($systemHealth['database']['status'] === 'warning' ? 'yellow' : 'red') ?>-600">
                            <?= ucfirst($systemHealth['database']['status']) ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Storage Health -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-<?= $systemHealth['storage']['status'] === 'healthy' ? 'green' : ($systemHealth['storage']['status'] === 'warning' ? 'yellow' : 'red') ?>-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-<?= $systemHealth['storage']['status'] === 'healthy' ? 'green' : ($systemHealth['storage']['status'] === 'warning' ? 'yellow' : 'red') ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Storage</p>
                        <p class="text-2xl font-bold text-gray-900"><?= $systemHealth['storage']['usage_percentage'] ?? 'N/A' ?>%</p>
                        <p class="text-xs text-<?= $systemHealth['storage']['status'] === 'healthy' ? 'green' : ($systemHealth['storage']['status'] === 'warning' ? 'yellow' : 'red') ?>-600">
                            <?= $systemHealth['storage']['free_space_gb'] ?? 'N/A' ?>GB free
                        </p>
                    </div>
                </div>
            </div>

            <!-- Application Health -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-<?= $systemHealth['application']['status'] === 'healthy' ? 'green' : ($systemHealth['application']['status'] === 'warning' ? 'yellow' : 'red') ?>-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-<?= $systemHealth['application']['status'] === 'healthy' ? 'green' : ($systemHealth['application']['status'] === 'warning' ? 'yellow' : 'red') ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Application</p>
                        <p class="text-2xl font-bold text-gray-900"><?= $systemHealth['application']['memory_percentage'] ?? 'N/A' ?>%</p>
                        <p class="text-xs text-<?= $systemHealth['application']['status'] === 'healthy' ? 'green' : ($systemHealth['application']['status'] === 'warning' ? 'yellow' : 'red') ?>-600">
                            Memory usage
                        </p>
                    </div>
                </div>
            </div>

            <!-- Active Alerts -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-<?= count($activeAlerts) > 0 ? 'red' : 'green' ?>-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-<?= count($activeAlerts) > 0 ? 'red' : 'green' ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <?php if (count($activeAlerts) > 0): ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                <?php else: ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                <?php endif; ?>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Alerts</p>
                        <p class="text-2xl font-bold text-gray-900"><?= count($activeAlerts) ?></p>
                        <p class="text-xs text-<?= count($activeAlerts) > 0 ? 'red' : 'green' ?>-600">
                            <?= count($activeAlerts) > 0 ? 'Needs attention' : 'All clear' ?>
                        </p>
                    </div>
                </div>
            </div>

        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Performance Metrics -->
            <div class="lg:col-span-2 bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Real-time Performance Metrics</h3>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-500">Live</span>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Metric</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Current Value</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Last Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($performanceMetrics, 0, 10) as $metric): ?>
                                <tr class="border-b border-gray-100 hover:bg-gray-50">
                                    <td class="py-3 px-4 font-medium text-gray-900"><?= e($metric['metric_name']) ?></td>
                                    <td class="py-3 px-4 text-sm text-gray-900">
                                        <?= number_format($metric['metric_value'], 2) ?> <?= e($metric['metric_unit']) ?>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $metric['status'] === 'normal' ? 'green' : ($metric['status'] === 'warning' ? 'yellow' : 'red') ?>-100 text-<?= $metric['status'] === 'normal' ? 'green' : ($metric['status'] === 'warning' ? 'yellow' : 'red') ?>-800">
                                            <?= ucfirst($metric['status']) ?>
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 text-sm text-gray-500">
                                        <?= date('H:i:s', strtotime($metric['recorded_at'])) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- System Alerts & Resource Usage -->
            <div class="space-y-6">
                
                <!-- Active System Alerts -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">System Alerts</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= count($activeAlerts) > 0 ? 'red' : 'green' ?>-100 text-<?= count($activeAlerts) > 0 ? 'red' : 'green' ?>-800">
                            <?= count($activeAlerts) ?> active
                        </span>
                    </div>
                    
                    <div class="space-y-3">
                        <?php if (!empty($activeAlerts)): ?>
                            <?php foreach (array_slice($activeAlerts, 0, 5) as $alert): ?>
                                <div class="flex items-start p-3 bg-<?= $alert['severity'] === 'critical' ? 'red' : ($alert['severity'] === 'high' ? 'orange' : 'yellow') ?>-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <svg class="w-5 h-5 text-<?= $alert['severity'] === 'critical' ? 'red' : ($alert['severity'] === 'high' ? 'orange' : 'yellow') ?>-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <h4 class="text-sm font-medium text-gray-900"><?= e($alert['title']) ?></h4>
                                        <p class="text-sm text-gray-600 mt-1"><?= e($alert['message']) ?></p>
                                        <p class="text-xs text-gray-500 mt-1"><?= date('M j, H:i', strtotime($alert['created_at'])) ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <p class="text-gray-500 text-sm">No active alerts</p>
                                <p class="text-gray-400 text-xs">System is running smoothly</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mt-4">
                        <a href="<?= url('/super-admin/system-monitoring/alerts') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Manage all alerts →
                        </a>
                    </div>
                </div>

                <!-- Resource Utilization -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Resource Utilization</h3>
                    
                    <div class="space-y-4">
                        <!-- Database Connections -->
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Database Connections</span>
                                <span class="text-gray-900"><?= $systemHealth['database']['connections'] ?? 0 ?>/<?= $systemHealth['database']['max_connections'] ?? 100 ?></span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <?php $dbUsage = ($systemHealth['database']['connections'] ?? 0) / ($systemHealth['database']['max_connections'] ?? 100) * 100; ?>
                                <div class="bg-<?= $dbUsage > 80 ? 'red' : ($dbUsage > 60 ? 'yellow' : 'green') ?>-500 h-2 rounded-full" style="width: <?= $dbUsage ?>%"></div>
                            </div>
                        </div>
                        
                        <!-- Memory Usage -->
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Memory Usage</span>
                                <span class="text-gray-900"><?= $systemHealth['application']['memory_usage_mb'] ?? 0 ?>MB</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-<?= ($systemHealth['application']['memory_percentage'] ?? 0) > 80 ? 'red' : (($systemHealth['application']['memory_percentage'] ?? 0) > 60 ? 'yellow' : 'green') ?>-500 h-2 rounded-full" style="width: <?= $systemHealth['application']['memory_percentage'] ?? 0 ?>%"></div>
                            </div>
                        </div>
                        
                        <!-- Storage Usage -->
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-600">Storage Usage</span>
                                <span class="text-gray-900"><?= $systemHealth['storage']['used_space_gb'] ?? 0 ?>GB</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-<?= ($systemHealth['storage']['usage_percentage'] ?? 0) > 80 ? 'red' : (($systemHealth['storage']['usage_percentage'] ?? 0) > 60 ? 'yellow' : 'green') ?>-500 h-2 rounded-full" style="width: <?= $systemHealth['storage']['usage_percentage'] ?? 0 ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>
</div>

<script>
// Auto-refresh system health every 30 seconds
setInterval(refreshSystemHealth, 30000);

function refreshSystemHealth() {
    fetch('<?= url('/super-admin/system-monitoring/health-api') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the page with new data
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error refreshing system health:', error);
        });
}

// Initialize real-time updates
document.addEventListener('DOMContentLoaded', function() {
    // Add any additional JavaScript for real-time features
    console.log('System monitoring dashboard initialized');
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
