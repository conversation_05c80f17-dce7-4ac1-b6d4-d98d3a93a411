<?php
$title = 'Edit Box - ' . ($box['name'] ?? 'Box');
ob_start();
?>

<!-- Edit Box Form -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- <PERSON>er -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        Edit Box
                    </h1>
                    <p class="text-gray-600 mt-2">Update storage container information</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/boxes/' . $box['id']) ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Box
                    </a>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
            <form action="<?= url('/app/boxes/' . $box['id']) ?>" method="POST" class="space-y-6">
                <input type="hidden" name="_method" value="PUT">
                
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Box Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Box Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="<?= e($box['name']) ?>"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="e.g., Box A-001">
                    </div>

                    <!-- Box Identifier -->
                    <div>
                        <label for="identifier" class="block text-sm font-medium text-gray-700 mb-2">
                            Box Identifier <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="identifier" 
                               name="identifier" 
                               value="<?= e($box['identifier']) ?>"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="e.g., BOX-A001">
                    </div>

                </div>

                <!-- Warehouse and Storage Type -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Warehouse -->
                    <div>
                        <label for="warehouse_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Warehouse <span class="text-red-500">*</span>
                        </label>
                        <select id="warehouse_id" 
                                name="warehouse_id" 
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="">Select a warehouse</option>
                            <?php foreach ($warehouses as $warehouse): ?>
                                <option value="<?= $warehouse['id'] ?>" <?= $warehouse['id'] == $box['warehouse_id'] ? 'selected' : '' ?>>
                                    <?= e($warehouse['name']) ?> - <?= e($warehouse['city']) ?>, <?= e($warehouse['state']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Storage Type -->
                    <div>
                        <label for="storage_type" class="block text-sm font-medium text-gray-700 mb-2">
                            Storage Type <span class="text-red-500">*</span>
                        </label>
                        <select id="storage_type" 
                                name="storage_type" 
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="">Select storage type</option>
                            <option value="physical" <?= $box['storage_type'] === 'physical' ? 'selected' : '' ?>>Physical Storage</option>
                            <option value="online" <?= $box['storage_type'] === 'online' ? 'selected' : '' ?>>Online Storage</option>
                        </select>
                    </div>

                </div>

                <!-- Capacity and Barcode -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <!-- Capacity -->
                    <div>
                        <label for="capacity" class="block text-sm font-medium text-gray-700 mb-2">
                            Capacity (number of documents)
                        </label>
                        <input type="number" 
                               id="capacity" 
                               name="capacity" 
                               value="<?= e($box['capacity']) ?>"
                               min="1"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                               placeholder="100">
                    </div>

                    <!-- Barcode -->
                    <div>
                        <label for="barcode" class="block text-sm font-medium text-gray-700 mb-2">
                            Barcode
                        </label>
                        <div class="flex space-x-2">
                            <input type="text" 
                                   id="barcode" 
                                   name="barcode" 
                                   value="<?= e($box['barcode']) ?>"
                                   class="flex-1 px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="Barcode value">
                            <a href="<?= url('/app/boxes/' . $box['id'] . '/generate-barcode') ?>" 
                               class="px-4 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors whitespace-nowrap">
                                Generate New
                            </a>
                        </div>
                    </div>

                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              placeholder="Optional description for this storage box..."><?= e($box['description'] ?? '') ?></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-4">
                        <a href="<?= url('/app/boxes/' . $box['id']) ?>" 
                           class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update Box
                        </button>
                    </div>
                    
                    <!-- Delete Button -->
                    <button type="button" 
                            onclick="confirmDelete()"
                            class="px-6 py-3 bg-red-600 text-white font-medium rounded-xl hover:bg-red-700 transition-colors">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete Box
                    </button>
                </div>

            </form>
        </div>

        <!-- Box Information -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">Box Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800">
                <div>
                    <h4 class="font-medium mb-1">Created:</h4>
                    <p><?= date('M j, Y g:i A', strtotime($box['created_at'])) ?></p>
                    <?php if (!empty($box['first_name']) || !empty($box['last_name'])): ?>
                        <p>by <?= e($box['first_name'] . ' ' . $box['last_name']) ?></p>
                    <?php endif; ?>
                </div>
                <div>
                    <h4 class="font-medium mb-1">Last Updated:</h4>
                    <p><?= date('M j, Y g:i A', strtotime($box['updated_at'])) ?></p>
                </div>
                <div>
                    <h4 class="font-medium mb-1">Status:</h4>
                    <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-green-100 text-green-800">
                        <?= ucfirst($box['status']) ?>
                    </span>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-8 max-w-md mx-4">
        <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete Box</h3>
            <p class="text-gray-600 mb-6">Are you sure you want to delete this box? This action cannot be undone.</p>
            <div class="flex items-center justify-center space-x-4">
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <form action="<?= url('/app/boxes/' . $box['id']) ?>" method="POST" class="inline">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" 
                            class="px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors">
                        Delete Box
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    document.getElementById('deleteModal').classList.remove('hidden');
    document.getElementById('deleteModal').classList.add('flex');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    document.getElementById('deleteModal').classList.remove('flex');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
