<?php
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="container mx-auto px-6 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        Edit Storage Location
                    </h1>
                    <p class="text-gray-600">Update <strong><?= e($location['name']) ?></strong> in <?= e($location['warehouse_name']) ?></p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/locations/' . $location['id']) ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Location
                    </a>
                </div>
            </div>
        </div>

        <!-- Warehouse Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-900"><?= e($location['warehouse_name']) ?></h3>
                    <p class="text-blue-700 text-sm">Warehouse</p>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white rounded-2xl shadow-lg p-8">
            <form action="<?= url('/app/locations/' . $location['id']) ?>" method="POST" class="space-y-6">
                <input type="hidden" name="_method" value="PUT">
                
                <!-- Location Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-semibold text-gray-800 mb-2">
                            Location Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="name" name="name" required
                               value="<?= e($location['name']) ?>"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="e.g., Shelf A-1, Room 101, Aisle 3">
                        <p class="text-sm text-gray-600 mt-1">Unique name for this storage location</p>
                    </div>

                    <div>
                        <label for="location_type" class="block text-sm font-semibold text-gray-800 mb-2">
                            Location Type
                        </label>
                        <select id="location_type" name="location_type"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="shelf" <?= ($location['type'] == 'shelf') ? 'selected' : '' ?>>📚 Shelf</option>
                            <option value="room" <?= ($location['type'] == 'room') ? 'selected' : '' ?>>🏠 Room</option>
                            <option value="aisle" <?= ($location['type'] == 'aisle') ? 'selected' : '' ?>>🛤️ Aisle</option>
                            <option value="cabinet" <?= ($location['type'] == 'cabinet') ? 'selected' : '' ?>>🗄️ Cabinet</option>
                            <option value="vault" <?= ($location['type'] == 'vault') ? 'selected' : '' ?>>🔒 Vault</option>
                            <option value="box" <?= ($location['type'] == 'box') ? 'selected' : '' ?>>📦 Box</option>
                            <option value="rack" <?= ($location['type'] == 'rack') ? 'selected' : '' ?>>🏗️ Rack</option>
                            <option value="floor" <?= ($location['type'] == 'floor') ? 'selected' : '' ?>>🏢 Floor</option>
                            <option value="building" <?= ($location['type'] == 'building') ? 'selected' : '' ?>>🏭 Building</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="capacity" class="block text-sm font-semibold text-gray-800 mb-2">
                            Capacity (Number of Documents)
                        </label>
                        <input type="number" id="capacity" name="capacity" min="1" 
                               value="<?= e($location['capacity'] ?? 100) ?>"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="100">
                        <p class="text-sm text-gray-600 mt-1">Maximum number of documents this location can hold</p>
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-semibold text-gray-800 mb-2">
                            Status
                        </label>
                        <select id="status" name="status"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="active" <?= ($location['status'] == 'active') ? 'selected' : '' ?>>✅ Active</option>
                            <option value="maintenance" <?= ($location['status'] == 'maintenance') ? 'selected' : '' ?>>🔧 Under Maintenance</option>
                            <option value="full" <?= ($location['status'] == 'full') ? 'selected' : '' ?>>📦 Full</option>
                            <option value="inactive" <?= ($location['status'] == 'inactive') ? 'selected' : '' ?>>❌ Inactive</option>
                        </select>
                    </div>
                </div>

                <!-- Current Identifier (Read-only) -->
                <div>
                    <label for="identifier" class="block text-sm font-semibold text-gray-800 mb-2">
                        Location Identifier
                    </label>
                    <input type="text" id="identifier" name="identifier" readonly
                           value="<?= e($location['identifier']) ?>"
                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl bg-gray-50 text-gray-600"
                           placeholder="Auto-generated">
                    <p class="text-sm text-gray-600 mt-1">System-generated unique identifier (cannot be changed)</p>
                </div>

                <div>
                    <label for="description" class="block text-sm font-semibold text-gray-800 mb-2">
                        Description
                    </label>
                    <textarea id="description" name="description" rows="4"
                              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none"
                              placeholder="Optional description of the storage location, access instructions, or special notes..."><?= e($location['description'] ?? '') ?></textarea>
                </div>

                <!-- Current Usage Info -->
                <?php if (($location['used_capacity'] ?? 0) > 0): ?>
                    <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                        <h3 class="font-semibold text-blue-900 mb-2">Current Usage</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-blue-700">Used Capacity:</span>
                                <span class="font-semibold text-blue-900"><?= number_format($location['used_capacity'] ?? 0, 2) ?></span>
                            </div>
                            <div>
                                <span class="text-blue-700">Total Capacity:</span>
                                <span class="font-semibold text-blue-900"><?= number_format($location['capacity'] ?? 0) ?></span>
                            </div>
                            <div>
                                <span class="text-blue-700">Utilization:</span>
                                <span class="font-semibold text-blue-900">
                                    <?= $location['capacity'] > 0 ? number_format((($location['used_capacity'] ?? 0) / $location['capacity']) * 100, 1) : 0 ?>%
                                </span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Form Actions -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-4">
                        <a href="<?= url('/app/locations/' . $location['id']) ?>" 
                           class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                            Cancel
                        </a>
                        <button type="submit"
                                class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update Location
                        </button>
                    </div>
                    
                    <!-- Delete Button -->
                    <button type="button" onclick="confirmDelete()" 
                            class="px-6 py-3 bg-red-600 text-white font-medium rounded-xl hover:bg-red-700 transition-colors">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-6 max-w-md mx-4">
        <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete Storage Location</h3>
            <p class="text-gray-600 mb-6">Are you sure you want to delete this storage location? This action cannot be undone.</p>
            <div class="flex items-center justify-center space-x-4">
                <button onclick="hideDeleteModal()" class="px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <form action="<?= url('/app/locations/' . $location['id']) ?>" method="POST" class="inline">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('Please enter a location name.');
        document.getElementById('name').focus();
        return false;
    }
    
    return true;
});

// Delete confirmation
function confirmDelete() {
    document.getElementById('deleteModal').classList.remove('hidden');
    document.getElementById('deleteModal').classList.add('flex');
}

function hideDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    document.getElementById('deleteModal').classList.remove('flex');
}

// Auto-focus on name field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('name').focus();
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
