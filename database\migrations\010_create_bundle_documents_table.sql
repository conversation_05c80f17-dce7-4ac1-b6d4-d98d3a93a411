-- Create bundle_documents table
CREATE TABLE bundle_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bundle_id INT NOT NULL,
    document_id INT NOT NULL,
    added_by INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sort_order INT DEFAULT 0,
    
    FOREIGN KEY (bundle_id) REFERENCES bundles(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id),
    
    UNIQUE KEY unique_bundle_document (bundle_id, document_id),
    INDEX idx_bundle_docs_bundle (bundle_id),
    INDEX idx_bundle_docs_document (document_id),
    INDEX idx_bundle_docs_added (added_at),
    INDEX idx_bundle_docs_sort (sort_order)
);
