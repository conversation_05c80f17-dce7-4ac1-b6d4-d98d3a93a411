<?php
$title = 'System Settings';
ob_start();
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">System Settings</h1>
                <p class="text-gray-600 mt-1">Configure global system settings and preferences</p>
            </div>
            <a href="<?= url('/super-admin/dashboard') ?>" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <form method="POST" action="<?= url('/super-admin/settings') ?>" class="space-y-8">
        
        <!-- General Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">General Settings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="app_name" class="block text-sm font-medium text-gray-700 mb-2">Application Name</label>
                    <input type="text" 
                           id="app_name" 
                           name="app_name" 
                           value="<?= e($settings['app_name'] ?? 'Document Management System') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="app_url" class="block text-sm font-medium text-gray-700 mb-2">Application URL</label>
                    <input type="url" 
                           id="app_url" 
                           name="app_url" 
                           value="<?= e($settings['app_url'] ?? 'http://localhost/dms') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                    <input type="email" 
                           id="admin_email" 
                           name="admin_email" 
                           value="<?= e($settings['admin_email'] ?? '<EMAIL>') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">Default Timezone</label>
                    <select id="timezone" 
                            name="timezone"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="UTC" <?= ($settings['timezone'] ?? 'UTC') === 'UTC' ? 'selected' : '' ?>>UTC</option>
                        <option value="America/New_York" <?= ($settings['timezone'] ?? '') === 'America/New_York' ? 'selected' : '' ?>>Eastern Time</option>
                        <option value="America/Chicago" <?= ($settings['timezone'] ?? '') === 'America/Chicago' ? 'selected' : '' ?>>Central Time</option>
                        <option value="America/Denver" <?= ($settings['timezone'] ?? '') === 'America/Denver' ? 'selected' : '' ?>>Mountain Time</option>
                        <option value="America/Los_Angeles" <?= ($settings['timezone'] ?? '') === 'America/Los_Angeles' ? 'selected' : '' ?>>Pacific Time</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Storage Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Storage Settings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="default_storage_limit" class="block text-sm font-medium text-gray-700 mb-2">Default Storage Limit (GB)</label>
                    <input type="number" 
                           id="default_storage_limit" 
                           name="default_storage_limit" 
                           value="<?= e($settings['default_storage_limit'] ?? '10') ?>"
                           min="1"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="max_file_size" class="block text-sm font-medium text-gray-700 mb-2">Max File Size (MB)</label>
                    <input type="number" 
                           id="max_file_size" 
                           name="max_file_size" 
                           value="<?= e($settings['max_file_size'] ?? '100') ?>"
                           min="1"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="storage_path" class="block text-sm font-medium text-gray-700 mb-2">Storage Path</label>
                    <input type="text" 
                           id="storage_path" 
                           name="storage_path" 
                           value="<?= e($settings['storage_path'] ?? '/storage/documents') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="backup_enabled" class="block text-sm font-medium text-gray-700 mb-2">Automatic Backups</label>
                    <select id="backup_enabled" 
                            name="backup_enabled"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="1" <?= ($settings['backup_enabled'] ?? '1') === '1' ? 'selected' : '' ?>>Enabled</option>
                        <option value="0" <?= ($settings['backup_enabled'] ?? '') === '0' ? 'selected' : '' ?>>Disabled</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Security Settings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="session_timeout" class="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                    <input type="number" 
                           id="session_timeout" 
                           name="session_timeout" 
                           value="<?= e($settings['session_timeout'] ?? '60') ?>"
                           min="5"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="password_min_length" class="block text-sm font-medium text-gray-700 mb-2">Minimum Password Length</label>
                    <input type="number" 
                           id="password_min_length" 
                           name="password_min_length" 
                           value="<?= e($settings['password_min_length'] ?? '8') ?>"
                           min="6"
                           max="20"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="two_factor_enabled" class="block text-sm font-medium text-gray-700 mb-2">Two-Factor Authentication</label>
                    <select id="two_factor_enabled" 
                            name="two_factor_enabled"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="0" <?= ($settings['two_factor_enabled'] ?? '0') === '0' ? 'selected' : '' ?>>Disabled</option>
                        <option value="1" <?= ($settings['two_factor_enabled'] ?? '') === '1' ? 'selected' : '' ?>>Enabled</option>
                    </select>
                </div>

                <div>
                    <label for="login_attempts" class="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
                    <input type="number" 
                           id="login_attempts" 
                           name="login_attempts" 
                           value="<?= e($settings['login_attempts'] ?? '5') ?>"
                           min="3"
                           max="10"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Email Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Email Settings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="smtp_host" class="block text-sm font-medium text-gray-700 mb-2">SMTP Host</label>
                    <input type="text" 
                           id="smtp_host" 
                           name="smtp_host" 
                           value="<?= e($settings['smtp_host'] ?? '') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="smtp.example.com">
                </div>

                <div>
                    <label for="smtp_port" class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                    <input type="number" 
                           id="smtp_port" 
                           name="smtp_port" 
                           value="<?= e($settings['smtp_port'] ?? '587') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="smtp_username" class="block text-sm font-medium text-gray-700 mb-2">SMTP Username</label>
                    <input type="text" 
                           id="smtp_username" 
                           name="smtp_username" 
                           value="<?= e($settings['smtp_username'] ?? '') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="smtp_password" class="block text-sm font-medium text-gray-700 mb-2">SMTP Password</label>
                    <input type="password" 
                           id="smtp_password" 
                           name="smtp_password" 
                           value="<?= e($settings['smtp_password'] ?? '') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Leave blank to keep current">
                </div>

                <div>
                    <label for="mail_from_address" class="block text-sm font-medium text-gray-700 mb-2">From Email Address</label>
                    <input type="email" 
                           id="mail_from_address" 
                           name="mail_from_address" 
                           value="<?= e($settings['mail_from_address'] ?? '') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="<EMAIL>">
                </div>

                <div>
                    <label for="mail_from_name" class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                    <input type="text" 
                           id="mail_from_name" 
                           name="mail_from_name" 
                           value="<?= e($settings['mail_from_name'] ?? 'DMS System') ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Subscription Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Subscription Settings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="basic_plan_price" class="block text-sm font-medium text-gray-700 mb-2">Basic Plan Price ($)</label>
                    <input type="number" 
                           id="basic_plan_price" 
                           name="basic_plan_price" 
                           value="<?= e($settings['basic_plan_price'] ?? '29') ?>"
                           min="0"
                           step="0.01"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="professional_plan_price" class="block text-sm font-medium text-gray-700 mb-2">Professional Plan Price ($)</label>
                    <input type="number" 
                           id="professional_plan_price" 
                           name="professional_plan_price" 
                           value="<?= e($settings['professional_plan_price'] ?? '79') ?>"
                           min="0"
                           step="0.01"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="enterprise_plan_price" class="block text-sm font-medium text-gray-700 mb-2">Enterprise Plan Price ($)</label>
                    <input type="number" 
                           id="enterprise_plan_price" 
                           name="enterprise_plan_price" 
                           value="<?= e($settings['enterprise_plan_price'] ?? '199') ?>"
                           min="0"
                           step="0.01"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex items-center justify-end space-x-4 pt-6">
            <a href="<?= url('/super-admin/dashboard') ?>" 
               class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                Cancel
            </a>
            <button type="submit" 
                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Save Settings
            </button>
        </div>
    </form>
</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
