<?php
/**
 * Test Rewrite Rules
 * 
 * This script tests if URL rewriting is working correctly
 */

echo "<h1>URL Rewrite Test</h1>";

echo "<h2>Server Information</h2>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>PATH_INFO:</strong> " . ($_SERVER['PATH_INFO'] ?? 'Not set') . "</p>";
echo "<p><strong>QUERY_STRING:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'Not set') . "</p>";
echo "<p><strong>REQUEST_METHOD:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'Not set') . "</p>";

echo "<h2>Apache Modules</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p>✅ mod_rewrite is loaded</p>";
    } else {
        echo "<p>❌ mod_rewrite is NOT loaded</p>";
    }
} else {
    echo "<p>⚠️ Cannot check Apache modules (not running under Apache or function not available)</p>";
}

echo "<h2>Test Links</h2>";
echo "<p>Click these links to test routing:</p>";
echo "<ul>";
echo "<li><a href='/dms/'>Home</a></li>";
echo "<li><a href='/dms/login'>Login</a></li>";
echo "<li><a href='/dms/dashboard'>Dashboard</a></li>";
echo "<li><a href='/dms/super-admin/dashboard'>Super Admin Dashboard</a></li>";
echo "<li><a href='/dms/app/companies'>Companies</a></li>";
echo "<li><a href='/dms/nonexistent'>Non-existent route (should show 404)</a></li>";
echo "</ul>";

echo "<h2>.htaccess Test</h2>";
if (file_exists('.htaccess')) {
    echo "<p>✅ .htaccess file exists</p>";
    echo "<h3>.htaccess Contents:</h3>";
    echo "<pre>" . htmlspecialchars(file_get_contents('.htaccess')) . "</pre>";
} else {
    echo "<p>❌ .htaccess file not found</p>";
}

echo "<h2>Directory Structure</h2>";
echo "<p><strong>Current directory:</strong> " . getcwd() . "</p>";
echo "<p><strong>Files in current directory:</strong></p>";
echo "<ul>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..') {
        echo "<li>" . htmlspecialchars($file) . "</li>";
    }
}
echo "</ul>";
?>
