-- Add storage_type column to bundles table
-- This column is needed for the bundle workflow to distinguish between physical and online storage

-- Add storage_type column
ALTER TABLE bundles 
ADD COLUMN storage_type ENUM('physical', 'online', 'mixed') DEFAULT 'mixed' AFTER access_level;

-- Add index for storage_type
ALTER TABLE bundles 
ADD INDEX idx_bundles_storage_type (storage_type);

-- Update existing bundles to have mixed storage type (since they can contain both types)
UPDATE bundles 
SET storage_type = 'mixed' 
WHERE storage_type IS NULL;

-- Add comment to document the purpose
ALTER TABLE bundles 
MODIFY COLUMN storage_type ENUM('physical', 'online', 'mixed') DEFAULT 'mixed' 
COMMENT 'Storage type: physical for warehouse storage only, online for digital-only storage, mixed for both types';
