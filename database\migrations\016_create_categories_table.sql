-- Create categories table for document categorization

CREATE TABLE IF NOT EXISTS categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6' COMMENT 'Hex color code for UI display',
    icon VARCHAR(50) DEFAULT 'folder' COMMENT 'Icon name for UI display',
    parent_id INT NULL COMMENT 'For hierarchical categories',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOR<PERSON>GN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_active (is_active),
    INDEX idx_categories_sort (sort_order),
    UNIQUE KEY unique_category_name_per_company (company_id, name, parent_id)
);

-- Insert default categories for existing companies
INSERT INTO categories (company_id, name, description, color, icon, created_by) VALUES
(1, 'General', 'General documents and files', '#6B7280', 'folder', 1),
(1, 'Financial', 'Financial documents, invoices, receipts', '#10B981', 'currency-dollar', 1),
(1, 'Legal', 'Contracts, agreements, legal documents', '#EF4444', 'scale', 1),
(1, 'HR', 'Human resources, employee documents', '#8B5CF6', 'users', 1),
(1, 'Marketing', 'Marketing materials, brochures, campaigns', '#F59E0B', 'megaphone', 1),
(1, 'Technical', 'Technical documentation, manuals', '#3B82F6', 'cog', 1),
(1, 'Correspondence', 'Letters, emails, communications', '#06B6D4', 'mail', 1),
(1, 'Reports', 'Business reports, analytics', '#84CC16', 'chart-bar', 1),
(1, 'Archived', 'Archived and historical documents', '#6B7280', 'archive', 1);

-- Create subcategories for Financial
INSERT INTO categories (company_id, name, description, color, icon, parent_id, created_by) 
SELECT 1, 'Invoices', 'Customer and vendor invoices', '#10B981', 'receipt-tax', id, 1 
FROM categories WHERE name = 'Financial' AND company_id = 1;

INSERT INTO categories (company_id, name, description, color, icon, parent_id, created_by) 
SELECT 1, 'Receipts', 'Purchase receipts and expense documents', '#10B981', 'receipt-refund', id, 1 
FROM categories WHERE name = 'Financial' AND company_id = 1;

INSERT INTO categories (company_id, name, description, color, icon, parent_id, created_by) 
SELECT 1, 'Tax Documents', 'Tax returns, forms, and related documents', '#10B981', 'calculator', id, 1 
FROM categories WHERE name = 'Financial' AND company_id = 1;

-- Create subcategories for Legal
INSERT INTO categories (company_id, name, description, color, icon, parent_id, created_by) 
SELECT 1, 'Contracts', 'Client and vendor contracts', '#EF4444', 'document-text', id, 1 
FROM categories WHERE name = 'Legal' AND company_id = 1;

INSERT INTO categories (company_id, name, description, color, icon, parent_id, created_by) 
SELECT 1, 'Compliance', 'Regulatory and compliance documents', '#EF4444', 'shield-check', id, 1 
FROM categories WHERE name = 'Legal' AND company_id = 1;

-- Create subcategories for HR
INSERT INTO categories (company_id, name, description, color, icon, parent_id, created_by) 
SELECT 1, 'Employee Files', 'Individual employee documentation', '#8B5CF6', 'user', id, 1 
FROM categories WHERE name = 'HR' AND company_id = 1;

INSERT INTO categories (company_id, name, description, color, icon, parent_id, created_by) 
SELECT 1, 'Policies', 'Company policies and procedures', '#8B5CF6', 'clipboard-list', id, 1 
FROM categories WHERE name = 'HR' AND company_id = 1;

INSERT INTO categories (company_id, name, description, color, icon, parent_id, created_by) 
SELECT 1, 'Training', 'Training materials and records', '#8B5CF6', 'academic-cap', id, 1 
FROM categories WHERE name = 'HR' AND company_id = 1;
