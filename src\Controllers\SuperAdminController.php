<?php

namespace App\Controllers;

/**
 * Super Admin Controller
 * 
 * Main dashboard and management interface for super administrators
 * Provides system-wide overview and management capabilities
 */
class SuperAdminController extends BaseController
{
    /**
     * Super Admin Dashboard
     */
    public function dashboard()
    {
        // Check authentication and super admin role
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get system-wide statistics
            $systemStats = $this->getSystemStats();

            // Get company overview
            $companyOverview = $this->getCompanyOverview();

            // Get recent activity across all companies
            $recentActivity = $this->getRecentSystemActivity();

            // Get storage utilization
            $storageStats = $this->getStorageStats();

            // Get subscription analytics
            $subscriptionStats = $this->getSubscriptionStats();

            // Get system alerts
            $systemAlerts = $this->getSystemAlerts();

            $this->view('super-admin/dashboard', [
                'title' => 'Super Admin Dashboard',
                'systemStats' => $systemStats,
                'companyOverview' => $companyOverview,
                'recentActivity' => $recentActivity,
                'storageStats' => $storageStats,
                'subscriptionStats' => $subscriptionStats,
                'systemAlerts' => $systemAlerts
            ]);

        } catch (\Exception $e) {
            // Log the error
            error_log("SuperAdmin Dashboard Error: " . $e->getMessage());

            // Show error page
            http_response_code(500);
            echo "<h1>Dashboard Error</h1>";
            echo "<p>Unable to load super admin dashboard: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p><a href='" . url('/login') . "'>Return to Login</a></p>";
        }
    }

    /**
     * System Analytics
     */
    public function analytics()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);
        
        try {
            // Get analytics data
            $userGrowth = $this->getUserGrowthData();
            $documentGrowth = $this->getDocumentGrowthData();
            $storageGrowth = $this->getStorageGrowthData();
            $companyGrowth = $this->getCompanyGrowthData();
            $subscriptionAnalytics = $this->getSubscriptionAnalytics();

            $this->view('super-admin/analytics', [
                'title' => 'System Analytics',
                'userGrowth' => $userGrowth,
                'documentGrowth' => $documentGrowth,
                'storageGrowth' => $storageGrowth,
                'companyGrowth' => $companyGrowth,
                'subscriptionAnalytics' => $subscriptionAnalytics
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading analytics: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/dashboard');
        }
    }

    /**
     * System Settings
     */
    public function settings()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);
        
        try {
            // Get current system settings
            $settings = $this->getSystemSettings();

            $this->view('super-admin/settings', [
                'title' => 'System Settings',
                'settings' => $settings
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading settings: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/dashboard');
        }
    }

    /**
     * Update system settings
     */
    public function updateSettings()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);
        
        try {
            // Validate input
            $data = $this->validate($_POST, [
                'system_name' => 'required|max:255',
                'system_email' => 'required|email|max:255',
                'default_storage_limit' => 'integer|min:1',
                'max_file_size' => 'integer|min:1',
                'allowed_file_types' => 'max:1000',
                'maintenance_mode' => 'boolean',
                'user_registration' => 'boolean',
                'email_verification' => 'boolean'
            ]);

            // Update system settings
            $this->updateSystemSetting('system_name', $data['system_name']);
            $this->updateSystemSetting('system_email', $data['system_email']);
            $this->updateSystemSetting('default_storage_limit', $data['default_storage_limit']);
            $this->updateSystemSetting('max_file_size', $data['max_file_size']);
            $this->updateSystemSetting('allowed_file_types', $data['allowed_file_types']);
            $this->updateSystemSetting('maintenance_mode', isset($data['maintenance_mode']) ? 1 : 0);
            $this->updateSystemSetting('user_registration', isset($data['user_registration']) ? 1 : 0);
            $this->updateSystemSetting('email_verification', isset($data['email_verification']) ? 1 : 0);

            // Log activity
            $this->logActivity('update', 'system_settings', 0, 'Updated system settings');

            $this->setFlashMessage('System settings updated successfully', 'success');
            $this->redirect('/super-admin/settings');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to update settings: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/settings');
        }
    }

    /**
     * Get system-wide statistics
     */
    private function getSystemStats()
    {
        $stats = [];

        try {
            // Total companies
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM companies");
            $stats['total_companies'] = $result['count'] ?? 0;

            // Active companies
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM companies WHERE status = 'active'");
            $stats['active_companies'] = $result['count'] ?? 0;

            // Total users
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM users");
            $stats['total_users'] = $result['count'] ?? 0;

            // Active users
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
            $stats['active_users'] = $result['count'] ?? 0;

            // Total documents
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM documents WHERE status != 'deleted'");
            $stats['total_documents'] = $result['count'] ?? 0;

            // Total storage used
            $result = $this->db->fetch("SELECT SUM(storage_used) as total FROM companies");
            $stats['total_storage_used'] = $result['total'] ?? 0;

            // Total storage limit
            $result = $this->db->fetch("SELECT SUM(storage_limit) as total FROM companies");
            $stats['total_storage_limit'] = $result['total'] ?? 0;

            // Storage percentage
            if ($stats['total_storage_limit'] > 0) {
                $stats['storage_percentage'] = round(($stats['total_storage_used'] / $stats['total_storage_limit']) * 100, 1);
            } else {
                $stats['storage_percentage'] = 0;
            }

            // Total bundles
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM bundles WHERE status = 'active'");
            $stats['total_bundles'] = $result['count'] ?? 0;

            // Total boxes
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM boxes");
            $stats['total_boxes'] = $result['count'] ?? 0;

            // Total requests
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM document_requests");
            $stats['total_requests'] = $result['count'] ?? 0;

            // Pending requests
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM document_requests WHERE status = 'pending'");
            $stats['pending_requests'] = $result['count'] ?? 0;

            // Total alerts
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM automated_alerts WHERE status IN ('pending', 'sent')");
            $stats['active_alerts'] = $result['count'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get company overview
     */
    private function getCompanyOverview()
    {
        try {
            return $this->db->fetchAll(
                "SELECT c.*,
                        COUNT(DISTINCT u.id) as user_count,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT b.id) as bundle_count,
                        COUNT(DISTINCT box.id) as box_count,
                        (c.storage_used / c.storage_limit * 100) as storage_percentage
                 FROM companies c
                 LEFT JOIN users u ON c.id = u.company_id AND u.status = 'active'
                 LEFT JOIN documents d ON c.id = d.company_id AND d.status != 'deleted'
                 LEFT JOIN bundles b ON c.id = b.company_id AND b.status = 'active'
                 LEFT JOIN boxes box ON c.id = box.company_id
                 WHERE c.status = 'active'
                 GROUP BY c.id
                 ORDER BY c.created_at DESC
                 LIMIT 10",
                []
            );
        } catch (\Exception $e) {
            // Return basic company info if complex query fails
            try {
                return $this->db->fetchAll(
                    "SELECT c.*, 0 as user_count, 0 as document_count, 0 as bundle_count, 0 as box_count,
                            (c.storage_used / c.storage_limit * 100) as storage_percentage
                     FROM companies c
                     WHERE c.status = 'active'
                     ORDER BY c.created_at DESC
                     LIMIT 10"
                );
            } catch (\Exception $e2) {
                return [];
            }
        }
    }

    /**
     * Get recent system activity
     */
    private function getRecentSystemActivity()
    {
        try {
            return $this->db->fetchAll(
                "SELECT al.*, u.first_name, u.last_name, u.email, c.name as company_name
                 FROM audit_logs al
                 LEFT JOIN users u ON al.user_id = u.id
                 LEFT JOIN companies c ON u.company_id = c.id
                 ORDER BY al.created_at DESC
                 LIMIT 20",
                []
            );
        } catch (\Exception $e) {
            // Return empty array if query fails
            return [];
        }
    }

    /**
     * Get storage statistics
     */
    private function getStorageStats()
    {
        $stats = [];

        try {
            // Storage by company
            $storageByCompany = $this->db->fetchAll(
                "SELECT c.name, c.storage_used, c.storage_limit,
                        (c.storage_used / c.storage_limit * 100) as percentage
                 FROM companies c
                 WHERE c.status = 'active' AND c.storage_limit > 0
                 ORDER BY percentage DESC
                 LIMIT 10"
            );
            $stats['by_company'] = $storageByCompany;

            // Storage growth over time
            $storageGrowth = $this->db->fetchAll(
                "SELECT DATE(created_at) as date, SUM(file_size) as daily_storage
                 FROM documents
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                 AND status != 'deleted'
                 GROUP BY DATE(created_at)
                 ORDER BY date ASC"
            );
            $stats['growth'] = $storageGrowth;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get subscription statistics
     */
    private function getSubscriptionStats()
    {
        $stats = [];

        try {
            // Companies by subscription plan
            $planStats = $this->db->fetchAll(
                "SELECT subscription_plan, COUNT(*) as count
                 FROM companies
                 WHERE status = 'active'
                 GROUP BY subscription_plan"
            );
            $stats['by_plan'] = [];
            foreach ($planStats as $row) {
                $stats['by_plan'][$row['subscription_plan']] = $row['count'];
            }

            // Revenue by plan (if monthly_fee exists)
            $revenueStats = $this->db->fetchAll(
                "SELECT subscription_plan, SUM(monthly_fee) as revenue
                 FROM companies
                 WHERE status = 'active' AND monthly_fee > 0
                 GROUP BY subscription_plan"
            );
            $stats['revenue_by_plan'] = [];
            foreach ($revenueStats as $row) {
                $stats['revenue_by_plan'][$row['subscription_plan']] = $row['revenue'];
            }

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get system alerts
     */
    private function getSystemAlerts()
    {
        try {
            return $this->db->fetchAll(
                "SELECT a.*, c.name as company_name
                 FROM automated_alerts a
                 JOIN companies c ON a.company_id = c.id
                 WHERE a.status IN ('pending', 'sent')
                 AND a.severity IN ('warning', 'critical')
                 ORDER BY a.severity DESC, a.created_at DESC
                 LIMIT 10",
                []
            );
        } catch (\Exception $e) {
            // Return empty array if automated_alerts table doesn't exist
            return [];
        }
    }

    /**
     * Update system setting
     */
    private function updateSystemSetting($key, $value)
    {
        // Check if setting exists
        $existing = $this->db->fetch(
            "SELECT id FROM system_settings WHERE setting_key = ?",
            [$key]
        );

        if ($existing) {
            $this->db->execute(
                "UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
                [$value, $key]
            );
        } else {
            $this->db->execute(
                "INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
                [$key, $value]
            );
        }
    }

    /**
     * Get system settings
     */
    private function getSystemSettings()
    {
        $settings = [];
        
        try {
            $results = $this->db->fetchAll(
                "SELECT setting_key, setting_value FROM system_settings"
            );
            
            foreach ($results as $row) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (\Exception $e) {
            // Return default settings on error
        }

        // Set defaults if not found
        $defaults = [
            'system_name' => 'Document Management System',
            'system_email' => '<EMAIL>',
            'default_storage_limit' => 5368709120, // 5GB
            'max_file_size' => 104857600, // 100MB
            'allowed_file_types' => 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif',
            'maintenance_mode' => 0,
            'user_registration' => 1,
            'email_verification' => 0
        ];

        return array_merge($defaults, $settings);
    }

    /**
     * Get user growth data for analytics
     */
    private function getUserGrowthData()
    {
        try {
            return $this->db->fetchAll(
                "SELECT DATE(created_at) as date, COUNT(*) as count
                 FROM users
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                 GROUP BY DATE(created_at)
                 ORDER BY date ASC"
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get document growth data for analytics
     */
    private function getDocumentGrowthData()
    {
        try {
            return $this->db->fetchAll(
                "SELECT DATE(created_at) as date, COUNT(*) as count
                 FROM documents
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                 AND status != 'deleted'
                 GROUP BY DATE(created_at)
                 ORDER BY date ASC"
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get storage growth data for analytics
     */
    private function getStorageGrowthData()
    {
        try {
            return $this->db->fetchAll(
                "SELECT DATE(created_at) as date, SUM(file_size) as total_size
                 FROM documents
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                 AND status != 'deleted'
                 GROUP BY DATE(created_at)
                 ORDER BY date ASC"
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get company growth data for analytics
     */
    private function getCompanyGrowthData()
    {
        try {
            return $this->db->fetchAll(
                "SELECT DATE(created_at) as date, COUNT(*) as count
                 FROM companies
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                 GROUP BY DATE(created_at)
                 ORDER BY date ASC"
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get subscription analytics data
     */
    private function getSubscriptionAnalytics()
    {
        $analytics = [];

        try {
            // Subscription distribution
            $planStats = $this->db->fetchAll(
                "SELECT subscription_plan, COUNT(*) as count
                 FROM companies
                 WHERE status = 'active'
                 GROUP BY subscription_plan"
            );
            $analytics['plan_distribution'] = [];
            foreach ($planStats as $row) {
                $analytics['plan_distribution'][$row['subscription_plan']] = $row['count'];
            }

            // Monthly subscription trends
            $monthlyTrends = $this->db->fetchAll(
                "SELECT DATE_FORMAT(created_at, '%Y-%m') as month,
                        subscription_plan, COUNT(*) as count
                 FROM companies
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                 GROUP BY DATE_FORMAT(created_at, '%Y-%m'), subscription_plan
                 ORDER BY month ASC"
            );
            $analytics['monthly_trends'] = $monthlyTrends;

            // Storage usage by plan
            $storageByPlan = $this->db->fetchAll(
                "SELECT subscription_plan,
                        AVG(storage_used) as avg_storage_used,
                        AVG(storage_limit) as avg_storage_limit,
                        AVG(storage_used / storage_limit * 100) as avg_usage_percentage
                 FROM companies
                 WHERE status = 'active' AND storage_limit > 0
                 GROUP BY subscription_plan"
            );
            $analytics['storage_by_plan'] = $storageByPlan;

        } catch (\Exception $e) {
            // Return default analytics on error
        }

        return $analytics;
    }
}
