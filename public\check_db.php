<?php
/**
 * Database Check Script
 */

// Include the application bootstrap
require_once __DIR__ . '/../src/autoload.php';
require_once __DIR__ . '/../src/config/database.php';

use App\Core\Database;
use App\Services\ServiceFactory;

try {
    $db = Database::getInstance();
    
    echo "<h1>Database Check</h1>";
    
    // Check if required tables exist
    $requiredTables = [
        'users', 'companies', 'warehouses', 'bundles', 'boxes', 'documents',
        'delivery_requests', 'delivery_items', 'delivery_tracking', 
        'delivery_returns', 'box_movements', 'search_logs'
    ];
    
    echo "<h2>Table Check</h2>";
    foreach ($requiredTables as $table) {
        $exists = $db->fetch("SHOW TABLES LIKE '{$table}'");
        $status = $exists ? '✅' : '❌';
        echo "<p>{$status} {$table}</p>";
    }
    
    // Check if we have a warehouse
    echo "<h2>Data Check</h2>";
    $warehouses = $db->fetchAll("SELECT * FROM warehouses LIMIT 5");
    echo "<p>Warehouses found: " . count($warehouses) . "</p>";
    
    if (empty($warehouses)) {
        echo "<p>❌ No warehouses found. Creating a test warehouse...</p>";
        
        // Create a test warehouse
        $warehouseId = $db->execute(
            "INSERT INTO warehouses (name, code, address, city, state, zip_code, country, status, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())",
            ['Test Warehouse', 'WH01', '123 Test St', 'Test City', 'Test State', '12345', 'USA']
        );
        
        echo "<p>✅ Created test warehouse with ID: {$warehouseId}</p>";
    } else {
        echo "<p>✅ Warehouses available:</p>";
        foreach ($warehouses as $warehouse) {
            echo "<p>- ID: {$warehouse['id']}, Name: {$warehouse['name']}, Code: {$warehouse['code']}</p>";
        }
    }
    
    // Check companies
    $companies = $db->fetchAll("SELECT * FROM companies WHERE status = 'active' LIMIT 5");
    echo "<p>Active companies found: " . count($companies) . "</p>";
    
    if (!empty($companies)) {
        echo "<p>✅ Companies available:</p>";
        foreach ($companies as $company) {
            echo "<p>- ID: {$company['id']}, Name: {$company['name']}</p>";
        }
    }
    
    // Check users
    $users = $db->fetchAll("SELECT * FROM users WHERE status = 'active' LIMIT 5");
    echo "<p>Active users found: " . count($users) . "</p>";
    
    if (!empty($users)) {
        echo "<p>✅ Users available:</p>";
        foreach ($users as $user) {
            echo "<p>- ID: {$user['id']}, Email: {$user['email']}, Company ID: {$user['company_id']}</p>";
        }
    }
    
    echo "<h2>Service Test</h2>";
    
    // Test service instantiation
    session_start();
    if (isset($_SESSION['user_id'])) {
        $user = $db->fetch(
            "SELECT u.*, c.name as company_name
             FROM users u
             LEFT JOIN companies c ON u.company_id = c.id
             WHERE u.id = ? AND u.status = 'active'",
            [$_SESSION['user_id']]
        );
        
        if ($user) {
            echo "<p>✅ Current user: {$user['email']} (Company: {$user['company_name']})</p>";

            // Test service factory
            ServiceFactory::initialize($db, $user, null);
            
            try {
                $boxService = ServiceFactory::getBoxHandlingService();
                echo "<p>✅ Box service instantiated successfully</p>";
                
                $bundleService = ServiceFactory::getBundleHandlingService();
                echo "<p>✅ Bundle service instantiated successfully</p>";
                
                $searchService = ServiceFactory::getSearchService();
                echo "<p>✅ Search service instantiated successfully</p>";
                
                $intakeService = ServiceFactory::getIntakeService();
                echo "<p>✅ Intake service instantiated successfully</p>";
                
                $deliveryService = ServiceFactory::getDeliveryService();
                echo "<p>✅ Delivery service instantiated successfully</p>";
                
            } catch (Exception $e) {
                echo "<p>❌ Service instantiation failed: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>❌ User not found or inactive</p>";
        }
    } else {
        echo "<p>❌ No user logged in</p>";
        echo "<p><a href='/dms/public/login.php'>Login here</a></p>";
    }
    
} catch (Exception $e) {
    echo "<h1>Database Error</h1>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Make sure XAMPP MySQL is running and the database exists.</p>";
}
?>
