<?php
/**
 * Debug Routes Script
 * 
 * This script helps debug routing issues by showing all registered routes
 */

// Start session
session_start();

// Define application constants
define('APP_ROOT', __DIR__);
define('PUBLIC_ROOT', __DIR__ . '/public');
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

echo "=== DMS ROUTE DEBUGGING ===\n";
echo "Testing route registration and matching\n\n";

try {
    // Create router instance
    $router = new \App\Core\Router();
    
    // Load routes
    require_once APP_ROOT . '/src/routes.php';
    
    // Use reflection to access private routes property
    $reflection = new ReflectionClass($router);
    $routesProperty = $reflection->getProperty('routes');
    $routesProperty->setAccessible(true);
    $routes = $routesProperty->getValue($router);
    
    echo "Total routes registered: " . count($routes) . "\n\n";
    
    // Find super-admin routes
    $superAdminRoutes = array_filter($routes, function($route) {
        return strpos($route['path'], '/super-admin') !== false;
    });
    
    echo "Super Admin routes found: " . count($superAdminRoutes) . "\n";
    foreach ($superAdminRoutes as $route) {
        echo "- {$route['method']} {$route['path']} -> {$route['handler']}\n";
        echo "  Pattern: {$route['pattern']}\n";
        echo "  Middlewares: " . implode(', ', $route['middlewares']) . "\n\n";
    }
    
    // Test specific route matching
    $testUri = '/super-admin/dashboard';
    $testMethod = 'GET';
    
    echo "Testing route matching for: {$testMethod} {$testUri}\n";
    
    foreach ($routes as $route) {
        if ($route['method'] === $testMethod) {
            if (preg_match($route['pattern'], $testUri, $matches)) {
                echo "✓ MATCH FOUND!\n";
                echo "  Route: {$route['path']}\n";
                echo "  Handler: {$route['handler']}\n";
                echo "  Pattern: {$route['pattern']}\n";
                echo "  Matches: " . json_encode($matches) . "\n";
                break;
            }
        }
    }
    
    // Test if SuperAdminController exists
    echo "\nTesting SuperAdminController:\n";
    if (class_exists('App\\Controllers\\SuperAdminController')) {
        echo "✓ SuperAdminController class exists\n";
        
        $controller = new \App\Controllers\SuperAdminController();
        if (method_exists($controller, 'dashboard')) {
            echo "✓ dashboard method exists\n";
        } else {
            echo "✗ dashboard method missing\n";
        }
    } else {
        echo "✗ SuperAdminController class not found\n";
    }
    
    // Test current request URI processing
    echo "\nCurrent request processing:\n";
    $requestUri = $_SERVER['REQUEST_URI'] ?? '/super-admin/dashboard';
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    
    echo "Original URI: {$requestUri}\n";
    
    // Remove query string from URI
    $requestUri = strtok($requestUri, '?');
    echo "After query removal: {$requestUri}\n";
    
    // Remove base path if application is in subdirectory
    $basePath = dirname($_SERVER['SCRIPT_NAME'] ?? '/index.php');
    echo "Base path: {$basePath}\n";
    
    if ($basePath !== '/' && $basePath !== '') {
        $requestUri = substr($requestUri, strlen($basePath));
        echo "After base path removal: {$requestUri}\n";
    }
    
    // Ensure URI starts with /
    if (!$requestUri || $requestUri[0] !== '/') {
        $requestUri = '/' . $requestUri;
        echo "After ensuring leading slash: {$requestUri}\n";
    }
    
    echo "Final processed URI: {$requestUri}\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
