<?php

namespace App\Services;

use App\Core\Database;

/**
 * Service Factory
 * 
 * Centralized factory for creating and managing business services
 */
class ServiceFactory
{
    private static $instances = [];
    private static $db;
    private static $user;
    private static $company;

    /**
     * Initialize the service factory
     */
    public static function initialize(Database $db, $user = null, $company = null)
    {
        self::$db = $db;
        self::$user = $user;
        self::$company = $company;
    }

    /**
     * Get Box Handling Service
     */
    public static function getBoxHandlingService()
    {
        if (!isset(self::$instances['BoxHandlingService'])) {
            self::$instances['BoxHandlingService'] = new BoxHandlingService(
                self::$db, 
                self::$user, 
                self::$company
            );
        }
        return self::$instances['BoxHandlingService'];
    }

    /**
     * Get Bundle Handling Service
     */
    public static function getBundleHandlingService()
    {
        if (!isset(self::$instances['BundleHandlingService'])) {
            self::$instances['BundleHandlingService'] = new BundleHandlingService(
                self::$db, 
                self::$user, 
                self::$company
            );
        }
        return self::$instances['BundleHandlingService'];
    }

    /**
     * Get Delivery Service
     */
    public static function getDeliveryService()
    {
        if (!isset(self::$instances['DeliveryService'])) {
            self::$instances['DeliveryService'] = new DeliveryService(
                self::$db, 
                self::$user, 
                self::$company
            );
        }
        return self::$instances['DeliveryService'];
    }

    /**
     * Get Search Service
     */
    public static function getSearchService()
    {
        if (!isset(self::$instances['SearchService'])) {
            self::$instances['SearchService'] = new SearchService(
                self::$db,
                self::$user,
                self::$company
            );
        }
        return self::$instances['SearchService'];
    }

    /**
     * Get Billing Service
     */
    public static function getBillingService()
    {
        if (!isset(self::$instances['BillingService'])) {
            self::$instances['BillingService'] = new BillingService(
                self::$db,
                self::$user,
                self::$company
            );
        }
        return self::$instances['BillingService'];
    }

    /**
     * Get Intake Service
     */
    public static function getIntakeService()
    {
        if (!isset(self::$instances['IntakeService'])) {
            self::$instances['IntakeService'] = new IntakeService(
                self::$db, 
                self::$user, 
                self::$company
            );
        }
        return self::$instances['IntakeService'];
    }

    /**
     * Update user context for all services
     */
    public static function updateUserContext($user, $company = null)
    {
        self::$user = $user;
        self::$company = $company;
        
        // Update existing service instances
        foreach (self::$instances as $service) {
            if (method_exists($service, 'updateUserContext')) {
                $service->updateUserContext($user, $company);
            }
        }
    }

    /**
     * Clear all service instances (useful for testing)
     */
    public static function clearInstances()
    {
        self::$instances = [];
    }

    /**
     * Get all available services
     */
    public static function getAvailableServices()
    {
        return [
            'BoxHandlingService' => 'Box Handling Service',
            'BundleHandlingService' => 'Bundle and File Handling Service',
            'DeliveryService' => 'Delivery Service',
            'SearchService' => 'Search Service',
            'IntakeService' => 'Intake Service',
            'BillingService' => 'Billing Service'
        ];
    }
}
