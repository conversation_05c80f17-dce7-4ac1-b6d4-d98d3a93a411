<?php
$title = 'Service Provider Dashboard';
ob_start();
?>

<!-- Service Provider Dashboard - Third-Party Document Storage Service -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- <PERSON> Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        Service Provider Dashboard
                    </h1>
                    <p class="text-gray-600 mt-2">Third-party document storage and management services overview</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/service-provider/clients') ?>" class="inline-flex items-center px-4 py-2 bg-white border border-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-50 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        Manage Clients
                    </a>
                    <a href="<?= url('/app/intake/create') ?>" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Intake
                    </a>
                </div>
            </div>
        </div>

        <!-- Service Provider Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <!-- Total Clients -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-blue-200 text-sm font-medium">Active</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-blue-100 mb-1">Client Companies</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['total_clients']) ?></p>
                    <p class="text-xs text-blue-100 mt-1">active clients</p>
                </div>
            </div>

            <!-- Total Documents -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-purple-200 text-sm font-medium">Total</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-purple-100 mb-1">Documents Managed</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['total_documents']) ?></p>
                    <p class="text-xs text-purple-100 mt-1">across all clients</p>
                </div>
            </div>

            <!-- Physical Storage -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl p-6 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-emerald-400 to-emerald-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-emerald-200 text-sm font-medium">Physical</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-emerald-100 mb-1">Storage Boxes</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['total_physical_boxes']) ?></p>
                    <p class="text-xs text-emerald-100 mt-1">physical storage units</p>
                </div>
            </div>

            <!-- Online Storage -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl p-6 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-indigo-400 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-indigo-200 text-sm font-medium">Cloud</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-indigo-100 mb-1">Online Storage</h3>
                    <p class="text-3xl font-bold text-white"><?= formatBytes($stats['total_online_storage']) ?></p>
                    <p class="text-xs text-indigo-100 mt-1">digital storage used</p>
                </div>
            </div>
        </div>

        <!-- Storage Type Comparison -->
        <div class="bg-white/80 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Storage Services Overview</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

                <!-- Physical Storage -->
                <div class="space-y-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Physical Storage</h3>
                            <p class="text-sm text-gray-600">Secure warehouse document storage</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-emerald-50 rounded-xl border border-emerald-200">
                            <div>
                                <p class="font-medium text-emerald-900">Total Boxes</p>
                                <p class="text-sm text-emerald-600">Physical storage units</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-emerald-900"><?= number_format($storageStats['physical']['total_boxes']) ?></p>
                                <p class="text-sm text-emerald-600"><?= $storageStats['physical']['utilization_percentage'] ?>% utilized</p>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Utilization</span>
                                <span class="font-medium text-gray-900"><?= $storageStats['physical']['occupied_boxes'] ?> / <?= $storageStats['physical']['total_boxes'] ?> boxes</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 h-3 rounded-full transition-all duration-300"
                                     style="width: <?= $storageStats['physical']['utilization_percentage'] ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Online Storage -->
                <div class="space-y-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Online Storage</h3>
                            <p class="text-sm text-gray-600">Cloud-based digital storage</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-indigo-50 rounded-xl border border-indigo-200">
                            <div>
                                <p class="font-medium text-indigo-900">Storage Used</p>
                                <p class="text-sm text-indigo-600">Digital storage capacity</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-indigo-900"><?= formatBytes($storageStats['online']['used_storage']) ?></p>
                                <p class="text-sm text-indigo-600"><?= $storageStats['online']['utilization_percentage'] ?>% of capacity</p>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Capacity</span>
                                <span class="font-medium text-gray-900"><?= formatBytes($storageStats['online']['used_storage']) ?> / <?= formatBytes($storageStats['online']['total_storage']) ?></span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 h-3 rounded-full transition-all duration-300"
                                     style="width: <?= $storageStats['online']['utilization_percentage'] ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Companies and Recent Intakes -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

            <!-- Client Companies -->
            <div class="bg-white/80 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">Client Companies</h2>
                    <a href="<?= url('/service-provider/clients') ?>" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                        View all →
                    </a>
                </div>

                <?php if (empty($clientCompanies)): ?>
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No clients yet</h3>
                        <p class="text-gray-600">Start by adding your first client company.</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($clientCompanies as $client): ?>
                            <div class="border border-gray-200 rounded-2xl p-4 hover:shadow-md transition-all duration-200">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900 mb-1">
                                            <a href="<?= url('/service-provider/client/' . $client['id']) ?>" class="hover:text-blue-600 transition-colors">
                                                <?= e($client['name']) ?>
                                            </a>
                                        </h3>
                                        <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                            <div>
                                                <span class="font-medium">Documents:</span> <?= number_format($client['document_count']) ?>
                                            </div>
                                            <div>
                                                <span class="font-medium">Storage:</span> <?= formatBytes($client['storage_used']) ?>
                                            </div>
                                            <div>
                                                <span class="font-medium">Physical:</span> <?= number_format($client['physical_count']) ?>
                                            </div>
                                            <div>
                                                <span class="font-medium">Online:</span> <?= number_format($client['online_count']) ?>
                                            </div>
                                        </div>
                                        <?php if ($client['intake_count'] > 0): ?>
                                            <div class="mt-2">
                                                <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-orange-100 text-orange-800">
                                                    <?= $client['intake_count'] ?> pending intake(s)
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Intakes -->
            <div class="bg-white/80 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-md">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">Recent Intakes</h2>
                    <a href="<?= url('/app/intake') ?>" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                        View all →
                    </a>
                </div>

                <?php if (empty($recentIntakes)): ?>
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No recent intakes</h3>
                        <p class="text-gray-600">Intake activity will appear here.</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($recentIntakes as $intake): ?>
                            <div class="border border-gray-200 rounded-2xl p-4 hover:shadow-md transition-all duration-200">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <h3 class="font-semibold text-gray-900">
                                                <a href="<?= url('/app/intake/' . $intake['id']) ?>" class="hover:text-blue-600 transition-colors">
                                                    <?= e($intake['reference_number']) ?>
                                                </a>
                                            </h3>
                                            <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium
                                                <?php
                                                switch ($intake['priority']) {
                                                    case 'urgent': echo 'bg-red-100 text-red-800'; break;
                                                    case 'high': echo 'bg-orange-100 text-orange-800'; break;
                                                    case 'medium': echo 'bg-yellow-100 text-yellow-800'; break;
                                                    case 'low': echo 'bg-green-100 text-green-800'; break;
                                                }
                                                ?>">
                                                <?= ucfirst($intake['priority']) ?>
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 mb-2">
                                            <span class="font-medium"><?= e($intake['client_name']) ?>:</span>
                                            <?= e($intake['description']) ?>
                                        </p>
                                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                                            <span>Source: <?= ucfirst(str_replace('_', ' ', $intake['source'])) ?></span>
                                            <span>Type: <?= ucfirst($intake['document_type']) ?></span>
                                            <span>Created: <?= date('M j, Y', strtotime($intake['created_at'])) ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>