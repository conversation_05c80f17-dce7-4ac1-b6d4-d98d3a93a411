<?php
/**
 * Debug URL and routing issues
 */

// Start session
session_start();

// Include the bootstrap
require_once __DIR__ . '/../bootstrap.php';

echo "<h2>URL Debug Information</h2>";

echo "<h3>Server Variables:</h3>";
echo "<ul>";
echo "<li><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'not set') . "</li>";
echo "<li><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'not set') . "</li>";
echo "<li><strong>HTTP_HOST:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'not set') . "</li>";
echo "<li><strong>REQUEST_METHOD:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'not set') . "</li>";
echo "</ul>";

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

echo "<h3>Base Path Detection:</h3>";
echo "<p><strong>Detected Base Path:</strong> " . ($basePath ?: '(empty)') . "</p>";

// Test URL generation
function url($path) {
    $basePath = '';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
    if (strpos($scriptName, '/dms/public/') !== false) {
        $basePath = '/dms/public';
    } elseif (strpos($scriptName, '/dms/') !== false) {
        $basePath = '/dms';
    } elseif (dirname($scriptName) !== '/') {
        $basePath = dirname($scriptName);
    }
    
    return $basePath . $path;
}

echo "<h3>URL Generation Test:</h3>";
echo "<ul>";
echo "<li><strong>url('/app/warehouse-ajax-create'):</strong> " . url('/app/warehouse-ajax-create') . "</li>";
echo "<li><strong>url('/app/warehouses'):</strong> " . url('/app/warehouses') . "</li>";
echo "<li><strong>url('/app/dashboard'):</strong> " . url('/app/dashboard') . "</li>";
echo "</ul>";

echo "<h3>Current Page URL:</h3>";
echo "<p>" . url('/test-url-debug.php') . "</p>";

echo "<h3>Test Links:</h3>";
echo "<ul>";
echo "<li><a href='" . url('/app/dashboard') . "'>Dashboard</a></li>";
echo "<li><a href='" . url('/app/warehouses') . "'>Warehouses</a></li>";
echo "</ul>";

// Test POST request simulation
echo "<h3>POST Request Test:</h3>";
echo "<form method='POST' action='" . url('/app/warehouse-ajax-create') . "'>";
echo "<input type='hidden' name='name' value='Test Warehouse'>";
echo "<input type='hidden' name='address' value='123 Test St'>";
echo "<input type='hidden' name='city' value='Test City'>";
echo "<input type='hidden' name='state' value='Test State'>";
echo "<input type='hidden' name='zip_code' value='12345'>";
echo "<button type='submit'>Test POST to warehouse-ajax-create</button>";
echo "</form>";
?>
