<?php
$title = $box['name'] ?? 'Box Details';
ob_start();
?>

<!-- Enhanced Box Details -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Enhanced Page Header -->
        <div class="mb-8">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                                <?= e($box['name']) ?>
                            </h1>
                            <div class="flex items-center space-x-4 mt-2">
                                <span class="text-sm font-mono bg-gray-100 px-3 py-1 rounded-lg text-gray-700">
                                    ID: <?= e($box['box_id'] ?? 'N/A') ?>
                                </span>
                                <span class="text-sm font-mono bg-blue-100 px-3 py-1 rounded-lg text-blue-700">
                                    <?= e($box['storage_location_code'] ?? 'No Location') ?>
                                </span>
                                <?php
                                $statusColors = [
                                    'empty' => 'bg-gray-100 text-gray-800',
                                    'partial' => 'bg-yellow-100 text-yellow-800',
                                    'full' => 'bg-red-100 text-red-800',
                                    'sealed' => 'bg-blue-100 text-blue-800'
                                ];
                                $statusColor = $statusColors[$box['occupancy_status']] ?? 'bg-gray-100 text-gray-800';
                                ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium <?= $statusColor ?>">
                                    <?= ucfirst($box['occupancy_status']) ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-white/30 hover:shadow-lg transition-all duration-200">
                            <div class="flex items-center justify-between mb-2">
                                <div class="text-2xl font-bold text-purple-600"><?= number_format($stats['bundle_count'] ?? 0) ?></div>
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 font-medium">📁 Bundles</div>
                            <div class="text-xs text-purple-600 mt-1">
                                <?= ($stats['bundle_count'] ?? 0) > 0 ? 'Contains documents' : 'No bundles yet' ?>
                            </div>
                        </div>
                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-white/30 hover:shadow-lg transition-all duration-200">
                            <div class="flex items-center justify-between mb-2">
                                <div class="text-2xl font-bold text-blue-600"><?= number_format($stats['document_count'] ?? 0) ?></div>
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 font-medium">📄 Documents</div>
                            <div class="text-xs text-blue-600 mt-1">
                                <?= ($stats['document_count'] ?? 0) > 0 ? 'Across all bundles' : 'No documents yet' ?>
                            </div>
                        </div>

                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-white/30 hover:shadow-lg transition-all duration-200">
                            <?php $usagePercent = $box['capacity'] > 0 ? min(100, (($stats['document_count'] ?? 0) / $box['capacity']) * 100) : 0; ?>
                            <div class="flex items-center justify-between mb-2">
                                <div class="text-2xl font-bold text-orange-600"><?= number_format($usagePercent, 1) ?>%</div>
                                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 font-medium">📊 Capacity Used</div>
                            <div class="text-xs text-orange-600 mt-1">
                                <?= number_format($box['capacity'] - ($stats['document_count'] ?? 0)) ?> remaining
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col space-y-3">
                    <a href="<?= url('/app/boxes/' . $box['id'] . '/edit') ?>"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Box
                    </a>
                    <button onclick="generateQRCode()"
                            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white font-medium rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                        </svg>
                        QR Code
                    </button>
                    <button onclick="printLabel()"
                            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        Print Label
                    </button>
                    <a href="<?= url('/app/boxes') ?>"
                       class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Boxes
                    </a>
                </div>
            </div>
        </div>

        <!-- Document Flow Hierarchy - INTAKE → BUNDLE → BOX → STORAGE -->
        <?php if (!empty($intakeHistory)): ?>
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Document Flow Hierarchy</h2>
                    <p class="text-sm text-gray-600 mt-1">INTAKE → BUNDLE → BOX → STORAGE workflow for this box</p>
                </div>
                <div class="text-sm text-gray-500">
                    <?= count($intakeHistory) ?> intake entries
                </div>
            </div>

            <div class="space-y-4">
                <?php foreach (array_slice($intakeHistory, 0, 5) as $intake): ?>
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4">
                        <div class="flex items-center space-x-4">
                            <!-- Intake -->
                            <div class="flex-1">
                                <div class="text-xs font-medium text-blue-600 uppercase">INTAKE</div>
                                <div class="text-sm font-semibold text-gray-900"><?= e($intake['reference_number']) ?></div>
                                <div class="text-xs text-gray-600"><?= e($intake['client_name']) ?></div>
                                <div class="text-xs text-<?= $intake['status'] === 'completed' ? 'green' : 'yellow' ?>-600">
                                    <?= ucfirst($intake['status']) ?>
                                </div>
                            </div>

                            <div class="text-gray-400">→</div>

                            <!-- Bundle -->
                            <div class="flex-1">
                                <div class="text-xs font-medium text-purple-600 uppercase">BUNDLE</div>
                                <div class="text-sm font-semibold text-gray-900"><?= e($intake['bundle_name']) ?></div>
                                <div class="text-xs text-gray-600">Bundle Processing</div>
                            </div>

                            <div class="text-gray-400">→</div>

                            <!-- Box (Current) -->
                            <div class="flex-1 bg-green-100 rounded-lg p-2">
                                <div class="text-xs font-medium text-green-600 uppercase">BOX (CURRENT)</div>
                                <div class="text-sm font-semibold text-gray-900"><?= e($box['box_id']) ?></div>
                                <div class="text-xs text-blue-600"><?= e($box['storage_location_code']) ?></div>
                            </div>

                            <div class="text-gray-400">→</div>

                            <!-- Storage -->
                            <div class="flex-1">
                                <div class="text-xs font-medium text-orange-600 uppercase">STORAGE</div>
                                <div class="text-sm font-semibold text-gray-900"><?= e($box['warehouse_name']) ?></div>
                                <div class="text-xs text-gray-600">Physical Storage</div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($intakeHistory) > 5): ?>
                    <div class="text-center">
                        <button onclick="toggleAllIntakes()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Show all <?= count($intakeHistory) ?> intake entries
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Enhanced Box Information Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">

            <!-- Box Details Card -->
            <div class="lg:col-span-2 bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-900">Box Information</h2>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-500">Created by</span>
                        <span class="text-sm font-medium text-gray-700"><?= e($box['first_name'] . ' ' . $box['last_name']) ?></span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Box Name</label>
                        <p class="text-lg font-semibold text-gray-900"><?= e($box['name']) ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Box Identifier</label>
                        <p class="text-lg font-mono font-semibold text-blue-900 bg-blue-50 px-3 py-1 rounded-lg inline-block"><?= e($box['box_id']) ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Storage Location Code</label>
                        <p class="text-lg font-mono font-semibold text-green-900 bg-green-50 px-3 py-1 rounded-lg inline-block"><?= e($box['storage_location_code'] ?? 'No Location') ?></p>
                        <p class="text-xs text-gray-600 mt-1">Row <?= e($box['row_number'] ?? 'N/A') ?>, Shelf <?= e($box['shelf_number'] ?? 'N/A') ?>, Position <?= e($box['position_number'] ?? 'N/A') ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Storage Type</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium
                            <?= $box['storage_type'] === 'physical' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' ?>">
                            <?= $box['storage_type'] === 'physical' ? '📦 Physical' : '☁️ Digital' ?>
                        </span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Capacity</label>
                        <div class="flex items-center space-x-2">
                            <p class="text-lg font-semibold text-gray-900"><?= number_format($box['capacity']) ?></p>
                            <span class="text-sm text-gray-600">documents max</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <?php $usagePercent = $box['capacity'] > 0 ? min(100, (($stats['document_count'] ?? 0) / $box['capacity']) * 100) : 0; ?>
                            <div class="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                                 style="width: <?= $usagePercent ?>%"></div>
                        </div>
                        <p class="text-xs text-gray-600 mt-1"><?= number_format($usagePercent, 1) ?>% full</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Current Status</label>
                        <?php
                        $statusColors = [
                            'empty' => 'bg-gray-100 text-gray-800',
                            'partial' => 'bg-yellow-100 text-yellow-800',
                            'full' => 'bg-red-100 text-red-800',
                            'sealed' => 'bg-blue-100 text-blue-800'
                        ];
                        $statusColor = $statusColors[$box['occupancy_status']] ?? 'bg-gray-100 text-gray-800';
                        ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium <?= $statusColor ?>">
                            <?= ucfirst($box['occupancy_status']) ?>
                        </span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Bundle Count</label>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-purple-900 bg-purple-50 px-3 py-1 rounded-lg inline-block">
                                📁 <?= number_format($stats['bundle_count'] ?? 0) ?>
                            </span>
                            <span class="text-sm text-gray-600">bundles</span>
                        </div>
                        <p class="text-xs text-purple-600 mt-1">
                            <?= ($stats['bundle_count'] ?? 0) > 0 ? 'Contains ' . number_format($stats['document_count'] ?? 0) . ' documents' : 'No bundles added yet' ?>
                        </p>
                    </div>
                </div>

                <!-- Warehouse Information -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Warehouse Location</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Warehouse</label>
                            <p class="text-lg font-semibold text-gray-900"><?= e($box['warehouse_name']) ?></p>
                            <p class="text-sm text-gray-600"><?= e($box['warehouse_city']) ?>, <?= e($box['warehouse_state']) ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Full Address</label>
                            <p class="text-sm text-gray-700">
                                <?= e($storageDetails['warehouse_address'] ?? '') ?><br>
                                <?= e($storageDetails['warehouse_city'] ?? '') ?>, <?= e($storageDetails['warehouse_state'] ?? '') ?> <?= e($storageDetails['warehouse_zip'] ?? '') ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Barcode Section -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Identification & Tracking</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Barcode</label>
                            <div class="flex items-center space-x-2">
                                <p class="text-lg font-mono text-gray-900 bg-gray-100 px-3 py-1 rounded-lg"><?= e($box['barcode_value'] ?? 'No Barcode') ?></p>
                                <button onclick="printBarcode('<?= e($box['barcode_value'] ?? '') ?>')"
                                        class="p-2 text-gray-400 hover:text-blue-600 transition-colors bg-gray-100 hover:bg-blue-50 rounded-lg"
                                        title="Print Barcode">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Created</label>
                            <p class="text-sm text-gray-700"><?= date('M j, Y \a\t g:i A', strtotime($box['created_at'])) ?></p>
                            <p class="text-xs text-gray-500">by <?= e($box['first_name'] . ' ' . $box['last_name']) ?></p>
                        </div>
                    </div>
                </div>

                <?php if (!empty($box['description'])): ?>
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Description</label>
                    <p class="text-gray-700 bg-gray-50 p-4 rounded-lg"><?= e($box['description']) ?></p>
                </div>
                <?php endif; ?>
            </div>

            <!-- Enhanced Statistics & Activity Card -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Statistics & Activity</h2>

                <div class="space-y-6">
                    <!-- Documents Stat -->
                    <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-blue-900"><?= number_format($stats['document_count'] ?? 0) ?></h3>
                        <p class="text-sm text-blue-700 font-medium">Documents</p>
                    </div>

                    <!-- Bundles Stat -->
                    <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border-2 border-purple-200">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-purple-900"><?= number_format($stats['bundle_count'] ?? 0) ?></h3>
                        <p class="text-sm text-purple-700 font-bold">📁 BUNDLES</p>
                        <p class="text-xs text-purple-600 mt-1">
                            <?= ($stats['bundle_count'] ?? 0) > 0 ? 'Organized storage' : 'Ready for bundles' ?>
                        </p>
                    </div>



                    <!-- Capacity Progress -->
                    <div class="pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm font-medium text-gray-700">Capacity Usage</span>
                            <span class="text-sm font-semibold text-gray-900">
                                <?= number_format($stats['document_count'] ?? 0) ?> / <?= number_format($box['capacity']) ?>
                            </span>
                        </div>
                        <?php
                        $usagePercent = $box['capacity'] > 0 ? min(100, (($stats['document_count'] ?? 0) / $box['capacity']) * 100) : 0;
                        $progressColor = $usagePercent >= 90 ? 'from-red-500 to-red-600' : ($usagePercent >= 70 ? 'from-yellow-500 to-yellow-600' : 'from-green-500 to-green-600');
                        ?>
                        <div class="w-full bg-gray-200 rounded-full h-3 mb-2">
                            <div class="bg-gradient-to-r <?= $progressColor ?> h-3 rounded-full transition-all duration-500"
                                 style="width: <?= $usagePercent ?>%"></div>
                        </div>
                        <div class="flex items-center justify-between">
                            <p class="text-xs text-gray-600"><?= number_format($usagePercent, 1) ?>% full</p>
                            <p class="text-xs text-gray-600"><?= number_format($box['capacity'] - ($stats['document_count'] ?? 0)) ?> remaining</p>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Quick Actions</h4>
                        <div class="space-y-2">
                            <button onclick="moveBox()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                                📦 Move to Different Warehouse
                            </button>
                            <button onclick="sealBox()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                                🔒 Seal Box
                            </button>
                            <button onclick="generateReport()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                                📊 Generate Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bundles in Box -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center space-x-3">
                            <h2 class="text-xl font-bold text-gray-900">Bundles in Box</h2>
                            <span class="inline-flex items-center px-3 py-1 rounded-lg text-lg font-bold bg-purple-100 text-purple-800">
                                <?= count($bundles) ?>
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">Document bundles organized in this storage box</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="text-right">
                        <div class="text-sm font-medium text-gray-700">Total Bundles</div>
                        <div class="text-2xl font-bold text-purple-600"><?= count($bundles) ?></div>
                    </div>
                    <a href="<?= url('/app/bundles/create?box_id=' . $box['id']) ?>"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Bundle
                    </a>
                </div>
            </div>

            <?php if (empty($bundles)): ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No bundles yet</h3>
                    <p class="text-gray-600 mb-4">This box doesn't contain any document bundles yet.</p>
                    <a href="<?= url('/app/bundles/create?box_id=' . $box['id']) ?>"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
                        Create First Bundle
                    </a>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($bundles as $bundle): ?>
                        <div class="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200 group">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                        <a href="<?= url('/app/bundles/' . $bundle['id']) ?>">
                                            <?= e($bundle['name']) ?>
                                        </a>
                                    </h3>
                                    <?php if (!empty($bundle['reference_number'])): ?>
                                        <p class="text-sm font-mono text-gray-600 mt-1"><?= e($bundle['reference_number']) ?></p>
                                    <?php endif; ?>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-blue-100 text-blue-800">
                                        Position <?= $bundle['position_in_box'] ?? 'N/A' ?>
                                    </span>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <!-- Bundle Stats -->
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                                        <div class="text-lg font-bold text-blue-600"><?= number_format($bundle['document_count']) ?></div>
                                        <div class="text-xs text-blue-600">Documents</div>
                                    </div>
                                    <div class="text-center p-3 bg-green-50 rounded-lg">
                                        <div class="text-lg font-bold text-green-600"><?= formatFileSize($bundle['total_size']) ?></div>
                                        <div class="text-xs text-green-600">Size</div>
                                    </div>
                                </div>

                                <!-- Bundle Details -->
                                <div class="text-sm text-gray-600">
                                    <div class="flex items-center justify-between">
                                        <span>Category:</span>
                                        <span class="font-medium"><?= e($bundle['category'] ?? 'General') ?></span>
                                    </div>
                                    <div class="flex items-center justify-between mt-1">
                                        <span>Priority:</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium
                                            <?= $bundle['priority'] === 'high' ? 'bg-red-100 text-red-800' :
                                                ($bundle['priority'] === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') ?>">
                                            <?= ucfirst($bundle['priority'] ?? 'medium') ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between mt-1">
                                        <span>Added:</span>
                                        <span class="font-medium"><?= date('M j, Y', strtotime($bundle['added_at'])) ?></span>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex items-center justify-between pt-3 border-t border-gray-200">
                                    <a href="<?= url('/app/bundles/' . $bundle['id']) ?>"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details
                                    </a>
                                    <button onclick="removeBundleFromBox(<?= $bundle['id'] ?>)"
                                            class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Remove
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Enhanced Documents in Box -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Documents in Box</h2>
                    <p class="text-sm text-gray-600 mt-1">All documents stored in this box through bundles</p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-500"><?= count($documents) ?> documents</span>
                    <a href="<?= url('/app/documents/create?box_id=' . $box['id']) ?>"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Document
                    </a>
                </div>
            </div>

            <?php if (empty($documents)): ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No documents yet</h3>
                    <p class="text-gray-600 mb-4">This box doesn't contain any documents yet. Documents are added through bundles.</p>
                    <div class="flex items-center justify-center space-x-3">
                        <a href="<?= url('/app/documents/create?box_id=' . $box['id']) ?>"
                           class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200">
                            Add Document
                        </a>
                        <a href="<?= url('/app/bundles/create?box_id=' . $box['id']) ?>"
                           class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
                            Create Bundle First
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <!-- Document Type Filter -->
                <div class="mb-4 flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Filter by type:</label>
                        <select id="documentTypeFilter" class="text-sm border border-gray-300 rounded-lg px-3 py-1">
                            <option value="">All Types</option>
                            <option value="pdf">PDF</option>
                            <option value="image">Images</option>
                            <option value="document">Documents</option>
                            <option value="spreadsheet">Spreadsheets</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">Sort by:</label>
                        <select id="documentSortFilter" class="text-sm border border-gray-300 rounded-lg px-3 py-1">
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                            <option value="name">Name A-Z</option>
                            <option value="size">Size</option>
                        </select>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full" id="documentsTable">
                        <thead>
                            <tr class="border-b border-gray-200 bg-gray-50">
                                <th class="text-left py-4 px-6 font-semibold text-gray-700">Document</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-700">Type</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-700">Bundle</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-700">Size</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-700">Created</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-700">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($documents as $document): ?>
                                <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors document-row"
                                    data-type="<?= $document['file_type_icon'] ?>"
                                    data-created="<?= strtotime($document['created_at']) ?>"
                                    data-name="<?= strtolower($document['title']) ?>"
                                    data-size="<?= $document['file_size'] ?>">
                                    <td class="py-4 px-6">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 rounded-lg flex items-center justify-center
                                                <?= $document['file_type_icon'] === 'pdf' ? 'bg-red-100' :
                                                    ($document['file_type_icon'] === 'image' ? 'bg-green-100' :
                                                    ($document['file_type_icon'] === 'document' ? 'bg-blue-100' :
                                                    ($document['file_type_icon'] === 'spreadsheet' ? 'bg-yellow-100' : 'bg-gray-100'))) ?>">
                                                <?php
                                                $icons = [
                                                    'pdf' => '📄',
                                                    'image' => '🖼️',
                                                    'document' => '📝',
                                                    'spreadsheet' => '📊',
                                                    'file' => '📁'
                                                ];
                                                echo $icons[$document['file_type_icon']] ?? '📁';
                                                ?>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900"><?= e($document['title']) ?></p>
                                                <p class="text-sm text-gray-600"><?= e($document['file_name']) ?></p>
                                                <p class="text-xs text-gray-500">by <?= e($document['first_name'] . ' ' . $document['last_name']) ?></p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 px-6">
                                        <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium
                                            <?= $document['file_type_icon'] === 'pdf' ? 'bg-red-100 text-red-800' :
                                                ($document['file_type_icon'] === 'image' ? 'bg-green-100 text-green-800' :
                                                ($document['file_type_icon'] === 'document' ? 'bg-blue-100 text-blue-800' :
                                                ($document['file_type_icon'] === 'spreadsheet' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'))) ?>">
                                            <?= ucfirst($document['file_type_icon']) ?>
                                        </span>
                                    </td>
                                    <td class="py-4 px-6">
                                        <?php if ($document['bundle_name']): ?>
                                            <a href="<?= url('/app/bundles/' . $document['bundle_id']) ?>"
                                               class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors">
                                                <?= e($document['bundle_name']) ?>
                                            </a>
                                            <?php if (!empty($document['bundle_ref'])): ?>
                                                <p class="text-xs text-gray-500 mt-1"><?= e($document['bundle_ref']) ?></p>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-gray-400 italic">No bundle</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-4 px-6 text-sm text-gray-600">
                                        <?= formatFileSize($document['file_size']) ?>
                                    </td>
                                    <td class="py-4 px-6 text-sm text-gray-600">
                                        <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                        <p class="text-xs text-gray-500"><?= date('g:i A', strtotime($document['created_at'])) ?></p>
                                    </td>
                                    <td class="py-4 px-6">
                                        <div class="flex items-center space-x-2">
                                            <a href="<?= url('/app/documents/' . $document['id']) ?>"
                                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                View
                                            </a>
                                            <a href="<?= url('/app/documents/' . $document['id'] . '/download') ?>"
                                               class="text-green-600 hover:text-green-800 text-sm font-medium">
                                                Download
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Activity Timeline -->
        <?php if (!empty($timeline)): ?>
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl shadow-lg p-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Activity Timeline</h2>
                    <p class="text-sm text-gray-600 mt-1">Recent activity and changes to this box</p>
                </div>
                <div class="text-sm text-gray-500">
                    <?= count($timeline) ?> recent activities
                </div>
            </div>

            <div class="space-y-4">
                <?php foreach ($timeline as $activity): ?>
                    <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-xl">
                        <div class="w-10 h-10 rounded-full flex items-center justify-center
                            <?= $activity['event_type'] === 'box_created' ? 'bg-blue-100' :
                                ($activity['event_type'] === 'bundle_added' ? 'bg-green-100' : 'bg-purple-100') ?>">
                            <?php
                            $eventIcons = [
                                'box_created' => '📦',
                                'bundle_added' => '📁',
                                'document_added' => '📄'
                            ];
                            echo $eventIcons[$activity['event_type']] ?? '📋';
                            ?>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900">
                                        <?php
                                        switch ($activity['event_type']) {
                                            case 'box_created':
                                                echo 'Box Created';
                                                break;
                                            case 'bundle_added':
                                                echo 'Bundle Added: ' . e($activity['bundle_name']);
                                                break;
                                            case 'document_added':
                                                echo 'Document Added: ' . e($activity['document_title']);
                                                break;
                                            default:
                                                echo 'Activity';
                                        }
                                        ?>
                                    </p>
                                    <p class="text-sm text-gray-600">
                                        by <?= e($activity['first_name'] . ' ' . $activity['last_name']) ?>
                                    </p>
                                </div>
                                <div class="text-sm text-gray-500">
                                    <?= date('M j, Y g:i A', strtotime($activity['created_at'])) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

    </div>
</div>

<script>
// Enhanced Box Management JavaScript

// Print barcode function
function printBarcode(barcode) {
    const boxInfo = {
        name: <?= json_encode($box['name']) ?>,
        location: <?= json_encode($box['storage_location_code']) ?>,
        warehouse: <?= json_encode($box['warehouse_name']) ?>
    };

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Print Box Barcode</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                    .header { font-size: 18px; font-weight: bold; margin-bottom: 20px; color: #2563EB; }
                    .barcode { font-size: 28px; font-weight: bold; margin: 20px 0; letter-spacing: 3px; border: 2px solid #000; padding: 15px; background: #f9f9f9; }
                    .label { font-size: 14px; margin: 10px 0; }
                    .box-info { margin: 20px 0; font-size: 12px; background: #f0f0f0; padding: 15px; border-radius: 5px; }
                    .info-item { margin: 5px 0; }
                </style>
            </head>
            <body>
                <div class="header">Document Management System</div>
                <div class="label">Storage Box Barcode</div>
                <div class="barcode">\${barcode}</div>
                <div class="box-info">
                    <div class="info-item"><strong>Box:</strong> \${boxInfo.name}</div>
                    <div class="info-item"><strong>Location:</strong> \${boxInfo.location}</div>
                    <div class="info-item"><strong>Warehouse:</strong> \${boxInfo.warehouse}</div>
                </div>
                <div class="label">Scan to locate and manage box contents</div>
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                <\/script>
            </body>
        </html>
    `);
    printWindow.document.close();
}

// Generate QR Code
function generateQRCode() {
    const boxData = {
        id: <?= (int)$box['id'] ?>,
        box_id: <?= json_encode($box['box_id']) ?>,
        name: <?= json_encode($box['name']) ?>,
        location: <?= json_encode($box['storage_location_code']) ?>,
        warehouse: <?= json_encode($box['warehouse_name']) ?>,
        url: window.location.origin + '/dms/public/app/boxes/<?= $box['id'] ?>',
        capacity: <?= (int)$box['capacity'] ?>,
        status: <?= json_encode($box['occupancy_status']) ?>,
        bundles: <?= count($bundles) ?>,
        documents: <?= $stats['document_count'] ?? 0 ?>
    };

    const qrWindow = window.open('', '_blank', 'width=500,height=700');
    qrWindow.document.write(`
        <html>
            <head>
                <title>Box QR Code - ${boxData.box_id}</title>
                <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"><\/script>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        text-align: center;
                        padding: 20px;
                        background: #f8f9fa;
                        margin: 0;
                    }
                    .container {
                        background: white;
                        border-radius: 10px;
                        padding: 30px;
                        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                        max-width: 400px;
                        margin: 0 auto;
                    }
                    .header {
                        color: #1f2937;
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 10px;
                    }
                    .box-id {
                        font-size: 20px;
                        font-weight: bold;
                        color: #3b82f6;
                        background: #eff6ff;
                        padding: 10px;
                        border-radius: 8px;
                        margin: 15px 0;
                    }
                    .qr-container {
                        margin: 25px 0;
                        padding: 20px;
                        background: white;
                        border: 2px solid #e5e7eb;
                        border-radius: 10px;
                    }
                    .box-info {
                        margin: 20px 0;
                        font-size: 14px;
                        text-align: left;
                    }
                    .info-item {
                        margin: 8px 0;
                        display: flex;
                        justify-content: space-between;
                        padding: 5px 0;
                        border-bottom: 1px dotted #d1d5db;
                    }
                    .info-label {
                        font-weight: bold;
                        color: #374151;
                    }
                    .info-value {
                        color: #6b7280;
                    }
                    .buttons {
                        margin-top: 25px;
                        display: flex;
                        gap: 10px;
                        justify-content: center;
                    }
                    .btn {
                        padding: 12px 24px;
                        border: none;
                        border-radius: 8px;
                        cursor: pointer;
                        font-weight: 500;
                        transition: all 0.2s;
                    }
                    .btn-primary {
                        background: #3b82f6;
                        color: white;
                    }
                    .btn-primary:hover {
                        background: #2563eb;
                    }
                    .btn-secondary {
                        background: #6b7280;
                        color: white;
                    }
                    .btn-secondary:hover {
                        background: #4b5563;
                    }
                    @media print {
                        body { background: white; }
                        .buttons { display: none; }
                        .container { box-shadow: none; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">📦 Storage Box QR Code</div>
                    <div class="box-id">${boxData.box_id}</div>

                    <div class="qr-container">
                        <canvas id="qrcode"></canvas>
                    </div>

                    <div class="box-info">
                        <div class="info-item">
                            <span class="info-label">Box Name:</span>
                            <span class="info-value">${boxData.name}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Location:</span>
                            <span class="info-value">${boxData.location}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Warehouse:</span>
                            <span class="info-value">${boxData.warehouse}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Capacity:</span>
                            <span class="info-value">${boxData.capacity.toLocaleString()} documents</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Status:</span>
                            <span class="info-value">${boxData.status.charAt(0).toUpperCase() + boxData.status.slice(1)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Bundles:</span>
                            <span class="info-value">${boxData.bundles}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Documents:</span>
                            <span class="info-value">${boxData.documents}</span>
                        </div>
                    </div>

                    <div class="buttons">
                        <button onclick="window.print()" class="btn btn-primary">🖨️ Print QR Code</button>
                        <button onclick="downloadQR()" class="btn btn-secondary">💾 Download</button>
                        <button onclick="window.close()" class="btn btn-secondary">✖️ Close</button>
                    </div>
                </div>

                <script>
                    let qrCanvas;

                    window.onload = function() {
                        qrCanvas = document.getElementById('qrcode');
                        QRCode.toCanvas(qrCanvas, boxData.url, {
                            width: 200,
                            margin: 2,
                            color: {
                                dark: '#000000',
                                light: '#FFFFFF'
                            },
                            errorCorrectionLevel: 'M'
                        }, function(error) {
                            if (error) {
                                console.error('QR Code generation failed:', error);
                                document.querySelector('.qr-container').innerHTML = '<p style="color: red;">QR Code generation failed</p>';
                            }
                        });
                    };

                    function downloadQR() {
                        if (qrCanvas) {
                            const link = document.createElement('a');
                            link.download = 'box-qr-' + boxData.box_id + '.png';
                            link.href = qrCanvas.toDataURL();
                            link.click();
                        }
                    }
                <\/script>
            </body>
        </html>
    `);
    qrWindow.document.close();
}

// Print comprehensive label
function printLabel() {
    const boxInfo = {
        box_id: <?= json_encode($box['box_id']) ?>,
        name: <?= json_encode($box['name']) ?>,
        location: <?= json_encode($box['storage_location_code']) ?>,
        warehouse: <?= json_encode($box['warehouse_name']) ?>,
        warehouse_address: <?= json_encode($box['warehouse_city'] . ', ' . $box['warehouse_state']) ?>,
        capacity: <?= (int)$box['capacity'] ?>,
        status: <?= json_encode(ucfirst($box['occupancy_status'])) ?>,
        created: <?= json_encode(date('M j, Y', strtotime($box['created_at']))) ?>,
        barcode: <?= json_encode($box['barcode_value']) ?>,
        bundles: <?= count($bundles) ?>,
        documents: <?= $stats['document_count'] ?? 0 ?>,
        row: <?= json_encode($box['row_number'] ?? 'N/A') ?>,
        shelf: <?= json_encode($box['shelf_number'] ?? 'N/A') ?>,
        position: <?= json_encode($box['position_number'] ?? 'N/A') ?>,
        storage_type: <?= json_encode($box['storage_type'] ?? 'physical') ?>
    };

    const printWindow = window.open('', '_blank', 'width=800,height=600');
    printWindow.document.write(`
        <html>
            <head>
                <title>Storage Box Label - ${boxInfo.box_id}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        padding: 20px;
                        margin: 0;
                        background: #f8f9fa;
                    }
                    .label-container {
                        display: flex;
                        gap: 20px;
                        justify-content: center;
                        flex-wrap: wrap;
                    }
                    .label {
                        border: 3px solid #000;
                        padding: 20px;
                        width: 350px;
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }
                    .header {
                        text-align: center;
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 15px;
                        color: #1f2937;
                        background: #f3f4f6;
                        padding: 10px;
                        border-radius: 5px;
                    }
                    .box-id {
                        text-align: center;
                        font-size: 28px;
                        font-weight: bold;
                        margin: 15px 0;
                        background: #3b82f6;
                        color: white;
                        padding: 15px;
                        border-radius: 8px;
                        letter-spacing: 2px;
                    }
                    .location {
                        text-align: center;
                        font-size: 16px;
                        margin: 10px 0;
                        font-weight: bold;
                        color: #059669;
                        background: #d1fae5;
                        padding: 8px;
                        border-radius: 5px;
                    }
                    .details { margin: 15px 0; }
                    .detail-row {
                        display: flex;
                        justify-content: space-between;
                        margin: 6px 0;
                        padding: 8px 0;
                        border-bottom: 1px dotted #d1d5db;
                        font-size: 14px;
                    }
                    .detail-label {
                        font-weight: bold;
                        color: #374151;
                        min-width: 100px;
                    }
                    .detail-value {
                        color: #6b7280;
                        text-align: right;
                        flex: 1;
                    }
                    .position-info {
                        background: #fef3c7;
                        padding: 10px;
                        border-radius: 5px;
                        margin: 10px 0;
                        text-align: center;
                        font-size: 12px;
                        border: 1px solid #f59e0b;
                    }
                    .position-title {
                        font-weight: bold;
                        color: #92400e;
                        margin-bottom: 5px;
                    }
                    .barcode {
                        text-align: center;
                        font-family: 'Courier New', monospace;
                        font-size: 11px;
                        margin: 15px 0;
                        background: #f9fafb;
                        padding: 12px;
                        border: 2px solid #e5e7eb;
                        border-radius: 5px;
                        word-break: break-all;
                    }
                    .footer {
                        text-align: center;
                        font-size: 10px;
                        color: #6b7280;
                        margin-top: 15px;
                        border-top: 1px solid #e5e7eb;
                        padding-top: 10px;
                    }
                    .status-badge {
                        display: inline-block;
                        padding: 4px 8px;
                        border-radius: 12px;
                        font-size: 11px;
                        font-weight: bold;
                        text-transform: uppercase;
                    }
                    .status-empty { background: #f3f4f6; color: #374151; }
                    .status-partial { background: #fef3c7; color: #92400e; }
                    .status-full { background: #fee2e2; color: #991b1b; }
                    .status-sealed { background: #dbeafe; color: #1e40af; }
                    .print-buttons {
                        text-align: center;
                        margin: 20px 0;
                    }
                    .btn {
                        padding: 10px 20px;
                        margin: 0 5px;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                        font-weight: 500;
                    }
                    .btn-primary { background: #3b82f6; color: white; }
                    .btn-secondary { background: #6b7280; color: white; }
                    @media print {
                        body { background: white; }
                        .print-buttons { display: none; }
                        .label-container { gap: 10px; }
                        .label {
                            box-shadow: none;
                            page-break-inside: avoid;
                            margin-bottom: 20px;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="print-buttons">
                    <button onclick="window.print()" class="btn btn-primary">🖨️ Print Label</button>
                    <button onclick="window.close()" class="btn btn-secondary">✖️ Close</button>
                </div>

                <div class="label-container">
                    <!-- Main Label -->
                    <div class="label">
                        <div class="header">📦 DOCUMENT STORAGE BOX</div>
                        <div class="box-id">\${boxInfo.box_id}</div>
                        <div class="location">📍 \${boxInfo.location}</div>

                        <div class="position-info">
                            <div class="position-title">Physical Position</div>
                            <div>Row: \${boxInfo.row} | Shelf: \${boxInfo.shelf} | Position: \${boxInfo.position}</div>
                        </div>

                        <div class="details">
                            <div class="detail-row">
                                <span class="detail-label">Name:</span>
                                <span class="detail-value">\${boxInfo.name}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Warehouse:</span>
                                <span class="detail-value">\${boxInfo.warehouse}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Location:</span>
                                <span class="detail-value">\${boxInfo.warehouse_address}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Capacity:</span>
                                <span class="detail-value">\${boxInfo.capacity.toLocaleString()} docs</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Status:</span>
                                <span class="detail-value">
                                    <span class="status-badge status-\${boxInfo.status.toLowerCase()}">\${boxInfo.status}</span>
                                </span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Bundles:</span>
                                <span class="detail-value">\${boxInfo.bundles}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Documents:</span>
                                <span class="detail-value">\${boxInfo.documents}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Type:</span>
                                <span class="detail-value">\${boxInfo.storage_type === 'physical' ? '📦 Physical' : '☁️ Digital'}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Created:</span>
                                <span class="detail-value">\${boxInfo.created}</span>
                            </div>
                        </div>

                        <div class="barcode">
                            <strong>BARCODE:</strong><br>
                            \${boxInfo.barcode}
                        </div>

                        <div class="footer">
                            Document Management System<br>
                            Storage Label - Generated: \${new Date().toLocaleDateString()}
                        </div>
                    </div>

                    <!-- Compact Label for Small Spaces -->
                    <div class="label" style="width: 250px;">
                        <div class="header" style="font-size: 14px;">📦 STORAGE BOX</div>
                        <div class="box-id" style="font-size: 20px; padding: 10px;">\${boxInfo.box_id}</div>
                        <div class="location" style="font-size: 14px;">📍 \${boxInfo.location}</div>

                        <div style="text-align: center; margin: 10px 0; font-size: 12px;">
                            <div><strong>\${boxInfo.warehouse}</strong></div>
                            <div style="margin: 5px 0;">
                                Row: \${boxInfo.row} | Shelf: \${boxInfo.shelf}<br>
                                Position: \${boxInfo.position}
                            </div>
                            <div style="margin: 8px 0;">
                                <span class="status-badge status-\${boxInfo.status.toLowerCase()}" style="font-size: 10px;">\${boxInfo.status}</span>
                            </div>
                            <div style="margin: 5px 0;">
                                📁 \${boxInfo.bundles} bundles | 📄 \${boxInfo.documents} docs
                            </div>
                        </div>

                        <div class="barcode" style="font-size: 10px; padding: 8px;">
                            <strong>BARCODE:</strong><br>
                            \${boxInfo.barcode}
                        </div>

                        <div class="footer" style="font-size: 8px;">
                            DMS - \${new Date().toLocaleDateString()}
                        </div>
                    </div>
                </div>

                <script>
                    // Auto-print after a short delay to ensure content is loaded
                    setTimeout(function() {
                        // Uncomment the next line if you want auto-print
                        // window.print();
                    }, 500);

                    window.onafterprint = function() {
                        // Uncomment the next line if you want auto-close after printing
                        // window.close();
                    };
                <\/script>
            </body>
        </html>
    `);
    printWindow.document.close();
}

// Document filtering and sorting
document.addEventListener('DOMContentLoaded', function() {
    const typeFilter = document.getElementById('documentTypeFilter');
    const sortFilter = document.getElementById('documentSortFilter');
    const table = document.getElementById('documentsTable');

    if (typeFilter && sortFilter && table) {
        function filterAndSort() {
            const rows = Array.from(table.querySelectorAll('.document-row'));
            const typeValue = typeFilter.value;
            const sortValue = sortFilter.value;

            // Filter
            rows.forEach(row => {
                if (!typeValue || row.dataset.type === typeValue) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Sort visible rows
            const visibleRows = rows.filter(row => row.style.display !== 'none');
            visibleRows.sort((a, b) => {
                switch (sortValue) {
                    case 'newest':
                        return parseInt(b.dataset.created) - parseInt(a.dataset.created);
                    case 'oldest':
                        return parseInt(a.dataset.created) - parseInt(b.dataset.created);
                    case 'name':
                        return a.dataset.name.localeCompare(b.dataset.name);
                    case 'size':
                        return parseInt(b.dataset.size) - parseInt(a.dataset.size);
                    default:
                        return 0;
                }
            });

            // Reorder in DOM
            const tbody = table.querySelector('tbody');
            visibleRows.forEach(row => tbody.appendChild(row));
        }

        typeFilter.addEventListener('change', filterAndSort);
        sortFilter.addEventListener('change', filterAndSort);
    }
});

// Additional functions for box management
function moveBox() {
    alert('Move box functionality - to be implemented');
}

function sealBox() {
    if (confirm('Are you sure you want to seal this box? This will prevent further modifications.')) {
        // Implementation for sealing box
        alert('Box sealing functionality - to be implemented');
    }
}

function generateReport() {
    window.open('<?= url('/app/boxes/' . $box['id'] . '/report') ?>', '_blank');
}

function removeBundleFromBox(bundleId) {
    if (confirm('Are you sure you want to remove this bundle from the box?')) {
        // Implementation for removing bundle
        alert('Remove bundle functionality - to be implemented');
    }
}

function toggleAllIntakes() {
    // Implementation for showing all intake entries
    alert('Show all intakes functionality - to be implemented');
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
