<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?? 'Client Portal' ?> - DMS</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .hover-scale {
            transition: transform 0.2s ease-in-out;
        }
        
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                
                <!-- Logo and Brand -->
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <span class="text-xl font-bold text-gray-900">DMS Client Portal</span>
                    </div>
                    
                    <!-- Navigation Links -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="<?= url('/client/dashboard') ?>" 
                           class="<?= strpos($_SERVER['REQUEST_URI'], '/client/dashboard') !== false ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            Dashboard
                        </a>
                        
                        <a href="<?= url('/client/documents') ?>" 
                           class="<?= strpos($_SERVER['REQUEST_URI'], '/client/documents') !== false ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors">
                            <i class="fas fa-file-alt mr-2"></i>
                            My Documents
                        </a>
                        
                        <a href="<?= url('/client/requests') ?>" 
                           class="<?= strpos($_SERVER['REQUEST_URI'], '/client/requests') !== false ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors">
                            <i class="fas fa-clipboard-list mr-2"></i>
                            Requests
                        </a>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="flex items-center">
                    
                    <!-- Search Button -->
                    <button type="button" 
                            onclick="toggleSearch()"
                            class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 mr-3">
                        <i class="fas fa-search"></i>
                    </button>
                    
                    <!-- User Dropdown -->
                    <div class="relative">
                        <button type="button" 
                                onclick="toggleUserMenu()"
                                class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium">
                                    <?= strtoupper(substr($user['first_name'] ?? 'U', 0, 1)) ?>
                                </span>
                            </div>
                            <span class="ml-3 text-gray-700 text-sm font-medium hidden md:block">
                                <?= e($user['first_name'] ?? 'User') ?> <?= e($user['last_name'] ?? '') ?>
                            </span>
                            <i class="fas fa-chevron-down ml-2 text-gray-400 text-xs"></i>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div id="userMenu" 
                             class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                            <div class="px-4 py-2 border-b border-gray-100">
                                <p class="text-sm text-gray-900 font-medium"><?= e($user['first_name'] ?? 'User') ?> <?= e($user['last_name'] ?? '') ?></p>
                                <p class="text-xs text-gray-500"><?= e($user['email'] ?? '') ?></p>
                            </div>
                            
                            <a href="<?= url('/client/profile') ?>" 
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user mr-2"></i>
                                Profile Settings
                            </a>
                            
                            <a href="<?= url('/client/help') ?>" 
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-question-circle mr-2"></i>
                                Help & Support
                            </a>
                            
                            <div class="border-t border-gray-100"></div>
                            
                            <a href="<?= url('/logout') ?>" 
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>
                                Sign Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div id="mobileMenu" class="hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
                <a href="<?= url('/client/dashboard') ?>" 
                   class="<?= strpos($_SERVER['REQUEST_URI'], '/client/dashboard') !== false ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                </a>
                
                <a href="<?= url('/client/documents') ?>" 
                   class="<?= strpos($_SERVER['REQUEST_URI'], '/client/documents') !== false ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                    <i class="fas fa-file-alt mr-2"></i>
                    My Documents
                </a>
                
                <a href="<?= url('/client/requests') ?>" 
                   class="<?= strpos($_SERVER['REQUEST_URI'], '/client/requests') !== false ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                    <i class="fas fa-clipboard-list mr-2"></i>
                    Requests
                </a>
            </div>
        </div>
    </nav>

    <!-- Search Overlay -->
    <div id="searchOverlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50">
        <div class="flex items-start justify-center pt-16 px-4">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Search Documents</h3>
                        <button onclick="toggleSearch()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form action="<?= url('/client/documents') ?>" method="GET" class="space-y-4">
                        <div>
                            <input type="text" 
                                   name="search" 
                                   placeholder="Search documents, bundles, or descriptions..."
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <select name="year" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">All Years</option>
                                <?php for ($year = date('Y'); $year >= 2020; $year--): ?>
                                    <option value="<?= $year ?>"><?= $year ?></option>
                                <?php endfor; ?>
                            </select>
                            
                            <select name="department" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">All Departments</option>
                                <option value="HR">HR</option>
                                <option value="Finance">Finance</option>
                                <option value="Legal">Legal</option>
                                <option value="IT">IT</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Operations">Operations</option>
                            </select>
                            
                            <select name="document_type" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">All Types</option>
                                <option value="contract">Contract</option>
                                <option value="invoice">Invoice</option>
                                <option value="receipt">Receipt</option>
                                <option value="report">Report</option>
                                <option value="correspondence">Correspondence</option>
                                <option value="legal">Legal Document</option>
                                <option value="financial">Financial Document</option>
                            </select>
                        </div>
                        
                        <div class="flex justify-end space-x-3">
                            <button type="button" 
                                    onclick="toggleSearch()"
                                    class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div id="flashMessage" class="fixed top-4 right-4 z-50 max-w-sm">
            <div class="bg-<?= $_SESSION['flash_type'] === 'error' ? 'red' : ($_SESSION['flash_type'] === 'warning' ? 'yellow' : 'green') ?>-100 border border-<?= $_SESSION['flash_type'] === 'error' ? 'red' : ($_SESSION['flash_type'] === 'warning' ? 'yellow' : 'green') ?>-400 text-<?= $_SESSION['flash_type'] === 'error' ? 'red' : ($_SESSION['flash_type'] === 'warning' ? 'yellow' : 'green') ?>-700 px-4 py-3 rounded-lg shadow-lg">
                <div class="flex items-center justify-between">
                    <span><?= e($_SESSION['flash_message']) ?></span>
                    <button onclick="closeFlashMessage()" class="ml-4 text-<?= $_SESSION['flash_type'] === 'error' ? 'red' : ($_SESSION['flash_type'] === 'warning' ? 'yellow' : 'green') ?>-500 hover:text-<?= $_SESSION['flash_type'] === 'error' ? 'red' : ($_SESSION['flash_type'] === 'warning' ? 'yellow' : 'green') ?>-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
        <?php 
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        ?>
    <?php endif; ?>

    <script>
        // Toggle user menu
        function toggleUserMenu() {
            const menu = document.getElementById('userMenu');
            menu.classList.toggle('hidden');
        }

        // Toggle mobile menu
        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        }

        // Toggle search overlay
        function toggleSearch() {
            const overlay = document.getElementById('searchOverlay');
            overlay.classList.toggle('hidden');
            if (!overlay.classList.contains('hidden')) {
                overlay.querySelector('input[name="search"]').focus();
            }
        }

        // Close flash message
        function closeFlashMessage() {
            const message = document.getElementById('flashMessage');
            if (message) {
                message.remove();
            }
        }

        // Auto-hide flash message after 5 seconds
        setTimeout(() => {
            closeFlashMessage();
        }, 5000);

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('userMenu');
            const userButton = event.target.closest('[onclick="toggleUserMenu()"]');
            
            if (!userButton && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // Close search overlay with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const searchOverlay = document.getElementById('searchOverlay');
                if (!searchOverlay.classList.contains('hidden')) {
                    toggleSearch();
                }
            }
        });
    </script>
