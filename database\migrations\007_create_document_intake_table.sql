-- Document Intake Table Migration
-- This table manages the intake process for documents before they are organized into bundles/boxes

CREATE TABLE IF NOT EXISTS document_intake (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    reference_number VARCHAR(50) UNIQUE,

    -- Client Information (as per documentation)
    client_name VARCHAR(255) NOT NULL,
    client_id VARCHAR(50),

    -- Intake Information
    source ENUM('email', 'physical_mail', 'scan', 'upload', 'fax', 'courier', 'walk_in', 'other') NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    expected_count INT DEFAULT 1,
    actual_count INT DEFAULT 0,

    -- Document Metadata (as per documentation)
    date_range_start DATE NULL,
    date_range_end DATE NULL,
    sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    department VARCHAR(100) NULL,

    -- Processing Information
    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
    bundle_id INT NULL,
    box_id INT NULL,

    -- Notes and Comments
    notes TEXT NULL,
    processing_notes TEXT NULL,
    completion_notes TEXT NULL,
    
    -- Timestamps and Users
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_by INT NULL,
    processed_at TIMESTAMP NULL,
    completed_by INT NULL,
    completed_at TIMESTAMP NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (completed_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (bundle_id) REFERENCES bundles(id) ON DELETE SET NULL,
    
    -- Indexes for Performance
    INDEX idx_company_status (company_id, status),
    INDEX idx_company_created (company_id, created_at),
    INDEX idx_reference (reference_number),
    INDEX idx_priority (priority),
    INDEX idx_source (source)
);

-- Add intake_id column to documents table to link documents to intake entries
ALTER TABLE documents 
ADD COLUMN intake_id INT NULL AFTER id,
ADD FOREIGN KEY (intake_id) REFERENCES document_intake(id) ON DELETE SET NULL,
ADD INDEX idx_intake_id (intake_id);

-- Insert sample intake data for demonstration
INSERT INTO document_intake (
    company_id, reference_number, source, document_type, description, 
    priority, expected_count, notes, status, created_by
) VALUES 
(1, 'INT-2024-000001', 'email', 'invoice', 'Monthly invoices from suppliers received via email', 'medium', 5, '<NAME_EMAIL>', 'pending', 1),
(1, 'INT-2024-000002', 'physical_mail', 'contract', 'New client contract documents delivered by courier', 'high', 3, 'Urgent - client meeting tomorrow', 'pending', 1),
(1, 'INT-2024-000003', 'scan', 'receipt', 'Expense receipts scanned from physical copies', 'low', 10, 'Monthly expense processing', 'processing', 1),
(1, 'INT-2024-000004', 'upload', 'report', 'Quarterly financial reports uploaded by finance team', 'urgent', 1, 'Board meeting preparation', 'completed', 1);

-- Create intake activity log table for detailed tracking
CREATE TABLE IF NOT EXISTS intake_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    intake_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    old_status VARCHAR(20),
    new_status VARCHAR(20),
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (intake_id) REFERENCES document_intake(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_intake_activity (intake_id, created_at),
    INDEX idx_user_activity (user_id, created_at)
);
