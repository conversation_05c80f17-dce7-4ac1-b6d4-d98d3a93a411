<?php
/**
 * Debug Routing <PERSON>ript
 * 
 * This script helps debug routing issues by showing exactly what's happening
 */

// Start session
session_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

echo "<h1>DMS Routing Debug</h1>";

try {
    // Create router instance
    $router = new \App\Core\Router();
    
    // Load routes
    require_once APP_ROOT . '/src/routes.php';
    
    // Get current request info
    $requestUri = $_SERVER['REQUEST_URI'] ?? '/super-admin/dashboard';
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '/dms/public/index.php';
    
    echo "<h2>Request Information</h2>";
    echo "<p><strong>Original REQUEST_URI:</strong> " . htmlspecialchars($requestUri) . "</p>";
    echo "<p><strong>REQUEST_METHOD:</strong> " . htmlspecialchars($requestMethod) . "</p>";
    echo "<p><strong>SCRIPT_NAME:</strong> " . htmlspecialchars($scriptName) . "</p>";
    
    // Process URI like index.php does
    $originalUri = $requestUri;
    
    // Remove query string from URI
    $requestUri = strtok($requestUri, '?');
    echo "<p><strong>After query removal:</strong> " . htmlspecialchars($requestUri) . "</p>";
    
    // Remove base path if application is in subdirectory
    $basePath = dirname($scriptName);
    echo "<p><strong>Detected base path:</strong> " . htmlspecialchars($basePath) . "</p>";
    
    // Handle case where app is in subdirectory like /dms/public/index.php
    if (strpos($scriptName, '/dms/public/index.php') !== false) {
        $basePath = '/dms';
        echo "<p><strong>Adjusted base path for DMS:</strong> " . htmlspecialchars($basePath) . "</p>";
    }
    
    if ($basePath !== '/' && $basePath !== '' && strpos($requestUri, $basePath) === 0) {
        $requestUri = substr($requestUri, strlen($basePath));
        echo "<p><strong>After base path removal:</strong> " . htmlspecialchars($requestUri) . "</p>";
    }
    
    // Ensure URI starts with /
    if (!$requestUri || $requestUri[0] !== '/') {
        $requestUri = '/' . $requestUri;
        echo "<p><strong>After ensuring leading slash:</strong> " . htmlspecialchars($requestUri) . "</p>";
    }
    
    echo "<p><strong>Final processed URI:</strong> " . htmlspecialchars($requestUri) . "</p>";
    
    // Get all routes using reflection
    $reflection = new ReflectionClass($router);
    $routesProperty = $reflection->getProperty('routes');
    $routesProperty->setAccessible(true);
    $routes = $routesProperty->getValue($router);
    
    echo "<h2>Route Analysis</h2>";
    echo "<p><strong>Total routes registered:</strong> " . count($routes) . "</p>";
    
    // Find super-admin routes
    $superAdminRoutes = array_filter($routes, function($route) {
        return strpos($route['path'], '/super-admin') !== false;
    });
    
    echo "<h3>Super Admin Routes (" . count($superAdminRoutes) . " found)</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Method</th><th>Path</th><th>Pattern</th><th>Handler</th><th>Middlewares</th></tr>";
    
    foreach ($superAdminRoutes as $route) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($route['method']) . "</td>";
        echo "<td>" . htmlspecialchars($route['path']) . "</td>";
        echo "<td>" . htmlspecialchars($route['pattern']) . "</td>";
        echo "<td>" . htmlspecialchars($route['handler']) . "</td>";
        echo "<td>" . htmlspecialchars(implode(', ', $route['middlewares'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test route matching
    echo "<h3>Route Matching Test</h3>";
    echo "<p>Testing: <strong>{$requestMethod} {$requestUri}</strong></p>";
    
    $matchFound = false;
    foreach ($routes as $route) {
        if ($route['method'] === $requestMethod) {
            echo "<p>Testing pattern: <code>" . htmlspecialchars($route['pattern']) . "</code> for path: <code>" . htmlspecialchars($route['path']) . "</code></p>";
            
            if (preg_match($route['pattern'], $requestUri, $matches)) {
                echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>✅ MATCH FOUND!</h4>";
                echo "<p><strong>Route:</strong> " . htmlspecialchars($route['path']) . "</p>";
                echo "<p><strong>Handler:</strong> " . htmlspecialchars($route['handler']) . "</p>";
                echo "<p><strong>Pattern:</strong> " . htmlspecialchars($route['pattern']) . "</p>";
                echo "<p><strong>Matches:</strong> " . htmlspecialchars(json_encode($matches)) . "</p>";
                echo "<p><strong>Middlewares:</strong> " . htmlspecialchars(implode(', ', $route['middlewares'])) . "</p>";
                echo "</div>";
                $matchFound = true;
                break;
            }
        }
    }
    
    if (!$matchFound) {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ NO MATCH FOUND</h4>";
        echo "<p>No route matches the request: <strong>{$requestMethod} {$requestUri}</strong></p>";
        echo "</div>";
    }
    
    // Test SuperAdminController
    echo "<h2>Controller Test</h2>";
    
    if (class_exists('App\\Controllers\\SuperAdminController')) {
        echo "<p>✅ SuperAdminController class exists</p>";
        
        try {
            $controller = new \App\Controllers\SuperAdminController();
            echo "<p>✅ SuperAdminController instantiated successfully</p>";
            
            if (method_exists($controller, 'dashboard')) {
                echo "<p>✅ dashboard method exists</p>";
            } else {
                echo "<p>❌ dashboard method missing</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Error instantiating controller: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p>❌ SuperAdminController class not found</p>";
    }
    
    // Test authentication
    echo "<h2>Authentication Test</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<p>✅ User is logged in (ID: {$_SESSION['user_id']})</p>";
        
        if (isset($_SESSION['user']['role'])) {
            echo "<p>✅ User role: " . htmlspecialchars($_SESSION['user']['role']) . "</p>";
            
            if ($_SESSION['user']['role'] === 'super_admin') {
                echo "<p>✅ User has super_admin role</p>";
            } else {
                echo "<p>❌ User does not have super_admin role</p>";
            }
        } else {
            echo "<p>❌ User role not set in session</p>";
        }
    } else {
        echo "<p>❌ User is not logged in</p>";
        echo "<p><strong>Note:</strong> You need to login first at <a href='/dms/login'>/dms/login</a></p>";
    }
    
    // Test direct access
    echo "<h2>Direct Access Test</h2>";
    echo "<p>Try accessing the super admin dashboard directly:</p>";
    echo "<p><a href='/dms/super-admin/dashboard' target='_blank'>Test Super Admin Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ ERROR</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}
?>
