<?php
/**
 * Complete Database Setup for DMS
 * 
 * This script will:
 * 1. Check MySQL connection
 * 2. Create database if needed
 * 3. Create all required tables
 * 4. Create demo data
 */

echo "Document Management System - Database Setup\n";
echo str_repeat("=", 60) . "\n";

try {
    // Step 1: Test MySQL connection
    echo "Step 1: Testing MySQL connection...\n";
    
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    try {
        $pdo = new PDO("mysql:host={$host}", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "✓ MySQL connection successful\n";
    } catch (Exception $e) {
        throw new Exception("Cannot connect to MySQL: " . $e->getMessage() . "\nPlease start MySQL in XAMPP Control Panel");
    }
    
    // Step 2: Create database
    echo "\nStep 2: Creating database...\n";
    
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS dms_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✓ Database 'dms_system' created/verified\n";
        
        $pdo->exec("USE dms_system");
        echo "✓ Using database 'dms_system'\n";
    } catch (Exception $e) {
        throw new Exception("Cannot create database: " . $e->getMessage());
    }
    
    // Step 3: Create tables
    echo "\nStep 3: Creating tables...\n";
    
    $tables = [
        'companies' => "
            CREATE TABLE IF NOT EXISTS companies (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                domain VARCHAR(255) UNIQUE,
                address TEXT,
                phone VARCHAR(20),
                email VARCHAR(255),
                subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
                storage_limit BIGINT DEFAULT 5368709120,
                storage_used BIGINT DEFAULT 0,
                status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                username VARCHAR(100) UNIQUE NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer') NOT NULL,
                phone VARCHAR(20),
                last_login TIMESTAMP NULL,
                login_attempts INT DEFAULT 0,
                locked_until TIMESTAMP NULL,
                email_verified BOOLEAN DEFAULT FALSE,
                status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'warehouses' => "
            CREATE TABLE IF NOT EXISTS warehouses (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                code VARCHAR(50) NOT NULL,
                description TEXT,
                address TEXT,
                total_capacity DECIMAL(10,2),
                used_capacity DECIMAL(10,2) DEFAULT 0,
                capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
                status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'storage_locations' => "
            CREATE TABLE IF NOT EXISTS storage_locations (
                id INT PRIMARY KEY AUTO_INCREMENT,
                warehouse_id INT NOT NULL,
                parent_id INT NULL,
                type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
                identifier VARCHAR(100) NOT NULL,
                name VARCHAR(255),
                description TEXT,
                capacity DECIMAL(10,2),
                used_capacity DECIMAL(10,2) DEFAULT 0,
                barcode_value VARCHAR(255) UNIQUE,
                status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'document_categories' => "
            CREATE TABLE IF NOT EXISTS document_categories (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                color VARCHAR(7),
                icon VARCHAR(50),
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'documents' => "
            CREATE TABLE IF NOT EXISTS documents (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                title VARCHAR(500) NOT NULL,
                description TEXT,
                file_name VARCHAR(500) NOT NULL,
                file_path VARCHAR(1000) NOT NULL,
                file_size BIGINT NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                file_hash VARCHAR(64) NOT NULL,
                version VARCHAR(20) DEFAULT '1.0',
                document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
                category_id INT,
                download_count INT DEFAULT 0,
                view_count INT DEFAULT 0,
                created_by INT NOT NULL,
                status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'bundles' => "
            CREATE TABLE IF NOT EXISTS bundles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                bundle_type ENUM('intake', 'project', 'department', 'custom') DEFAULT 'custom',
                location_id INT,
                created_by INT NOT NULL,
                status ENUM('open', 'closed', 'archived') DEFAULT 'open',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'barcodes' => "
            CREATE TABLE IF NOT EXISTS barcodes (
                id INT PRIMARY KEY AUTO_INCREMENT,
                entity_type ENUM('document', 'location', 'box', 'bundle') NOT NULL,
                entity_id INT NOT NULL,
                barcode_value VARCHAR(255) UNIQUE NOT NULL,
                barcode_type ENUM('qr', 'code128', 'code39', 'ean13', 'datamatrix') DEFAULT 'qr',
                is_active BOOLEAN DEFAULT TRUE,
                generated_by INT NOT NULL,
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                scan_count INT DEFAULT 0
            )",
        
        'audit_logs' => "
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                user_id INT,
                entity_type VARCHAR(50) NOT NULL,
                entity_id INT,
                action ENUM('create', 'read', 'update', 'delete', 'login', 'logout', 'download', 'upload', 'scan') NOT NULL,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"
    ];
    
    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            echo "✓ Created table: {$tableName}\n";
        } catch (Exception $e) {
            echo "⚠ Table {$tableName}: " . $e->getMessage() . "\n";
        }
    }
    
    // Step 4: Verify tables
    echo "\nStep 4: Verifying tables...\n";
    
    $stmt = $pdo->query("SHOW TABLES");
    $createdTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Tables in database:\n";
    foreach ($createdTables as $table) {
        echo "✓ {$table}\n";
    }
    
    // Step 5: Create demo company and user
    echo "\nStep 5: Creating demo data...\n";
    
    // Check if demo company exists
    $stmt = $pdo->prepare("SELECT id FROM companies WHERE name = 'Demo Company'");
    $stmt->execute();
    $company = $stmt->fetch();
    
    if (!$company) {
        $stmt = $pdo->prepare("
            INSERT INTO companies (name, domain, email, address, phone, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'Demo Company',
            'demo.local',
            '<EMAIL>',
            '123 Demo Street, Demo City, DC 12345',
            '+****************',
            'active'
        ]);
        $companyId = $pdo->lastInsertId();
        echo "✓ Created demo company (ID: {$companyId})\n";
    } else {
        $companyId = $company['id'];
        echo "✓ Demo company already exists (ID: {$companyId})\n";
    }
    
    // Check if demo user exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if (!$user) {
        $stmt = $pdo->prepare("
            INSERT INTO users (company_id, username, email, password_hash, first_name, last_name, role, status, email_verified) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $companyId,
            'admin',
            '<EMAIL>',
            password_hash('admin123', PASSWORD_DEFAULT),
            'System',
            'Administrator',
            'company_admin',
            'active',
            true
        ]);
        $userId = $pdo->lastInsertId();
        echo "✓ Created demo admin user (ID: {$userId})\n";
    } else {
        echo "✓ Demo admin user already exists\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎉 DATABASE SETUP COMPLETE!\n";
    echo str_repeat("=", 60) . "\n";
    echo "Database: dms_system\n";
    echo "Tables created: " . count($createdTables) . "\n";
    echo "\nDemo Login Credentials:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
    echo "\nLogin URL: http://localhost/dms/public/login\n";
    echo str_repeat("=", 60) . "\n";
    
} catch (Exception $e) {
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "❌ SETUP FAILED\n";
    echo str_repeat("=", 60) . "\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "\nTroubleshooting:\n";
    echo "1. Start XAMPP Control Panel\n";
    echo "2. Start Apache and MySQL services\n";
    echo "3. Check MySQL is running on port 3306\n";
    echo "4. Verify MySQL credentials (root/no password)\n";
    echo str_repeat("=", 60) . "\n";
}
?>
