<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;

/**
 * API User Controller
 * 
 * Handles user-related API endpoints
 */
class UserController extends BaseController
{
    /**
     * Get users list
     */
    public function index()
    {
        $this->requireAuth();
        
        try {
            $page = (int)($_GET['page'] ?? 1);
            $limit = min((int)($_GET['limit'] ?? 20), 100); // Max 100 per page
            $offset = ($page - 1) * $limit;
            
            $search = $_GET['search'] ?? '';
            $role = $_GET['role'] ?? '';
            $status = $_GET['status'] ?? 'active';
            
            // Build query
            $whereConditions = ['u.company_id = ?'];
            $params = [$this->user['company_id']];
            
            if (!empty($search)) {
                $whereConditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if (!empty($role)) {
                $whereConditions[] = "u.role = ?";
                $params[] = $role;
            }
            
            if (!empty($status)) {
                $whereConditions[] = "u.status = ?";
                $params[] = $status;
            }
            
            $whereClause = implode(' AND ', $whereConditions);
            
            // Get users
            $users = $this->db->fetchAll(
                "SELECT u.id, u.first_name, u.last_name, u.email, u.phone, u.role, u.status, 
                        u.created_at, u.last_login, c.name as company_name
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 WHERE {$whereClause}
                 ORDER BY u.created_at DESC
                 LIMIT ? OFFSET ?",
                array_merge($params, [$limit, $offset])
            );
            
            // Get total count
            $total = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM users u WHERE {$whereClause}",
                $params
            );
            
            $this->jsonResponse([
                'success' => true,
                'data' => $users,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Failed to fetch users: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Get specific user
     */
    public function show($userId)
    {
        $this->requireAuth();
        
        try {
            $user = $this->db->fetch(
                "SELECT u.id, u.first_name, u.last_name, u.email, u.phone, u.role, u.status,
                        u.created_at, u.updated_at, u.last_login, c.name as company_name
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 WHERE u.id = ? AND u.company_id = ?",
                [$userId, $this->user['company_id']]
            );
            
            if (!$user) {
                $this->jsonResponse(['error' => 'User not found'], 404);
                return;
            }
            
            $this->jsonResponse([
                'success' => true,
                'data' => $user
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Failed to fetch user: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Get current user profile
     */
    public function profile()
    {
        $this->requireAuth();
        
        try {
            $user = $this->db->fetch(
                "SELECT u.id, u.first_name, u.last_name, u.email, u.phone, u.role, u.status,
                        u.avatar, u.created_at, u.updated_at, u.last_login, c.name as company_name
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 WHERE u.id = ?",
                [$this->user['id']]
            );
            
            if (!$user) {
                $this->jsonResponse(['error' => 'User not found'], 404);
                return;
            }
            
            $this->jsonResponse([
                'success' => true,
                'data' => $user
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Failed to fetch profile: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Update current user profile
     */
    public function updateProfile()
    {
        $this->requireAuth();
        
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            $data = [
                'first_name' => $input['first_name'] ?? '',
                'last_name' => $input['last_name'] ?? '',
                'email' => $input['email'] ?? '',
                'phone' => $input['phone'] ?? '',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Validate required fields
            if (empty($data['first_name']) || empty($data['last_name']) || empty($data['email'])) {
                $this->jsonResponse(['error' => 'First name, last name, and email are required'], 400);
                return;
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $this->jsonResponse(['error' => 'Invalid email format'], 400);
                return;
            }
            
            // Check email uniqueness (excluding current user)
            $existingUser = $this->db->fetch(
                "SELECT id FROM users WHERE email = ? AND id != ?",
                [$data['email'], $this->user['id']]
            );
            
            if ($existingUser) {
                $this->jsonResponse(['error' => 'Email address is already in use'], 400);
                return;
            }
            
            // Update user
            $this->db->update('users', $data, ['id' => $this->user['id']]);
            
            // Get updated user data
            $updatedUser = $this->db->fetch(
                "SELECT u.id, u.first_name, u.last_name, u.email, u.phone, u.role, u.status,
                        u.avatar, u.created_at, u.updated_at, c.name as company_name
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 WHERE u.id = ?",
                [$this->user['id']]
            );
            
            $this->jsonResponse([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => $updatedUser
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Failed to update profile: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Change password
     */
    public function changePassword()
    {
        $this->requireAuth();
        
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            $currentPassword = $input['current_password'] ?? '';
            $newPassword = $input['new_password'] ?? '';
            $confirmPassword = $input['confirm_password'] ?? '';
            
            // Validate input
            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                $this->jsonResponse(['error' => 'All password fields are required'], 400);
                return;
            }
            
            // Verify current password
            if (!password_verify($currentPassword, $this->user['password'])) {
                $this->jsonResponse(['error' => 'Current password is incorrect'], 400);
                return;
            }
            
            // Validate new password
            if (strlen($newPassword) < 8) {
                $this->jsonResponse(['error' => 'New password must be at least 8 characters long'], 400);
                return;
            }
            
            if ($newPassword !== $confirmPassword) {
                $this->jsonResponse(['error' => 'New password and confirmation do not match'], 400);
                return;
            }
            
            // Update password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $this->db->update('users', 
                ['password' => $hashedPassword, 'updated_at' => date('Y-m-d H:i:s')], 
                ['id' => $this->user['id']]
            );
            
            $this->jsonResponse([
                'success' => true,
                'message' => 'Password changed successfully'
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Failed to change password: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Get user statistics
     */
    public function stats($userId = null)
    {
        $this->requireAuth();
        
        try {
            $targetUserId = $userId ?? $this->user['id'];
            
            // Verify access to user stats
            if ($targetUserId != $this->user['id']) {
                // Only allow if same company or super admin
                if ($this->user['role'] !== 'super_admin') {
                    $targetUser = $this->db->fetch(
                        "SELECT company_id FROM users WHERE id = ?",
                        [$targetUserId]
                    );
                    
                    if (!$targetUser || $targetUser['company_id'] != $this->user['company_id']) {
                        $this->jsonResponse(['error' => 'Access denied'], 403);
                        return;
                    }
                }
            }
            
            // Get user statistics
            $stats = [
                'documents_uploaded' => $this->db->fetchColumn(
                    "SELECT COUNT(*) FROM documents WHERE created_by = ?",
                    [$targetUserId]
                ),
                'total_storage_used' => $this->db->fetchColumn(
                    "SELECT COALESCE(SUM(file_size), 0) FROM documents WHERE created_by = ?",
                    [$targetUserId]
                ),
                'recent_uploads' => $this->db->fetchColumn(
                    "SELECT COUNT(*) FROM documents WHERE created_by = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)",
                    [$targetUserId]
                ),
                'last_upload' => $this->db->fetchColumn(
                    "SELECT MAX(created_at) FROM documents WHERE created_by = ?",
                    [$targetUserId]
                )
            ];
            
            $this->jsonResponse([
                'success' => true,
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => 'Failed to fetch user stats: ' . $e->getMessage()], 500);
        }
    }
}
