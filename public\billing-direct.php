<?php
/**
 * Direct Billing Access
 * 
 * Temporary direct access to billing module while we debug routing
 */

session_start();

// Security check - only allow from localhost
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';

if (!in_array($clientIP, $allowedIPs)) {
    die('Access denied. This page can only be accessed from localhost for security reasons.');
}

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');
define('BASE_PATH', '/dms/public');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        header('Location: /dms/public/');
        exit;
    }
    
    // Set up database
    require_once CONFIG_PATH . '/database.php';
    $db = \App\Core\Database::getInstance();
    
    // Set up service factory
    \App\Services\ServiceFactory::initialize($db, $_SESSION, $_SESSION);
    
    // Create and call billing controller
    $billingController = new \App\Controllers\BillingController();
    
    // Handle different actions
    $action = $_GET['action'] ?? 'index';
    $id = $_GET['id'] ?? null;
    
    switch ($action) {
        case 'index':
            $billingController->index();
            break;
            
        case 'rates':
            $billingController->serviceRates();
            break;
            
        case 'client':
            if ($id) {
                $billingController->clientBilling($id);
            } else {
                $billingController->index();
            }
            break;
            
        case 'invoice':
            if ($id) {
                $billingController->viewInvoice($id);
            } else {
                $billingController->index();
            }
            break;
            
        case 'generate-invoice':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $billingController->generateInvoice();
            } else {
                $billingController->index();
            }
            break;
            
        case 'mark-paid':
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && $id) {
                $billingController->markPaid($id);
            } else {
                $billingController->index();
            }
            break;
            
        case 'log-event':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $billingController->logEvent();
            } else {
                header('Content-Type: application/json');
                echo json_encode(['error' => 'Method not allowed']);
            }
            break;
            
        default:
            $billingController->index();
            break;
    }
    
} catch (Exception $e) {
    // Log error
    error_log("Billing Direct Access Error: " . $e->getMessage());
    
    // Show error page
    http_response_code(500);
    
    echo "<h1>Billing Module Error</h1>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    if ($config['debug']) {
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    
    echo '<p><a href="/dms/public/">← Back to Main Application</a></p>';
}
?>
