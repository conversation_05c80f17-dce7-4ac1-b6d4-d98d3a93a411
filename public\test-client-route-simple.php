<?php
/**
 * Simple Client Route Test
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

echo "<h1>Simple Client Route Test</h1>";

// Test the exact URL pattern
$testUri = '/app/billing/client/1';
$pattern = '/^\/app\/billing\/client\/([^\/]+)$/';

echo "<p><strong>Testing URI:</strong> {$testUri}</p>";
echo "<p><strong>Pattern:</strong> {$pattern}</p>";

if (preg_match($pattern, $testUri, $matches)) {
    echo "<p style='color: green;'>✅ Pattern matches!</p>";
    echo "<p><strong>Matches:</strong> " . json_encode($matches) . "</p>";
    echo "<p><strong>Client ID:</strong> " . $matches[1] . "</p>";
} else {
    echo "<p style='color: red;'>❌ Pattern does not match!</p>";
}

echo "<hr>";

// Test router pattern conversion
$router = new \App\Core\Router();

// Use reflection to test the pattern conversion method
$reflection = new ReflectionClass($router);
$method = $reflection->getMethod('convertToPattern');
$method->setAccessible(true);

$routePath = '/app/billing/client/{id}';
$generatedPattern = $method->invoke($router, $routePath);

echo "<p><strong>Route Path:</strong> {$routePath}</p>";
echo "<p><strong>Generated Pattern:</strong> {$generatedPattern}</p>";

if (preg_match($generatedPattern, $testUri, $matches)) {
    echo "<p style='color: green;'>✅ Generated pattern matches!</p>";
    echo "<p><strong>Matches:</strong> " . json_encode($matches) . "</p>";
} else {
    echo "<p style='color: red;'>❌ Generated pattern does not match!</p>";
}

echo "<hr>";

// Test if we can access the controller directly
echo "<h3>Direct Controller Test</h3>";

try {
    $controller = new \App\Controllers\BillingController();
    echo "<p>✅ BillingController instantiated successfully</p>";
    
    if (method_exists($controller, 'clientBilling')) {
        echo "<p>✅ clientBilling method exists</p>";
    } else {
        echo "<p>❌ clientBilling method does not exist</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error instantiating controller: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Test Links</h3>";
echo '<p><a href="/dms/app/billing" target="_blank">Main Billing Page</a></p>';
echo '<p><a href="/dms/app/billing/client/1" target="_blank">Client Billing Page</a></p>';
echo '<p><a href="/dms/public/billing-direct.php?action=client&id=1" target="_blank">Direct Access (Working)</a></p>';
?>
