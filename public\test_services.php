<?php
/**
 * Simple Business Services Test Page
 * 
 * Direct access test page for business services
 */

// Include the application bootstrap
require_once __DIR__ . '/../src/autoload.php';
require_once __DIR__ . '/../src/config/database.php';

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: /dms/public/login.php');
    exit;
}

use App\Core\Database;
use App\Services\ServiceFactory;

// Initialize database and services
try {
    $db = Database::getInstance();
    
    // Get current user
    $user = $db->fetch(
        "SELECT u.*, c.name as company_name
         FROM users u
         LEFT JOIN companies c ON u.company_id = c.id
         WHERE u.id = ? AND u.status = 'active'",
        [$_SESSION['user_id']]
    );
    
    if (!$user) {
        header('Location: /dms/public/login.php');
        exit;
    }
    
    // Initialize service factory
    ServiceFactory::initialize($db, $user, null);
    
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Services Test - DMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">🧪 Business Services Test</h1>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
                <h2 class="text-lg font-semibold text-blue-800 mb-2">Welcome, <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>!</h2>
                <p class="text-blue-700">Company: <?= htmlspecialchars($user['company_name']) ?></p>
                <p class="text-blue-700">This page allows you to test the new business services.</p>
            </div>

            <!-- Service Status -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">📊 Service Status</h2>
                <button onclick="checkServiceStatus()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Check Service Status
                </button>
                <div id="serviceStatus" class="mt-4"></div>
            </div>

            <!-- Quick Service Tests -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                
                <!-- Box Service Test -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold mb-4">📦 Box Service</h3>
                    <button onclick="testBoxService()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        Test Box Service
                    </button>
                    <div id="boxResult" class="mt-4 text-sm"></div>
                </div>

                <!-- Bundle Service Test -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold mb-4">📁 Bundle Service</h3>
                    <button onclick="testBundleService()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        Test Bundle Service
                    </button>
                    <div id="bundleResult" class="mt-4 text-sm"></div>
                </div>

                <!-- Search Service Test -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold mb-4">🔍 Search Service</h3>
                    <button onclick="testSearchService()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        Test Search Service
                    </button>
                    <div id="searchResult" class="mt-4 text-sm"></div>
                </div>

                <!-- Intake Service Test -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold mb-4">📥 Intake Service</h3>
                    <button onclick="testIntakeService()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        Test Intake Service
                    </button>
                    <div id="intakeResult" class="mt-4 text-sm"></div>
                </div>

                <!-- Delivery Service Test -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold mb-4">🚚 Delivery Service</h3>
                    <button onclick="testDeliveryService()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        Test Delivery Service
                    </button>
                    <div id="deliveryResult" class="mt-4 text-sm"></div>
                </div>

                <!-- All Services Test -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold mb-4">🎯 All Services</h3>
                    <button onclick="testAllServices()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 w-full">
                        Test All Services
                    </button>
                    <div id="allResult" class="mt-4 text-sm"></div>
                </div>
            </div>

            <!-- API Endpoints Reference -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📋 API Endpoints</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h3 class="font-medium mb-2">Service Management</h3>
                        <ul class="text-sm space-y-1">
                            <li><code class="bg-gray-100 px-2 py-1 rounded">GET /app/services/status</code></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-medium mb-2">Box Services</h3>
                        <ul class="text-sm space-y-1">
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /app/services/boxes/create</code></li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /app/services/boxes/{id}/move</code></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-medium mb-2">Bundle Services</h3>
                        <ul class="text-sm space-y-1">
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /app/services/bundles/create</code></li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /app/services/bundles/{id}/close</code></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-medium mb-2">Search & Other</h3>
                        <ul class="text-sm space-y-1">
                            <li><code class="bg-gray-100 px-2 py-1 rounded">GET /app/services/search</code></li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /app/services/delivery/create</code></li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /app/services/intake/create</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Helper function to make API calls
        async function apiCall(url, method = 'GET', data = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Display result in a container
        function displayResult(containerId, result, title = 'Result') {
            const container = document.getElementById(containerId);
            const success = result.success !== false;
            const bgColor = success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
            const textColor = success ? 'text-green-800' : 'text-red-800';
            
            container.innerHTML = `
                <div class="border rounded p-3 ${bgColor}">
                    <h4 class="font-medium ${textColor} mb-2">${title}</h4>
                    <pre class="text-xs ${textColor} whitespace-pre-wrap">${JSON.stringify(result, null, 2)}</pre>
                </div>
            `;
        }

        // Service Status Check
        async function checkServiceStatus() {
            const result = await apiCall('/dms/public/test_api.php?action=status');
            displayResult('serviceStatus', result, 'Service Status');
        }

        // Test Box Service
        async function testBoxService() {
            const result = await apiCall('/dms/public/test_api.php?action=create_box');
            displayResult('boxResult', result, 'Box Service Test');
        }

        // Test Bundle Service
        async function testBundleService() {
            const result = await apiCall('/dms/public/test_api.php?action=create_bundle');
            displayResult('bundleResult', result, 'Bundle Service Test');
        }

        // Test Search Service
        async function testSearchService() {
            const result = await apiCall('/dms/public/test_api.php?action=search');
            displayResult('searchResult', result, 'Search Service Test');
        }

        // Test Intake Service
        async function testIntakeService() {
            const result = await apiCall('/dms/public/test_api.php?action=create_intake');
            displayResult('intakeResult', result, 'Intake Service Test');
        }

        // Test Delivery Service
        async function testDeliveryService() {
            const result = await apiCall('/dms/public/test_api.php?action=create_delivery');
            displayResult('deliveryResult', result, 'Delivery Service Test');
        }

        // Test All Services
        async function testAllServices() {
            document.getElementById('allResult').innerHTML = '<div class="text-blue-600">Testing all services...</div>';
            
            const tests = [
                { name: 'Service Status', func: checkServiceStatus },
                { name: 'Box Service', func: testBoxService },
                { name: 'Bundle Service', func: testBundleService },
                { name: 'Search Service', func: testSearchService },
                { name: 'Intake Service', func: testIntakeService }
            ];
            
            let results = [];
            for (const test of tests) {
                try {
                    await test.func();
                    results.push(`✅ ${test.name}: Passed`);
                } catch (error) {
                    results.push(`❌ ${test.name}: Failed - ${error.message}`);
                }
            }
            
            document.getElementById('allResult').innerHTML = `
                <div class="bg-blue-50 border border-blue-200 rounded p-3">
                    <h4 class="font-medium text-blue-800 mb-2">All Services Test Results</h4>
                    <div class="text-sm text-blue-700">
                        ${results.map(result => `<div>${result}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        // Load service status on page load
        window.onload = function() {
            checkServiceStatus();
        };
    </script>
</body>
</html>
