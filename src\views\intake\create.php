<?php
$title = 'New Intake Entry';
ob_start();
?>

<!-- Create Intake Entry Form -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        New Intake Entry
                    </h1>
                    <p class="text-gray-600 mt-2">Register incoming documents for processing and organization</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/intake') ?>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Intake
                    </a>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
            <form action="<?= url('/app/intake') ?>" method="POST" class="space-y-8">

                <!-- Client Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Client Information
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- Client Name -->
                        <div>
                            <label for="client_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Client Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="client_name"
                                   name="client_name"
                                   required
                                   value="<?= e($_POST['client_name'] ?? '') ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="Enter client name...">
                        </div>

                        <!-- Client ID -->
                        <div>
                            <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Client ID
                            </label>
                            <input type="text"
                                   id="client_id"
                                   name="client_id"
                                   value="<?= e($_POST['client_id'] ?? '') ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="Optional client identifier...">
                        </div>

                    </div>
                </div>

                <!-- Document Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Document Information
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                    <!-- Source -->
                    <div>
                        <label for="source" class="block text-sm font-medium text-gray-700 mb-2">
                            Document Source <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="source"
                                   name="source"
                                   required
                                   autocomplete="off"
                                   placeholder="Type or select document source..."
                                   class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white shadow-sm hover:border-gray-400"
                                   onclick="toggleSourceDropdown()"
                                   oninput="filterSourceOptions(this.value)">
                            <button type="button" onclick="toggleSourceDropdown()" class="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none">
                                <svg class="w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <!-- Custom Dropdown -->
                            <div id="source-dropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-y-auto">
                                <div class="p-2">
                                    <?php foreach ($sources as $value => $label): ?>
                                        <div class="source-option px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 cursor-pointer rounded-lg transition-colors duration-150"
                                             onclick="selectSource('<?= e($label) ?>')">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <?= e($label) ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="border-t border-gray-100 mt-2 pt-2">
                                        <div class="px-3 py-2 text-xs text-gray-500 italic">
                                            💡 Type to create a custom source
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">You can type a custom source or select from suggestions</p>
                    </div>

                    <!-- Document Type -->
                    <div>
                        <label for="document_type" class="block text-sm font-medium text-gray-700 mb-2">
                            Document Type <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="document_type"
                                   name="document_type"
                                   required
                                   autocomplete="off"
                                   placeholder="Type or select document type..."
                                   class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white shadow-sm hover:border-gray-400"
                                   onclick="toggleTypeDropdown()"
                                   oninput="filterTypeOptions(this.value)">
                            <button type="button" onclick="toggleTypeDropdown()" class="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none">
                                <svg class="w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <!-- Custom Dropdown -->
                            <div id="type-dropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-y-auto">
                                <div class="p-2">
                                    <?php foreach ($documentTypes as $value => $label): ?>
                                        <div class="type-option px-3 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 cursor-pointer rounded-lg transition-colors duration-150"
                                             onclick="selectType('<?= e($label) ?>')">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                                </svg>
                                                <?= e($label) ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="border-t border-gray-100 mt-2 pt-2">
                                        <div class="px-3 py-2 text-xs text-gray-500 italic">
                                            💡 Type to create a custom type
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">You can type a custom type or select from suggestions</p>
                    </div>

                    </div>
                </div>

                <!-- Processing Details -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        Processing Details
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- Priority -->
                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                                Priority <span class="text-red-500">*</span>
                            </label>
                            <select id="priority"
                                    name="priority"
                                    required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                <option value="">Select priority level</option>
                                <option value="low" <?= ($_POST['priority'] ?? '') === 'low' ? 'selected' : '' ?>>Low</option>
                                <option value="medium" <?= ($_POST['priority'] ?? '') === 'medium' ? 'selected' : '' ?>>Medium</option>
                                <option value="high" <?= ($_POST['priority'] ?? '') === 'high' ? 'selected' : '' ?>>High</option>
                                <option value="urgent" <?= ($_POST['priority'] ?? '') === 'urgent' ? 'selected' : '' ?>>Urgent</option>
                            </select>
                        </div>

                        <!-- Expected Count -->
                        <div>
                            <label for="expected_count" class="block text-sm font-medium text-gray-700 mb-2">
                                Expected Document Count <span class="text-red-500">*</span>
                            </label>
                            <input type="number"
                                   id="expected_count"
                                   name="expected_count"
                                   min="1"
                                   required
                                   value="<?= e($_POST['expected_count'] ?? '1') ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="1">
                            <p class="text-xs text-gray-600 mt-1">How many documents do you expect to receive?</p>
                        </div>

                        <!-- Actual Count -->
                        <div>
                            <label for="actual_count" class="block text-sm font-medium text-gray-700 mb-2">
                                Current Document Count
                            </label>
                            <input type="number"
                                   id="actual_count"
                                   name="actual_count"
                                   min="0"
                                   value="<?= e($_POST['actual_count'] ?? '0') ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="0">
                            <p class="text-xs text-gray-600 mt-1">How many documents do you currently have? (Leave 0 if none yet)</p>
                        </div>

                        <!-- Sensitivity Level -->
                        <div>
                            <label for="sensitivity_level" class="block text-sm font-medium text-gray-700 mb-2">
                                Sensitivity Level
                            </label>
                            <select id="sensitivity_level"
                                    name="sensitivity_level"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                <option value="public" <?= ($_POST['sensitivity_level'] ?? '') === 'public' ? 'selected' : '' ?>>Public</option>
                                <option value="internal" <?= ($_POST['sensitivity_level'] ?? 'internal') === 'internal' ? 'selected' : '' ?>>Internal</option>
                                <option value="confidential" <?= ($_POST['sensitivity_level'] ?? '') === 'confidential' ? 'selected' : '' ?>>Confidential</option>
                                <option value="restricted" <?= ($_POST['sensitivity_level'] ?? '') === 'restricted' ? 'selected' : '' ?>>Restricted</option>
                            </select>
                        </div>

                        <!-- Department -->
                        <div>
                            <label for="department" class="block text-sm font-medium text-gray-700 mb-2">
                                Department
                            </label>
                            <input type="text"
                                   id="department"
                                   name="department"
                                   value="<?= e($_POST['department'] ?? '') ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   placeholder="Enter department name...">
                        </div>

                    </div>
                </div>



                <!-- Description -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                        </svg>
                        Description
                    </h3>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description <span class="text-red-500">*</span>
                        </label>
                        <textarea id="description"
                                  name="description"
                                  rows="4"
                                  required
                                  value="<?= e($_POST['description'] ?? '') ?>"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                  placeholder="Describe the documents being received..."><?= e($_POST['description'] ?? '') ?></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="<?= url('/app/intake') ?>" 
                       class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-300 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Intake Entry
                    </button>
                </div>

            </form>
        </div>

        <!-- Help Information -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">Intake Process Guide</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800">
                <div>
                    <h4 class="font-medium mb-2">Custom Fields:</h4>
                    <ul class="space-y-1">
                        <li>• <strong>Document Source:</strong> Type your own or select from suggestions</li>
                        <li>• <strong>Document Type:</strong> Create custom types as needed</li>
                        <li>• <strong>Client ID:</strong> Auto-generated from client name</li>
                        <li>• <strong>Smart Suggestions:</strong> System suggests based on your input</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Common Sources:</h4>
                    <ul class="space-y-1">
                        <li>• Email, Physical Mail, Scan</li>
                        <li>• Direct Upload, Fax</li>
                        <li>• Courier, Walk-in Delivery</li>
                        <li>• Or create your own custom source</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Priority Levels:</h4>
                    <ul class="space-y-1">
                        <li>• <strong>Urgent:</strong> Immediate processing</li>
                        <li>• <strong>High:</strong> Within 24 hours</li>
                        <li>• <strong>Medium:</strong> Within 3 days</li>
                        <li>• <strong>Low:</strong> Within 1 week</li>
                    </ul>
                </div>
            </div>
            <div class="mt-4 p-4 bg-blue-100 rounded-lg">
                <p class="text-sm text-blue-800">
                    <strong>💡 Pro Tip:</strong> You can create your own custom Document Sources and Document Types by simply typing them in the fields. The system will remember your custom entries for future use.
                </p>
            </div>
            <div class="mt-3 p-4 bg-green-100 rounded-lg">
                <p class="text-sm text-green-800">
                    <strong>Next Steps:</strong> After creating an intake entry, documents can be uploaded and assigned to bundles for organized storage in boxes.
                </p>
            </div>
        </div>

    </div>
</div>

<script>
// Auto-suggest document type based on source selection
document.getElementById('source').addEventListener('input', function() {
    const source = this.value.toLowerCase();
    const documentTypeInput = document.getElementById('document_type');

    // Suggest document types based on source
    if (source.includes('email')) {
        documentTypeInput.value = 'Correspondence';
    } else if (source.includes('scan')) {
        documentTypeInput.value = 'Contract';
    } else if (source.includes('fax')) {
        documentTypeInput.value = 'Legal Document';
    } else if (source.includes('upload')) {
        documentTypeInput.value = 'Report';
    } else if (source.includes('physical') || source.includes('mail')) {
        documentTypeInput.value = 'Invoice';
    }
});

// Auto-adjust priority based on document type
document.getElementById('document_type').addEventListener('input', function() {
    const docType = this.value.toLowerCase();
    const prioritySelect = document.getElementById('priority');

    // Suggest priority based on document type
    if (docType.includes('legal') || docType.includes('contract')) {
        prioritySelect.value = 'high';
    } else if (docType.includes('invoice') || docType.includes('financial')) {
        prioritySelect.value = 'medium';
    } else if (docType.includes('correspondence') || docType.includes('report')) {
        prioritySelect.value = 'low';
    }
});



// Auto-generate client ID from client name
document.getElementById('client_name').addEventListener('input', function() {
    const clientName = this.value;
    const clientIdInput = document.getElementById('client_id');

    if (clientName && !clientIdInput.value) {
        // Generate a simple client ID from the name
        const clientId = clientName.replace(/[^A-Za-z0-9]/g, '').toUpperCase().substring(0, 8);
        if (clientId.length >= 3) {
            clientIdInput.value = clientId + '-' + new Date().getFullYear().toString().substr(-2);
        }
    }
});

// Enhanced form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const clientName = document.getElementById('client_name').value.trim();
    const source = document.getElementById('source').value.trim();
    const documentType = document.getElementById('document_type').value.trim();
    const description = document.getElementById('description').value.trim();
    const priority = document.getElementById('priority').value;

    if (!clientName || !source || !documentType || !description || !priority) {
        e.preventDefault();
        alert('Please fill in all required fields (marked with *)');
        return false;
    }

    // Show success message
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<svg class="w-5 h-5 mr-2 inline animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Creating...';
    submitBtn.disabled = true;
});

// Enhanced Dropdown Functions
function toggleSourceDropdown() {
    const dropdown = document.getElementById('source-dropdown');
    const typeDropdown = document.getElementById('type-dropdown');

    // Close type dropdown if open
    typeDropdown.classList.add('hidden');

    // Toggle source dropdown
    dropdown.classList.toggle('hidden');
}

function toggleTypeDropdown() {
    const dropdown = document.getElementById('type-dropdown');
    const sourceDropdown = document.getElementById('source-dropdown');

    // Close source dropdown if open
    sourceDropdown.classList.add('hidden');

    // Toggle type dropdown
    dropdown.classList.toggle('hidden');
}

function selectSource(value) {
    document.getElementById('source').value = value;
    document.getElementById('source-dropdown').classList.add('hidden');

    // Trigger auto-suggestion for document type
    document.getElementById('source').dispatchEvent(new Event('input'));
}

function selectType(value) {
    document.getElementById('document_type').value = value;
    document.getElementById('type-dropdown').classList.add('hidden');

    // Trigger auto-suggestion for priority
    document.getElementById('document_type').dispatchEvent(new Event('input'));
}

function filterSourceOptions(searchTerm) {
    const options = document.querySelectorAll('.source-option');
    const dropdown = document.getElementById('source-dropdown');

    if (searchTerm.length > 0) {
        dropdown.classList.remove('hidden');

        options.forEach(option => {
            const text = option.textContent.toLowerCase();
            if (text.includes(searchTerm.toLowerCase())) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        });
    } else {
        options.forEach(option => {
            option.style.display = 'block';
        });
    }
}

function filterTypeOptions(searchTerm) {
    const options = document.querySelectorAll('.type-option');
    const dropdown = document.getElementById('type-dropdown');

    if (searchTerm.length > 0) {
        dropdown.classList.remove('hidden');

        options.forEach(option => {
            const text = option.textContent.toLowerCase();
            if (text.includes(searchTerm.toLowerCase())) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        });
    } else {
        options.forEach(option => {
            option.style.display = 'block';
        });
    }
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    const sourceInput = document.getElementById('source');
    const typeInput = document.getElementById('document_type');
    const sourceDropdown = document.getElementById('source-dropdown');
    const typeDropdown = document.getElementById('type-dropdown');

    // Close source dropdown if clicking outside
    if (!sourceInput.contains(event.target) && !sourceDropdown.contains(event.target)) {
        sourceDropdown.classList.add('hidden');
    }

    // Close type dropdown if clicking outside
    if (!typeInput.contains(event.target) && !typeDropdown.contains(event.target)) {
        typeDropdown.classList.add('hidden');
    }
});

// Handle keyboard navigation
document.getElementById('source').addEventListener('keydown', function(e) {
    if (e.key === 'ArrowDown') {
        e.preventDefault();
        document.getElementById('source-dropdown').classList.remove('hidden');
        const firstOption = document.querySelector('.source-option:not([style*="display: none"])');
        if (firstOption) firstOption.focus();
    }
});

document.getElementById('document_type').addEventListener('keydown', function(e) {
    if (e.key === 'ArrowDown') {
        e.preventDefault();
        document.getElementById('type-dropdown').classList.remove('hidden');
        const firstOption = document.querySelector('.type-option:not([style*="display: none"])');
        if (firstOption) firstOption.focus();
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
