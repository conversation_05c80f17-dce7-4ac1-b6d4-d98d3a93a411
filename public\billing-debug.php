<?php
/**
 * Billing Module Debug Page
 * 
 * This page helps debug billing access issues and provides correct URLs
 */

session_start();

// Security check - only allow from localhost
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';

if (!in_array($clientIP, $allowedIPs)) {
    die('Access denied. This debug page can only be accessed from localhost for security reasons.');
}

require_once __DIR__ . '/../src/autoload.php';

// Get current URL info
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
$requestUri = $_SERVER['REQUEST_URI'] ?? '';

// Detect base path
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

$baseUrl = $protocol . '://' . $host . $basePath;

// Test database connection
$dbStatus = 'Unknown';
$billingTablesExist = false;
$userLoggedIn = false;

try {
    require_once __DIR__ . '/../src/config/database.php';

    $db = \App\Core\Database::getInstance();
    $dbStatus = 'Connected';
    
    // Check billing tables
    $tables = ['billing_rates', 'billing_events', 'billing_invoices', 'billing_payments'];
    $existingTables = [];
    
    foreach ($tables as $table) {
        $exists = $db->fetch("SHOW TABLES LIKE '{$table}'");
        if ($exists) {
            $existingTables[] = $table;
        }
    }
    
    $billingTablesExist = count($existingTables) === count($tables);
    
    // Check if user is logged in
    $userLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    
} catch (Exception $e) {
    $dbStatus = 'Error: ' . $e->getMessage();
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Billing Module Debug - DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-bug"></i> Billing Module Debug Information
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- System Status -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>System Status</h5>
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Database Connection
                                        <span class="badge bg-<?= $dbStatus === 'Connected' ? 'success' : 'danger' ?>">
                                            <?= htmlspecialchars($dbStatus) ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Billing Tables
                                        <span class="badge bg-<?= $billingTablesExist ? 'success' : 'warning' ?>">
                                            <?= $billingTablesExist ? 'All Present' : 'Missing/Incomplete' ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        User Session
                                        <span class="badge bg-<?= $userLoggedIn ? 'success' : 'warning' ?>">
                                            <?= $userLoggedIn ? 'Logged In' : 'Not Logged In' ?>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>URL Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Host:</strong></td>
                                        <td><?= htmlspecialchars($host) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Base Path:</strong></td>
                                        <td><?= htmlspecialchars($basePath) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Base URL:</strong></td>
                                        <td><?= htmlspecialchars($baseUrl) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Current URI:</strong></td>
                                        <td><?= htmlspecialchars($requestUri) ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Billing URLs to Try -->
                        <div class="mb-4">
                            <h5>🎯 Try These Billing URLs</h5>
                            <div class="alert alert-info">
                                <strong>Click the links below to test different URL formats:</strong>
                            </div>
                            
                            <div class="list-group">
                                <?php
                                $urls = [
                                    'Main Application' => $baseUrl . '/app/billing',
                                    'With Index.php' => $baseUrl . '/index.php/app/billing',
                                    'Direct Public' => $protocol . '://' . $host . '/dms/public/app/billing',
                                    'Direct Public with Index' => $protocol . '://' . $host . '/dms/public/index.php/app/billing',
                                    'Alternative Path' => $protocol . '://' . $host . '/dms/app/billing'
                                ];
                                
                                foreach ($urls as $label => $url):
                                ?>
                                    <a href="<?= htmlspecialchars($url) ?>" class="list-group-item list-group-item-action" target="_blank">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?= htmlspecialchars($label) ?></h6>
                                            <small><i class="fas fa-external-link-alt"></i></small>
                                        </div>
                                        <p class="mb-1"><code><?= htmlspecialchars($url) ?></code></p>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mb-4">
                            <h5>🚀 Quick Actions</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <i class="fas fa-sign-in-alt fa-2x text-primary mb-2"></i>
                                            <h6>Login First</h6>
                                            <p class="small text-muted">Make sure you're logged in</p>
                                            <a href="<?= $baseUrl ?>" class="btn btn-primary btn-sm">Go to Login</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <i class="fas fa-tachometer-alt fa-2x text-success mb-2"></i>
                                            <h6>Dashboard</h6>
                                            <p class="small text-muted">Access main dashboard</p>
                                            <a href="<?= $baseUrl ?>/app/dashboard" class="btn btn-success btn-sm">Dashboard</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <i class="fas fa-database fa-2x text-info mb-2"></i>
                                            <h6>Test Database</h6>
                                            <p class="small text-muted">Check database connection</p>
                                            <a href="test-db-connection.php" class="btn btn-info btn-sm">Test DB</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Troubleshooting -->
                        <div class="mb-4">
                            <h5>🔧 Troubleshooting Steps</h5>
                            <div class="accordion" id="troubleshootingAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingOne">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                            1. Check if you're logged in
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <p>The billing module requires authentication. Make sure you:</p>
                                            <ul>
                                                <li>Are logged into the DMS application</li>
                                                <li>Have appropriate permissions (admin/manager role)</li>
                                                <li>Session hasn't expired</li>
                                            </ul>
                                            <p><strong>Status:</strong> 
                                                <span class="badge bg-<?= $userLoggedIn ? 'success' : 'warning' ?>">
                                                    <?= $userLoggedIn ? 'Logged In' : 'Not Logged In' ?>
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingTwo">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                            2. Verify billing tables exist
                                        </button>
                                    </h2>
                                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <p>The billing module requires specific database tables:</p>
                                            <ul>
                                                <li>billing_rates</li>
                                                <li>billing_events</li>
                                                <li>billing_invoices</li>
                                                <li>billing_payments</li>
                                            </ul>
                                            <p><strong>Status:</strong> 
                                                <span class="badge bg-<?= $billingTablesExist ? 'success' : 'warning' ?>">
                                                    <?= $billingTablesExist ? 'All Tables Present' : 'Tables Missing' ?>
                                                </span>
                                            </p>
                                            <?php if (!$billingTablesExist): ?>
                                                <p><a href="setup-billing.php" class="btn btn-warning btn-sm">Run Billing Setup</a></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingThree">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                            3. Check Apache mod_rewrite
                                        </button>
                                    </h2>
                                    <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <p>Clean URLs require Apache mod_rewrite to be enabled. If clean URLs don't work:</p>
                                            <ul>
                                                <li>Use URLs with <code>index.php</code> in them</li>
                                                <li>Check if XAMPP has mod_rewrite enabled</li>
                                                <li>Verify .htaccess file is working</li>
                                            </ul>
                                            <p><strong>Try:</strong> <code><?= $baseUrl ?>/index.php/app/billing</code></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Current Session Info -->
                        <?php if ($userLoggedIn): ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-user-check"></i> Session Information</h6>
                                <p class="mb-0">
                                    <strong>User ID:</strong> <?= htmlspecialchars($_SESSION['user_id'] ?? 'Unknown') ?><br>
                                    <strong>Role:</strong> <?= htmlspecialchars($_SESSION['user_role'] ?? 'Unknown') ?><br>
                                    <strong>Company:</strong> <?= htmlspecialchars($_SESSION['company_name'] ?? 'Unknown') ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Not Logged In</h6>
                                <p class="mb-0">You need to log in to access the billing module. <a href="<?= $baseUrl ?>">Click here to login</a>.</p>
                            </div>
                        <?php endif; ?>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt"></i> 
                                This debug page is only accessible from localhost. Delete this file after troubleshooting.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
