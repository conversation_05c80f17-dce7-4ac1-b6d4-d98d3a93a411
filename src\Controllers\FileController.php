<?php

namespace App\Controllers;

/**
 * File Controller
 * 
 * Handles file serving, previews, and downloads
 */
class FileController extends BaseController
{
    /**
     * Serve files (previews, thumbnails, etc.)
     */
    public function serve($type, $file)
    {
        try {
            // Validate file type
            $allowedTypes = ['documents', 'thumbnails', 'previews', 'avatars'];
            if (!in_array($type, $allowedTypes)) {
                http_response_code(404);
                exit('File type not allowed');
            }
            
            // Construct file path
            $filePath = APP_ROOT . '/public/uploads/' . $type . '/' . $file;
            
            // Security check - ensure file is within uploads directory
            $realPath = realpath($filePath);
            $uploadsPath = realpath(APP_ROOT . '/public/uploads/' . $type);
            
            if (!$realPath || !$uploadsPath || strpos($realPath, $uploadsPath) !== 0) {
                http_response_code(404);
                exit('File not found');
            }
            
            // Check if file exists
            if (!file_exists($filePath)) {
                http_response_code(404);
                exit('File not found');
            }
            
            // For documents, check access permissions
            if ($type === 'documents') {
                $this->checkDocumentAccess($file);
            }
            
            // Get file info
            $mimeType = mime_content_type($filePath);
            $fileSize = filesize($filePath);
            $fileName = basename($filePath);
            
            // Set headers
            header('Content-Type: ' . $mimeType);
            header('Content-Length: ' . $fileSize);
            header('Cache-Control: public, max-age=3600'); // Cache for 1 hour
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($filePath)) . ' GMT');
            
            // For images, allow inline display
            if (strpos($mimeType, 'image/') === 0) {
                header('Content-Disposition: inline; filename="' . $fileName . '"');
            } else {
                // For other files, suggest download
                header('Content-Disposition: attachment; filename="' . $fileName . '"');
            }
            
            // Output file
            readfile($filePath);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(500);
            exit('Error serving file: ' . $e->getMessage());
        }
    }
    
    /**
     * Check if user has access to document
     */
    private function checkDocumentAccess($fileName)
    {
        // If user is not authenticated, deny access
        if (!$this->user) {
            http_response_code(403);
            exit('Access denied');
        }
        
        // Find document by filename
        $document = $this->db->fetch(
            "SELECT id, company_id, status FROM documents WHERE file_name = ?",
            [$fileName]
        );
        
        if (!$document) {
            http_response_code(404);
            exit('Document not found');
        }
        
        // Check if document is active
        if ($document['status'] !== 'active') {
            http_response_code(404);
            exit('Document not available');
        }
        
        // Super admin can access any document
        if ($this->user['role'] === 'super_admin') {
            return;
        }
        
        // Check if user belongs to the same company as the document
        if ($this->user['company_id'] != $document['company_id']) {
            http_response_code(403);
            exit('Access denied');
        }
    }
    
    /**
     * Generate thumbnail for image files
     */
    public function generateThumbnail($documentId)
    {
        $this->requireAuth();
        
        try {
            // Get document
            $document = $this->db->fetch(
                "SELECT * FROM documents WHERE id = ? AND company_id = ?",
                [$documentId, $this->user['company_id']]
            );
            
            if (!$document) {
                throw new \Exception('Document not found.');
            }
            
            // Check if file is an image
            if (strpos($document['file_type'], 'image/') !== 0) {
                throw new \Exception('Thumbnails can only be generated for image files.');
            }
            
            $originalPath = APP_ROOT . '/public' . $document['file_path'];
            if (!file_exists($originalPath)) {
                throw new \Exception('Original file not found.');
            }
            
            // Create thumbnails directory
            $thumbnailDir = APP_ROOT . '/public/uploads/thumbnails';
            if (!is_dir($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }
            
            $thumbnailPath = $thumbnailDir . '/' . $document['file_name'];
            
            // Generate thumbnail
            $this->createThumbnail($originalPath, $thumbnailPath, 200, 200);
            
            $this->jsonResponse([
                'success' => true,
                'thumbnail_url' => '/uploads/thumbnails/' . $document['file_name']
            ]);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Create thumbnail image
     */
    private function createThumbnail($sourcePath, $destPath, $maxWidth, $maxHeight)
    {
        // Get image info
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            throw new \Exception('Invalid image file.');
        }
        
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $sourceType = $imageInfo[2];
        
        // Calculate thumbnail dimensions
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        $thumbWidth = (int)($sourceWidth * $ratio);
        $thumbHeight = (int)($sourceHeight * $ratio);
        
        // Create source image
        switch ($sourceType) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            default:
                throw new \Exception('Unsupported image type for thumbnail generation.');
        }
        
        if (!$sourceImage) {
            throw new \Exception('Failed to create source image.');
        }
        
        // Create thumbnail image
        $thumbImage = imagecreatetruecolor($thumbWidth, $thumbHeight);
        
        // Preserve transparency for PNG and GIF
        if ($sourceType == IMAGETYPE_PNG || $sourceType == IMAGETYPE_GIF) {
            imagealphablending($thumbImage, false);
            imagesavealpha($thumbImage, true);
            $transparent = imagecolorallocatealpha($thumbImage, 255, 255, 255, 127);
            imagefilledrectangle($thumbImage, 0, 0, $thumbWidth, $thumbHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled(
            $thumbImage, $sourceImage,
            0, 0, 0, 0,
            $thumbWidth, $thumbHeight,
            $sourceWidth, $sourceHeight
        );
        
        // Save thumbnail
        switch ($sourceType) {
            case IMAGETYPE_JPEG:
                imagejpeg($thumbImage, $destPath, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($thumbImage, $destPath);
                break;
            case IMAGETYPE_GIF:
                imagegif($thumbImage, $destPath);
                break;
        }
        
        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($thumbImage);
    }
    
    /**
     * Get file preview information
     */
    public function getPreviewInfo($documentId)
    {
        $this->requireAuth();
        
        try {
            // Get document
            $document = $this->db->fetch(
                "SELECT * FROM documents WHERE id = ? AND company_id = ?",
                [$documentId, $this->user['company_id']]
            );
            
            if (!$document) {
                throw new \Exception('Document not found.');
            }
            
            $previewInfo = [
                'id' => $document['id'],
                'title' => $document['title'],
                'file_name' => $document['file_name'],
                'file_type' => $document['file_type'],
                'file_size' => $document['file_size'],
                'can_preview' => false,
                'preview_url' => null,
                'thumbnail_url' => null
            ];
            
            // Check if file can be previewed
            $previewableTypes = [
                'application/pdf',
                'image/jpeg',
                'image/png',
                'image/gif',
                'text/plain'
            ];
            
            if (in_array($document['file_type'], $previewableTypes)) {
                $previewInfo['can_preview'] = true;
                $previewInfo['preview_url'] = $document['file_path'];
                
                // For images, the preview and thumbnail are the same
                if (strpos($document['file_type'], 'image/') === 0) {
                    $previewInfo['thumbnail_url'] = $document['file_path'];
                }
            }
            
            $this->jsonResponse($previewInfo);
            
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }
}
