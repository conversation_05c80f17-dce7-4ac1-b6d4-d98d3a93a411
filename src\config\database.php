<?php
/**
 * Database Configuration
 * 
 * Database connection settings for the Document Management System
 */

return [
    // Default database connection
    'default' => 'mysql',
    
    // Database connections
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'database' => $_ENV['DB_DATABASE'] ?? 'dms_system',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET sql_mode='STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'"
            ]
        ],
        
        // Test database connection
        'testing' => [
            'driver' => 'mysql',
            'host' => $_ENV['TEST_DB_HOST'] ?? 'localhost',
            'port' => $_ENV['TEST_DB_PORT'] ?? '3306',
            'database' => $_ENV['TEST_DB_DATABASE'] ?? 'dms_test',
            'username' => $_ENV['TEST_DB_USERNAME'] ?? 'root',
            'password' => $_ENV['TEST_DB_PASSWORD'] ?? '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        ]
    ],
    
    // Migration settings
    'migrations' => [
        'table' => 'migrations',
        'path' => (defined('APP_ROOT') ? APP_ROOT : dirname(dirname(__DIR__))) . '/database/migrations'
    ],

    // Backup settings
    'backup' => [
        'path' => (defined('APP_ROOT') ? APP_ROOT : dirname(dirname(__DIR__))) . '/storage/backups',
        'compress' => true,
        'exclude_tables' => ['sessions', 'cache']
    ]
];
