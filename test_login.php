<?php
/**
 * Test Login Functionality
 * 
 * Quick test to verify login system works
 */

// Define constants
define('APP_ROOT', __DIR__);
define('CONFIG_PATH', __DIR__ . '/src/config');
define('BASE_PATH', '');

require_once 'src/autoload.php';

// Start session
session_start();

echo "<h1>Login System Test</h1>";

try {
    $db = App\Core\Database::getInstance();
    
    // Check if demo user exists
    $user = $db->fetch(
        "SELECT * FROM users WHERE email = '<EMAIL>' LIMIT 1"
    );
    
    if ($user) {
        echo "<p>✓ Demo user found: {$user['email']}</p>";
        echo "<p>✓ User role: {$user['role']}</p>";
        echo "<p>✓ User status: {$user['status']}</p>";
        
        // Test password verification
        if (password_verify('admin123', $user['password_hash'])) {
            echo "<p>✓ Password verification successful</p>";
        } else {
            echo "<p>✗ Password verification failed</p>";
        }
        
        // Check company
        $company = $db->fetch(
            "SELECT * FROM companies WHERE id = ?",
            [$user['company_id']]
        );
        
        if ($company) {
            echo "<p>✓ Company found: {$company['name']}</p>";
            echo "<p>✓ Company status: {$company['status']}</p>";
        } else {
            echo "<p>✗ Company not found</p>";
        }
        
    } else {
        echo "<p>✗ Demo user not found. Run: php create_demo_user.php</p>";
    }
    
    echo "<hr>";
    echo "<h2>Test Login Form</h2>";
    echo "<p><a href='" . url('/login') . "' target='_blank'>Open Login Page</a></p>";
    echo "<p>Use credentials: <EMAIL> / admin123</p>";
    
} catch (Exception $e) {
    echo "<p>✗ Database error: " . $e->getMessage() . "</p>";
    echo "<p>Make sure MySQL is running and migrations are complete.</p>";
}
?>
