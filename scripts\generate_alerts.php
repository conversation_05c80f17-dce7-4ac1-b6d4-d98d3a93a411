<?php
/**
 * Automated Alert Generation Script
 * 
 * This script should be run daily via cron job to generate automated alerts
 * for retention periods, storage limits, and other system notifications.
 * 
 * Usage: php scripts/generate_alerts.php
 * Cron: 0 9 * * * /usr/bin/php /path/to/dms/scripts/generate_alerts.php
 */

require_once __DIR__ . '/../src/autoload.php';

use App\Core\Database;
use App\Controllers\AlertController;

echo "=== DMS Automated Alert Generation ===\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Initialize database connection
    $db = Database::getInstance();
    
    // Create AlertController instance
    $alertController = new AlertController();
    
    $totalAlertsGenerated = 0;
    
    // Generate retention alerts
    echo "1. Generating retention alerts...\n";
    $retentionAlerts = $alertController->generateRetentionAlerts();
    $totalAlertsGenerated += $retentionAlerts;
    
    // Generate storage limit alerts
    echo "\n2. Generating storage limit alerts...\n";
    $storageAlerts = $alertController->generateStorageAlerts();
    $totalAlertsGenerated += $storageAlerts;
    
    // Generate request pending alerts
    echo "\n3. Generating request pending alerts...\n";
    $requestAlerts = generateRequestPendingAlerts($db);
    $totalAlertsGenerated += $requestAlerts;
    
    // Generate document expiry alerts
    echo "\n4. Generating document expiry alerts...\n";
    $expiryAlerts = generateDocumentExpiryAlerts($db);
    $totalAlertsGenerated += $expiryAlerts;
    
    // Send email notifications for critical alerts
    echo "\n5. Sending email notifications...\n";
    $emailsSent = sendCriticalAlertEmails($db);
    
    echo "\n=== Summary ===\n";
    echo "Total alerts generated: {$totalAlertsGenerated}\n";
    echo "Email notifications sent: {$emailsSent}\n";
    echo "Completed at: " . date('Y-m-d H:i:s') . "\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

/**
 * Generate alerts for requests pending too long
 */
function generateRequestPendingAlerts($db)
{
    $alertsGenerated = 0;
    
    try {
        // Find requests pending for more than 3 days
        $pendingRequests = $db->fetchAll(
            "SELECT dr.*, c.name as company_name, client.email as client_email
             FROM document_requests dr
             JOIN companies c ON dr.service_provider_company_id = c.id
             JOIN users client ON dr.client_user_id = client.id
             WHERE dr.status = 'pending'
             AND dr.requested_date <= DATE_SUB(NOW(), INTERVAL 3 DAY)
             AND NOT EXISTS (
                 SELECT 1 FROM automated_alerts 
                 WHERE target_type = 'request' 
                 AND target_id = dr.id 
                 AND alert_type = 'request_pending'
                 AND status IN ('pending', 'sent')
                 AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             )",
            []
        );

        foreach ($pendingRequests as $request) {
            $daysPending = ceil((time() - strtotime($request['requested_date'])) / (60 * 60 * 24));
            
            $db->execute(
                "INSERT INTO automated_alerts (
                    alert_type, company_id, target_type, target_id, target_reference,
                    title, message, severity, alert_date, recipient_email, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    'request_pending',
                    $request['service_provider_company_id'],
                    'request',
                    $request['id'],
                    $request['request_number'],
                    'Request Pending Review',
                    "Request {$request['request_number']} has been pending for {$daysPending} days",
                    $daysPending >= 7 ? 'critical' : 'warning',
                    date('Y-m-d'),
                    $request['client_email'],
                    'pending'
                ]
            );

            $alertsGenerated++;
        }

        echo "Generated {$alertsGenerated} request pending alerts\n";
        return $alertsGenerated;

    } catch (Exception $e) {
        echo "Error generating request pending alerts: " . $e->getMessage() . "\n";
        return 0;
    }
}

/**
 * Generate alerts for documents approaching expiry
 */
function generateDocumentExpiryAlerts($db)
{
    $alertsGenerated = 0;
    
    try {
        // Find documents with expiry dates in the next 30 days
        $expiringDocuments = $db->fetchAll(
            "SELECT d.*, b.company_id, b.reference_number as bundle_reference, c.name as company_name
             FROM documents d
             JOIN bundles b ON d.bundle_id = b.id
             JOIN companies c ON b.company_id = c.id
             WHERE d.expiry_date IS NOT NULL
             AND d.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
             AND d.expiry_date > CURDATE()
             AND d.status != 'deleted'
             AND NOT EXISTS (
                 SELECT 1 FROM automated_alerts 
                 WHERE target_type = 'document' 
                 AND target_id = d.id 
                 AND alert_type = 'document_expiry'
                 AND status IN ('pending', 'sent')
             )",
            []
        );

        foreach ($expiringDocuments as $document) {
            $daysUntilExpiry = ceil((strtotime($document['expiry_date']) - time()) / (60 * 60 * 24));
            
            $db->execute(
                "INSERT INTO automated_alerts (
                    alert_type, company_id, target_type, target_id, target_reference,
                    title, message, severity, alert_date, due_date, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    'document_expiry',
                    $document['company_id'],
                    'document',
                    $document['id'],
                    $document['reference_number'],
                    'Document Expiry Warning',
                    "Document {$document['file_name']} expires in {$daysUntilExpiry} days",
                    $daysUntilExpiry <= 7 ? 'critical' : 'warning',
                    date('Y-m-d'),
                    $document['expiry_date'],
                    'pending'
                ]
            );

            $alertsGenerated++;
        }

        echo "Generated {$alertsGenerated} document expiry alerts\n";
        return $alertsGenerated;

    } catch (Exception $e) {
        echo "Error generating document expiry alerts: " . $e->getMessage() . "\n";
        return 0;
    }
}

/**
 * Send email notifications for critical alerts
 */
function sendCriticalAlertEmails($db)
{
    $emailsSent = 0;
    
    try {
        // Get critical alerts that haven't been sent
        $criticalAlerts = $db->fetchAll(
            "SELECT a.*, c.name as company_name, c.email as company_email
             FROM automated_alerts a
             JOIN companies c ON a.company_id = c.id
             WHERE a.severity = 'critical'
             AND a.status = 'pending'
             AND a.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)",
            []
        );

        foreach ($criticalAlerts as $alert) {
            // In a real implementation, you would send actual emails here
            // For now, we'll just mark them as sent and log the action
            
            $db->execute(
                "UPDATE automated_alerts SET 
                 status = 'sent', 
                 sent_at = NOW(),
                 updated_at = NOW()
                 WHERE id = ?",
                [$alert['id']]
            );

            echo "Email notification sent for alert: {$alert['title']}\n";
            $emailsSent++;
        }

        return $emailsSent;

    } catch (Exception $e) {
        echo "Error sending email notifications: " . $e->getMessage() . "\n";
        return 0;
    }
}
?>
