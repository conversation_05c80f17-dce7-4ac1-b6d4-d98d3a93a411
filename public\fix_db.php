<?php
/**
 * Quick Database Fix Script
 */

// Include the application bootstrap
require_once __DIR__ . '/../src/autoload.php';
require_once __DIR__ . '/../src/config/database.php';

use App\Core\Database;

try {
    $db = Database::getInstance();
    
    echo "<h1>Database Fix Script</h1>";
    
    // Fix 1: Add reference_number to bundles table
    echo "<h2>Fix 1: Adding reference_number to bundles table</h2>";
    try {
        $db->execute("ALTER TABLE bundles ADD COLUMN reference_number VARCHAR(50) UNIQUE");
        echo "<p>✅ Added reference_number column to bundles table</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p>✅ reference_number column already exists in bundles table</p>";
        } else {
            echo "<p>❌ Error adding reference_number to bundles: " . $e->getMessage() . "</p>";
        }
    }
    
    // Fix 2: Add other missing columns to bundles
    echo "<h2>Fix 2: Adding other missing columns to bundles</h2>";
    $bundleColumns = [
        'bundle_type' => "VARCHAR(50) DEFAULT 'custom'",
        'category' => "VARCHAR(100) DEFAULT 'general'",
        'priority' => "VARCHAR(20) DEFAULT 'medium'",
        'retention_period' => "INT DEFAULT 7",
        'access_level' => "VARCHAR(20) DEFAULT 'private'",
        'document_count' => "INT DEFAULT 0",
        'total_size' => "BIGINT DEFAULT 0",
        'closed_at' => "TIMESTAMP NULL",
        'closed_by' => "INT NULL"
    ];
    
    foreach ($bundleColumns as $column => $definition) {
        try {
            $db->execute("ALTER TABLE bundles ADD COLUMN {$column} {$definition}");
            echo "<p>✅ Added {$column} column to bundles table</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p>✅ {$column} column already exists in bundles table</p>";
            } else {
                echo "<p>❌ Error adding {$column} to bundles: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Fix 3: Add missing columns to document_intake
    echo "<h2>Fix 3: Adding missing columns to document_intake</h2>";
    $intakeColumns = [
        'reference_number' => "VARCHAR(50) UNIQUE",
        'priority' => "VARCHAR(20) DEFAULT 'medium'",
        'expected_count' => "INT DEFAULT 0",
        'actual_count' => "INT DEFAULT 0",
        'sensitivity_level' => "VARCHAR(20) DEFAULT 'internal'",
        'department' => "VARCHAR(255)",
        'notes' => "TEXT",
        'bundle_id' => "INT NULL"
    ];
    
    foreach ($intakeColumns as $column => $definition) {
        try {
            $db->execute("ALTER TABLE document_intake ADD COLUMN {$column} {$definition}");
            echo "<p>✅ Added {$column} column to document_intake table</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p>✅ {$column} column already exists in document_intake table</p>";
            } else {
                echo "<p>❌ Error adding {$column} to document_intake: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Fix 4: Add missing columns to boxes
    echo "<h2>Fix 4: Adding missing columns to boxes</h2>";
    $boxColumns = [
        'client_prefix' => "VARCHAR(20)",
        'box_number' => "INT",
        'storage_location_code' => "VARCHAR(100)",
        'row_number' => "VARCHAR(10) DEFAULT 'R1'",
        'shelf_number' => "VARCHAR(10) DEFAULT 'S1'",
        'position_number' => "VARCHAR(10)",
        'capacity' => "INT DEFAULT 100",
        'current_count' => "INT DEFAULT 0",
        'barcode_value' => "VARCHAR(255)",
        'barcode_generated_at' => "TIMESTAMP NULL"
    ];
    
    foreach ($boxColumns as $column => $definition) {
        try {
            $db->execute("ALTER TABLE boxes ADD COLUMN {$column} {$definition}");
            echo "<p>✅ Added {$column} column to boxes table</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p>✅ {$column} column already exists in boxes table</p>";
            } else {
                echo "<p>❌ Error adding {$column} to boxes: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Fix 5: Add missing columns to documents
    echo "<h2>Fix 5: Adding missing columns to documents</h2>";
    $documentColumns = [
        'storage_type' => "VARCHAR(20) DEFAULT 'physical'",
        'tags' => "JSON",
        'ocr_text' => "TEXT"
    ];
    
    foreach ($documentColumns as $column => $definition) {
        try {
            $db->execute("ALTER TABLE documents ADD COLUMN {$column} {$definition}");
            echo "<p>✅ Added {$column} column to documents table</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p>✅ {$column} column already exists in documents table</p>";
            } else {
                echo "<p>❌ Error adding {$column} to documents: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Fix 6: Check if delivery tables exist and create them if needed
    echo "<h2>Fix 6: Checking delivery tables</h2>";

    // Check if delivery_requests table exists
    $deliveryTableExists = $db->fetch("SHOW TABLES LIKE 'delivery_requests'");

    if (!$deliveryTableExists) {
        echo "<p>❌ delivery_requests table doesn't exist. Creating it...</p>";

        // Create delivery_requests table
        $createDeliveryTable = "
        CREATE TABLE delivery_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            company_id INT NOT NULL,
            delivery_reference VARCHAR(50) UNIQUE NOT NULL,

            target_type ENUM('box', 'bundle', 'document') NOT NULL,
            target_id INT NOT NULL,

            client_id INT,
            client_name VARCHAR(255),

            delivery_type ENUM('physical', 'digital', 'both') DEFAULT 'physical',
            delivery_method ENUM('courier', 'postal', 'express', 'pickup', 'email') DEFAULT 'courier',
            priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',

            requested_delivery_date DATE,
            scheduled_delivery_date DATE,

            delivery_address TEXT,
            special_instructions TEXT,

            status ENUM('pending', 'approved', 'rejected', 'scheduled', 'dispatched', 'delivered', 'failed', 'returned') DEFAULT 'pending',

            approved_by INT,
            approved_at TIMESTAMP NULL,
            rejected_by INT,
            rejected_at TIMESTAMP NULL,
            rejection_reason TEXT,

            dispatched_at TIMESTAMP NULL,
            courier_id INT,
            tracking_number VARCHAR(100),

            delivered_at TIMESTAMP NULL,
            recipient_name VARCHAR(255),
            recipient_signature TEXT,

            returned_at TIMESTAMP NULL,

            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(id),

            INDEX idx_delivery_company (company_id),
            INDEX idx_delivery_status (status),
            INDEX idx_delivery_target (target_type, target_id),
            INDEX idx_delivery_client (client_id),
            INDEX idx_delivery_date (requested_delivery_date)
        )";

        try {
            $db->execute($createDeliveryTable);
            echo "<p>✅ Created delivery_requests table</p>";
        } catch (Exception $e) {
            echo "<p>❌ Error creating delivery_requests table: " . $e->getMessage() . "</p>";
        }

        // Create delivery_items table
        $createDeliveryItemsTable = "
        CREATE TABLE delivery_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            delivery_id INT NOT NULL,

            item_type ENUM('box', 'bundle', 'document') NOT NULL,
            item_id INT NOT NULL,
            item_name VARCHAR(255) NOT NULL,
            quantity INT DEFAULT 1,

            status ENUM('pending', 'prepared', 'dispatched', 'delivered', 'returned') DEFAULT 'pending',

            notes TEXT,

            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (delivery_id) REFERENCES delivery_requests(id) ON DELETE CASCADE,

            INDEX idx_delivery_items_delivery (delivery_id),
            INDEX idx_delivery_items_item (item_type, item_id)
        )";

        try {
            $db->execute($createDeliveryItemsTable);
            echo "<p>✅ Created delivery_items table</p>";
        } catch (Exception $e) {
            echo "<p>❌ Error creating delivery_items table: " . $e->getMessage() . "</p>";
        }

    } else {
        echo "<p>✅ delivery_requests table exists</p>";

        // Check if delivery_reference column exists
        $columns = $db->fetchAll("SHOW COLUMNS FROM delivery_requests");
        $hasDeliveryReference = false;

        foreach ($columns as $column) {
            if ($column['Field'] === 'delivery_reference') {
                $hasDeliveryReference = true;
                break;
            }
        }

        if (!$hasDeliveryReference) {
            try {
                $db->execute("ALTER TABLE delivery_requests ADD COLUMN delivery_reference VARCHAR(50) UNIQUE");
                echo "<p>✅ Added delivery_reference column to delivery_requests table</p>";
            } catch (Exception $e) {
                echo "<p>❌ Error adding delivery_reference: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>✅ delivery_reference column exists</p>";
        }
    }

    // Fix 7: Create other missing tables
    echo "<h2>Fix 7: Creating other missing tables</h2>";

    $missingTables = [
        'box_movements' => "
        CREATE TABLE box_movements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            box_id INT NOT NULL,

            old_location VARCHAR(255),
            new_location VARCHAR(255) NOT NULL,
            movement_type ENUM('storage', 'delivery', 'return', 'transfer') DEFAULT 'storage',

            moved_by INT,
            moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,

            FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
            FOREIGN KEY (moved_by) REFERENCES users(id),

            INDEX idx_box_movements_box (box_id),
            INDEX idx_box_movements_date (moved_at)
        )",

        'search_logs' => "
        CREATE TABLE search_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            company_id INT NOT NULL,

            search_query TEXT NOT NULL,
            search_filters JSON,
            result_count INT DEFAULT 0,
            search_time_ms DECIMAL(10,2),

            search_context VARCHAR(50),

            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,

            INDEX idx_search_logs_company (company_id),
            INDEX idx_search_logs_user (user_id),
            INDEX idx_search_logs_date (created_at)
        )",

        'box_bundles' => "
        CREATE TABLE box_bundles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            box_id INT NOT NULL,
            bundle_id INT NOT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            added_by INT,

            UNIQUE KEY unique_box_bundle (box_id, bundle_id),
            FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE CASCADE,
            FOREIGN KEY (bundle_id) REFERENCES bundles(id) ON DELETE CASCADE,
            FOREIGN KEY (added_by) REFERENCES users(id)
        )"
    ];

    foreach ($missingTables as $tableName => $createSql) {
        $tableExists = $db->fetch("SHOW TABLES LIKE '{$tableName}'");

        if (!$tableExists) {
            try {
                $db->execute($createSql);
                echo "<p>✅ Created {$tableName} table</p>";
            } catch (Exception $e) {
                echo "<p>❌ Error creating {$tableName} table: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>✅ {$tableName} table already exists</p>";
        }
    }

    echo "<h2>✅ All database fixes completed!</h2>";
    echo "<p><a href='test_services.php'>Test Services Again</a></p>";
    
} catch (Exception $e) {
    echo "<h1>Database Error</h1>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
