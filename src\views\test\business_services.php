<?php
/**
 * Business Services Test Page
 *
 * Simple web interface to test business services
 */

// Start output buffering for content
ob_start();
?>
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🧪 Business Services Test</h1>
        
        <!-- Service Status -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">📊 Service Status</h2>
            <button onclick="checkServiceStatus()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Check Service Status
            </button>
            <div id="serviceStatus" class="mt-4"></div>
        </div>

        <!-- Box Handling Service Tests -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">📦 Box Handling Service</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-medium mb-2">Create Box</h3>
                    <form onsubmit="createBox(event)" class="space-y-2">
                        <input type="text" name="name" placeholder="Box Name" required class="w-full p-2 border rounded">
                        <input type="number" name="warehouse_id" placeholder="Warehouse ID" value="1" required class="w-full p-2 border rounded">
                        <input type="text" name="client_prefix" placeholder="Client Prefix" value="TEST" required class="w-full p-2 border rounded">
                        <textarea name="description" placeholder="Description" class="w-full p-2 border rounded"></textarea>
                        <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Create Box</button>
                    </form>
                </div>
                <div id="boxResults" class="bg-gray-50 p-4 rounded"></div>
            </div>
        </div>

        <!-- Bundle Handling Service Tests -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">📁 Bundle Handling Service</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-medium mb-2">Create Bundle</h3>
                    <form onsubmit="createBundle(event)" class="space-y-2">
                        <input type="text" name="name" placeholder="Bundle Name" required class="w-full p-2 border rounded">
                        <textarea name="description" placeholder="Description" class="w-full p-2 border rounded"></textarea>
                        <select name="bundle_type" class="w-full p-2 border rounded">
                            <option value="custom">Custom</option>
                            <option value="intake">Intake</option>
                            <option value="project">Project</option>
                            <option value="department">Department</option>
                        </select>
                        <select name="priority" class="w-full p-2 border rounded">
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                        <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Create Bundle</button>
                    </form>
                </div>
                <div id="bundleResults" class="bg-gray-50 p-4 rounded"></div>
            </div>
        </div>

        <!-- Search Service Tests -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">🔍 Search Service</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-medium mb-2">Search</h3>
                    <form onsubmit="performSearch(event)" class="space-y-2">
                        <input type="text" name="query" placeholder="Search query" required class="w-full p-2 border rounded">
                        <div class="flex space-x-2">
                            <label><input type="checkbox" name="search_types" value="documents" checked> Documents</label>
                            <label><input type="checkbox" name="search_types" value="bundles" checked> Bundles</label>
                            <label><input type="checkbox" name="search_types" value="boxes" checked> Boxes</label>
                        </div>
                        <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Search</button>
                    </form>
                    
                    <h3 class="font-medium mb-2 mt-4">Search Suggestions</h3>
                    <form onsubmit="getSearchSuggestions(event)" class="space-y-2">
                        <input type="text" name="query" placeholder="Type for suggestions" class="w-full p-2 border rounded">
                        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Get Suggestions</button>
                    </form>
                </div>
                <div id="searchResults" class="bg-gray-50 p-4 rounded"></div>
            </div>
        </div>

        <!-- Intake Service Tests -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">📥 Intake Service</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-medium mb-2">Create Intake Request</h3>
                    <form onsubmit="createIntakeRequest(event)" class="space-y-2">
                        <input type="text" name="client_name" placeholder="Client Name" required class="w-full p-2 border rounded">
                        <input type="text" name="source" placeholder="Source" required class="w-full p-2 border rounded">
                        <select name="document_type" required class="w-full p-2 border rounded">
                            <option value="">Select Document Type</option>
                            <option value="contract">Contract</option>
                            <option value="invoice">Invoice</option>
                            <option value="report">Report</option>
                            <option value="image">Image</option>
                            <option value="other">Other</option>
                        </select>
                        <textarea name="description" placeholder="Description" required class="w-full p-2 border rounded"></textarea>
                        <input type="number" name="expected_count" placeholder="Expected Count" class="w-full p-2 border rounded">
                        <label><input type="checkbox" name="auto_create_bundle" value="1"> Auto-create Bundle</label>
                        <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Create Intake</button>
                    </form>
                </div>
                <div id="intakeResults" class="bg-gray-50 p-4 rounded"></div>
            </div>
        </div>

        <!-- Delivery Service Tests -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">🚚 Delivery Service</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-medium mb-2">Create Delivery Request</h3>
                    <form onsubmit="createDeliveryRequest(event)" class="space-y-2">
                        <select name="target_type" required class="w-full p-2 border rounded">
                            <option value="">Select Target Type</option>
                            <option value="box">Box</option>
                            <option value="bundle">Bundle</option>
                            <option value="document">Document</option>
                        </select>
                        <input type="number" name="target_id" placeholder="Target ID" required class="w-full p-2 border rounded">
                        <input type="number" name="client_id" placeholder="Client ID" required class="w-full p-2 border rounded">
                        <select name="delivery_type" required class="w-full p-2 border rounded">
                            <option value="physical">Physical</option>
                            <option value="digital">Digital</option>
                            <option value="both">Both</option>
                        </select>
                        <select name="delivery_method" class="w-full p-2 border rounded">
                            <option value="courier">Courier</option>
                            <option value="postal">Postal</option>
                            <option value="express">Express</option>
                            <option value="pickup">Pickup</option>
                        </select>
                        <textarea name="delivery_address" placeholder="Delivery Address" class="w-full p-2 border rounded"></textarea>
                        <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Create Delivery</button>
                    </form>
                </div>
                <div id="deliveryResults" class="bg-gray-50 p-4 rounded"></div>
            </div>
        </div>
    </div>

    <script>
        // Helper function to make API calls
        async function apiCall(url, method = 'GET', data = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Service Status Check
        async function checkServiceStatus() {
            const result = await apiCall('/app/services/status');
            document.getElementById('serviceStatus').innerHTML = `
                <pre class="bg-gray-100 p-4 rounded text-sm">${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        // Box Service Tests
        async function createBox(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            const result = await apiCall('/app/services/boxes/create', 'POST', data);
            document.getElementById('boxResults').innerHTML = `
                <h4 class="font-medium mb-2">Result:</h4>
                <pre class="bg-gray-100 p-4 rounded text-sm">${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        // Bundle Service Tests
        async function createBundle(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            const result = await apiCall('/app/services/bundles/create', 'POST', data);
            document.getElementById('bundleResults').innerHTML = `
                <h4 class="font-medium mb-2">Result:</h4>
                <pre class="bg-gray-100 p-4 rounded text-sm">${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        // Search Service Tests
        async function performSearch(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const query = formData.get('query');
            const searchTypes = formData.getAll('search_types');
            
            const params = new URLSearchParams({
                q: query,
                'filters[search_types]': searchTypes.join(',')
            });
            
            const result = await apiCall(`/app/services/search?${params}`);
            document.getElementById('searchResults').innerHTML = `
                <h4 class="font-medium mb-2">Search Results:</h4>
                <pre class="bg-gray-100 p-4 rounded text-sm">${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        async function getSearchSuggestions(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const query = formData.get('query');
            
            const result = await apiCall(`/app/services/search/suggestions?q=${encodeURIComponent(query)}`);
            document.getElementById('searchResults').innerHTML = `
                <h4 class="font-medium mb-2">Suggestions:</h4>
                <pre class="bg-gray-100 p-4 rounded text-sm">${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        // Intake Service Tests
        async function createIntakeRequest(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            const result = await apiCall('/app/services/intake/create', 'POST', data);
            document.getElementById('intakeResults').innerHTML = `
                <h4 class="font-medium mb-2">Result:</h4>
                <pre class="bg-gray-100 p-4 rounded text-sm">${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        // Delivery Service Tests
        async function createDeliveryRequest(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            const result = await apiCall('/app/services/delivery/create', 'POST', data);
            document.getElementById('deliveryResults').innerHTML = `
                <h4 class="font-medium mb-2">Result:</h4>
                <pre class="bg-gray-100 p-4 rounded text-sm">${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        // Load service status on page load
        window.onload = function() {
            checkServiceStatus();
        };
    </script>

<?php
// Capture content and include in layout
$content = ob_get_clean();
require_once APP_ROOT . '/src/views/layouts/app.php';
?>
