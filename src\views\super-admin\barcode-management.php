<?php
$title = $title ?? 'Advanced Barcode Management';
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
    
    <!-- Header -->
    <div class="bg-white/90 backdrop-blur-sm border-b border-white/30 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Advanced Barcode Management</h1>
                    <p class="text-gray-600 mt-1">Bulk operations, analytics, and comprehensive tracking</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Barcode System Status</p>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <p class="font-medium text-green-600">Operational</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Quick Actions -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="<?= url('/super-admin/barcode-management/bulk-generate') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Bulk Generate
                </a>
                
                <a href="<?= url('/super-admin/barcode-management/mobile-scanner') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                    </svg>
                    Mobile Scanner
                </a>
                
                <a href="<?= url('/super-admin/barcode-management/analytics') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Analytics
                </a>
                
                <a href="<?= url('/super-admin/barcode-management/audit-trail') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Audit Trail
                </a>
            </div>
        </div>

        <!-- Barcode Overview Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Total Barcodes -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Barcodes</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($barcodeStats['total_barcodes']) ?></p>
                        <p class="text-xs text-green-600"><?= number_format($barcodeStats['active_barcodes']) ?> active</p>
                    </div>
                </div>
            </div>

            <!-- Scanned Today -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Generated Today</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($barcodeStats['generated_today']) ?></p>
                        <p class="text-xs text-blue-600"><?= number_format($barcodeStats['scanned_barcodes']) ?> ever scanned</p>
                    </div>
                </div>
            </div>

            <!-- Total Scans -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Scans</p>
                        <p class="text-2xl font-bold text-gray-900"><?= number_format($barcodeStats['total_scans']) ?></p>
                        <p class="text-xs text-purple-600"><?= number_format($barcodeStats['avg_scans_per_barcode'], 1) ?> avg per barcode</p>
                    </div>
                </div>
            </div>

            <!-- Distribution -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Distribution</p>
                        <div class="text-xs text-gray-600 space-y-1">
                            <p><?= number_format($barcodeStats['box_barcodes']) ?> boxes</p>
                            <p><?= number_format($barcodeStats['bundle_barcodes']) ?> bundles</p>
                            <p><?= number_format($barcodeStats['document_barcodes']) ?> documents</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Usage Analytics -->
            <div class="lg:col-span-2 bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Barcode Usage Analytics</h3>
                    <a href="<?= url('/super-admin/barcode-management/analytics') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View detailed analytics →
                    </a>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Entity Type</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Total Barcodes</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Total Scans</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Usage Rate</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($usageAnalytics as $analytics): ?>
                                <tr class="border-b border-gray-100 hover:bg-gray-50">
                                    <td class="py-3 px-4">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-<?= $analytics['entity_type'] === 'box' ? 'blue' : ($analytics['entity_type'] === 'bundle' ? 'green' : 'purple') ?>-100 rounded-lg flex items-center justify-center mr-3">
                                                <svg class="w-4 h-4 text-<?= $analytics['entity_type'] === 'box' ? 'blue' : ($analytics['entity_type'] === 'bundle' ? 'green' : 'purple') ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <?php if ($analytics['entity_type'] === 'box'): ?>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                                    <?php elseif ($analytics['entity_type'] === 'bundle'): ?>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 00-2 2v2a2 2 0 002 2m0 0h14m-14 0a2 2 0 002 2v2a2 2 0 01-2 2"></path>
                                                    <?php else: ?>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    <?php endif; ?>
                                                </svg>
                                            </div>
                                            <span class="font-medium text-gray-900"><?= ucfirst($analytics['entity_type']) ?>s</span>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4 text-sm text-gray-900"><?= number_format($analytics['total_barcodes']) ?></td>
                                    <td class="py-3 px-4 text-sm text-gray-900"><?= number_format($analytics['total_scans']) ?></td>
                                    <td class="py-3 px-4">
                                        <?php $usageRate = $analytics['total_barcodes'] > 0 ? ($analytics['used_barcodes'] / $analytics['total_barcodes']) * 100 : 0; ?>
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-<?= $usageRate > 70 ? 'green' : ($usageRate > 40 ? 'yellow' : 'red') ?>-500 h-2 rounded-full" style="width: <?= $usageRate ?>%"></div>
                                            </div>
                                            <span class="text-xs text-gray-600"><?= round($usageRate, 1) ?>%</span>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Activities & Quick Stats -->
            <div class="space-y-6">
                
                <!-- Recent Activities -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Activities</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Live
                        </span>
                    </div>
                    
                    <div class="space-y-3">
                        <?php if (!empty($recentActivities)): ?>
                            <?php foreach (array_slice($recentActivities, 0, 5) as $activity): ?>
                                <div class="flex items-start p-3 bg-gray-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-<?= $activity['action'] === 'generated' ? 'green' : 'blue' ?>-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-<?= $activity['action'] === 'generated' ? 'green' : 'blue' ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <?php if ($activity['action'] === 'generated'): ?>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                <?php else: ?>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <?php endif; ?>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <h4 class="text-sm font-medium text-gray-900"><?= ucfirst($activity['action']) ?> - <?= e($activity['entity_name']) ?></h4>
                                        <p class="text-sm text-gray-600 mt-1"><?= e($activity['barcode_value']) ?></p>
                                        <p class="text-xs text-gray-500 mt-1">by <?= e($activity['first_name'] . ' ' . $activity['last_name']) ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-gray-500 text-sm">No recent activities</p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mt-4">
                        <a href="<?= url('/super-admin/barcode-management/audit-trail') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View full audit trail →
                        </a>
                    </div>
                </div>

                <!-- Generation Trends -->
                <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Generation Trends (30 days)</h3>
                    
                    <div class="space-y-4">
                        <?php 
                        $trendsByType = [];
                        foreach ($generationTrends as $trend) {
                            $trendsByType[$trend['entity_type']][] = $trend;
                        }
                        ?>
                        
                        <?php foreach (['box', 'bundle', 'document'] as $type): ?>
                            <?php if (isset($trendsByType[$type])): ?>
                                <?php $totalGenerated = array_sum(array_column($trendsByType[$type], 'barcodes_generated')); ?>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600"><?= ucfirst($type) ?>s</span>
                                    <span class="font-medium text-gray-900"><?= number_format($totalGenerated) ?></span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>

            </div>

        </div>

    </div>
</div>

<script>
// Auto-refresh dashboard every 30 seconds for real-time updates
setTimeout(() => {
    location.reload();
}, 30000);

// Initialize any charts or interactive elements here
document.addEventListener('DOMContentLoaded', function() {
    // Add any JavaScript for interactive features
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
