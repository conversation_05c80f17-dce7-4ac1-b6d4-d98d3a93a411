<?php
$title = 'Intake Details - ' . ($intake['reference_number'] ?? 'Intake');
ob_start();
?>

<!-- Enhanced Intake Details -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Floating Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/20 to-blue-600/20 rounded-full blur-3xl"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Enhanced Page Header -->
        <div class="mb-12">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div class="flex items-center space-x-6">
                    <!-- Status Icon -->
                    <div class="relative">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <!-- Status Indicator -->
                        <div class="absolute -bottom-2 -right-2 w-8 h-8 rounded-full border-4 border-white shadow-lg
                            <?php
                            switch ($intake['status']) {
                                case 'pending': echo 'bg-orange-500'; break;
                                case 'processing': echo 'bg-blue-500'; break;
                                case 'completed': echo 'bg-green-500'; break;
                                case 'cancelled': echo 'bg-red-500'; break;
                            }
                            ?>">
                        </div>
                    </div>

                    <!-- Header Content -->
                    <div>
                        <div class="flex items-center space-x-3 mb-2">
                            <h1 class="text-4xl font-bold text-gray-900">
                                <?= e($intake['reference_number']) ?>
                            </h1>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                <?php
                                switch ($intake['status']) {
                                    case 'pending': echo 'bg-orange-100 text-orange-800 border border-orange-200'; break;
                                    case 'processing': echo 'bg-blue-100 text-blue-800 border border-blue-200'; break;
                                    case 'completed': echo 'bg-green-100 text-green-800 border border-green-200'; break;
                                    case 'cancelled': echo 'bg-red-100 text-red-800 border border-red-200'; break;
                                }
                                ?>">
                                <?= ucfirst($intake['status']) ?>
                            </span>
                        </div>
                        <p class="text-xl text-gray-600 mb-2"><?= e($intake['description']) ?></p>
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <?= e($intake['client_name']) ?>
                            </span>
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Created <?= date('M j, Y', strtotime($intake['created_at'])) ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center space-x-3">
                    <a href="<?= url('/app/intake') ?>" class="inline-flex items-center px-6 py-3 bg-white/80 backdrop-blur-sm border border-gray-200 text-gray-700 font-medium rounded-xl hover:bg-white hover:shadow-md transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Intake
                    </a>
                    <?php if ($intake['status'] === 'pending'): ?>
                        <button onclick="showProcessModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            Process Intake
                        </button>
                    <?php elseif ($intake['status'] === 'processing'): ?>
                        <div class="flex space-x-3">
                            <?php if (!empty($intake['bundle_id'])): ?>
                                <a href="<?= url('/app/bundles/' . $intake['bundle_id']) ?>"
                                   class="inline-flex items-center px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    View Bundle
                                </a>
                            <?php endif; ?>
                            <button onclick="showCompleteModal()" class="inline-flex items-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Complete Processing
                            </button>
                        </div>
                    <?php elseif ($intake['status'] === 'completed'): ?>
                        <div class="flex items-center space-x-2 text-green-600">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-medium">Processing Completed</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Workflow Progress -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-6 shadow-lg mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Workflow Progress</h2>
            <div class="flex items-center justify-between">
                <!-- Step 1: Created -->
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Created</p>
                        <p class="text-xs text-gray-500">Intake entry</p>
                    </div>
                </div>

                <!-- Connector -->
                <div class="flex-1 h-0.5 bg-gray-300 mx-4 <?= $intake['status'] !== 'pending' ? 'bg-green-500' : '' ?>"></div>

                <!-- Step 2: Processing -->
                <div class="flex items-center">
                    <div class="w-8 h-8 <?= $intake['status'] === 'processing' || $intake['status'] === 'completed' ? 'bg-green-500' : ($intake['status'] === 'pending' ? 'bg-blue-500' : 'bg-gray-300') ?> rounded-full flex items-center justify-center flex-shrink-0">
                        <?php if ($intake['status'] === 'processing' || $intake['status'] === 'completed'): ?>
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        <?php elseif ($intake['status'] === 'pending'): ?>
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        <?php else: ?>
                            <span class="text-xs text-gray-500">2</span>
                        <?php endif; ?>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Processing</p>
                        <p class="text-xs text-gray-500">Bundle assignment</p>
                    </div>
                </div>

                <!-- Connector -->
                <div class="flex-1 h-0.5 bg-gray-300 mx-4 <?= $intake['status'] === 'completed' ? 'bg-green-500' : '' ?>"></div>

                <!-- Step 3: Completed -->
                <div class="flex items-center">
                    <div class="w-8 h-8 <?= $intake['status'] === 'completed' ? 'bg-green-500' : 'bg-gray-300' ?> rounded-full flex items-center justify-center flex-shrink-0">
                        <?php if ($intake['status'] === 'completed'): ?>
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        <?php else: ?>
                            <span class="text-xs text-gray-500">3</span>
                        <?php endif; ?>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Completed</p>
                        <p class="text-xs text-gray-500">Final storage</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Intake Information Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            
            <!-- Intake Details Card -->
            <div class="lg:col-span-2 bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Intake Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Reference Number</label>
                        <p class="text-lg font-semibold text-gray-900"><?= e($intake['reference_number']) ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Status</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium 
                            <?php
                            switch ($intake['status']) {
                                case 'pending': echo 'bg-orange-100 text-orange-800'; break;
                                case 'processing': echo 'bg-blue-100 text-blue-800'; break;
                                case 'completed': echo 'bg-green-100 text-green-800'; break;
                                case 'cancelled': echo 'bg-red-100 text-red-800'; break;
                            }
                            ?>">
                            <?= ucfirst($intake['status']) ?>
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Source</label>
                        <p class="text-lg font-semibold text-gray-900"><?= ucfirst(str_replace('_', ' ', $intake['source'])) ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Document Type</label>
                        <p class="text-lg font-semibold text-gray-900"><?= ucfirst($intake['document_type']) ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Priority</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium 
                            <?php
                            switch ($intake['priority']) {
                                case 'urgent': echo 'bg-red-100 text-red-800'; break;
                                case 'high': echo 'bg-orange-100 text-orange-800'; break;
                                case 'medium': echo 'bg-yellow-100 text-yellow-800'; break;
                                case 'low': echo 'bg-green-100 text-green-800'; break;
                            }
                            ?>">
                            <?= ucfirst($intake['priority']) ?>
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Expected Count</label>
                        <p class="text-lg font-semibold text-gray-900"><?= number_format($intake['expected_count']) ?> documents</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Actual Count</label>
                        <p class="text-lg font-semibold text-gray-900"><?= number_format($intake['actual_count']) ?> documents</p>
                    </div>
                    
                    <?php if (!empty($intake['bundle_name'])): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Assigned Bundle</label>
                        <p class="text-lg font-semibold text-blue-600"><?= e($intake['bundle_name']) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
                
                <?php if (!empty($intake['notes'])): ?>
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Notes</label>
                    <p class="text-gray-700"><?= e($intake['notes']) ?></p>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($intake['processing_notes'])): ?>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Processing Notes</label>
                    <p class="text-gray-700"><?= e($intake['processing_notes']) ?></p>
                </div>
                <?php endif; ?>
            </div>

            <!-- Timeline Card -->
            <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Timeline</h2>
                
                <div class="space-y-6">
                    <!-- Created -->
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Created</h3>
                            <p class="text-sm text-gray-600"><?= date('M j, Y g:i A', strtotime($intake['created_at'])) ?></p>
                            <?php if (!empty($intake['creator_first_name'])): ?>
                                <p class="text-sm text-gray-500">by <?= e($intake['creator_first_name'] . ' ' . $intake['creator_last_name']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Processed -->
                    <?php if (!empty($intake['processed_at'])): ?>
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Processing Started</h3>
                            <p class="text-sm text-gray-600"><?= date('M j, Y g:i A', strtotime($intake['processed_at'])) ?></p>
                            <?php if (!empty($intake['processor_first_name'])): ?>
                                <p class="text-sm text-gray-500">by <?= e($intake['processor_first_name'] . ' ' . $intake['processor_last_name']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Completed -->
                    <?php if (!empty($intake['completed_at'])): ?>
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Completed</h3>
                            <p class="text-sm text-gray-600"><?= date('M j, Y g:i A', strtotime($intake['completed_at'])) ?></p>
                            <?php if (!empty($intake['completer_first_name'])): ?>
                                <p class="text-sm text-gray-500">by <?= e($intake['completer_first_name'] . ' ' . $intake['completer_last_name']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Processing Status (only show when processing) -->
        <?php if ($intake['status'] === 'processing'): ?>
        <div class="bg-blue-50/90 backdrop-blur-sm border border-blue-200/50 rounded-3xl p-8 shadow-lg mb-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-blue-900">Processing Status</h2>
                <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-blue-100 text-blue-800">
                    In Progress
                </span>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-blue-900 mb-3">✅ Completed Steps</h3>
                    <ul class="space-y-2 text-sm text-blue-700">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Intake entry created
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php if (!empty($intake['bundle_name'])): ?>
                                Assigned to bundle: <strong><?= e($intake['bundle_name']) ?></strong>
                            <?php else: ?>
                                Bundle assignment completed
                            <?php endif; ?>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Processing started
                        </li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold text-blue-900 mb-3">📋 Next Steps</h3>
                    <ul class="space-y-2 text-sm text-blue-700">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Upload/scan documents
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Verify document count
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Complete processing
                        </li>
                    </ul>
                </div>
            </div>

            <div class="mt-6 p-4 bg-blue-100 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm text-blue-800">
                        <strong>Expected:</strong> <?= number_format($intake['expected_count']) ?> documents |
                        <strong>Current:</strong> <?= number_format($intake['actual_count']) ?> documents
                        <?php if ($intake['actual_count'] >= $intake['expected_count']): ?>
                            <span class="text-green-600 font-medium">✓ Target reached</span>
                        <?php else: ?>
                            <span class="text-orange-600 font-medium">(<?= $intake['expected_count'] - $intake['actual_count'] ?> remaining)</span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions Panel -->
        <div class="bg-white/90 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-lg">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900 flex items-center">
                    <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Quick Actions
                </h2>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    Intake #<?= e($intake['reference_number']) ?>
                </span>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- View Documents -->
                <a href="<?= url('/app/documents?intake_id=' . $intake['id']) ?>"
                   class="group bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center mr-3 group-hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">View Documents</h3>
                            <p class="text-sm text-gray-600">Browse associated files</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-2xl font-bold text-blue-600"><?= count($documents) ?></span>
                        <svg class="w-5 h-5 text-blue-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </a>

                <!-- Add Document -->
                <a href="<?= url('/app/documents/create?intake_id=' . $intake['id']) ?>"
                   class="group bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-green-600 rounded-xl flex items-center justify-center mr-3 group-hover:bg-green-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 group-hover:text-green-700 transition-colors">Add Document</h3>
                            <p class="text-sm text-gray-600">Upload new file</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-green-600">Upload</span>
                        <svg class="w-5 h-5 text-green-400 group-hover:text-green-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </a>

                <!-- Generate Report -->
                <a href="<?= url('/app/reports/intake/' . $intake['id']) ?>"
                   class="group bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-purple-600 rounded-xl flex items-center justify-center mr-3 group-hover:bg-purple-700 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 group-hover:text-purple-700 transition-colors">Generate Report</h3>
                            <p class="text-sm text-gray-600">Export intake summary</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-purple-600">Export</span>
                        <svg class="w-5 h-5 text-purple-400 group-hover:text-purple-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </a>
            </div>

            <!-- Additional Info -->
            <div class="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm text-blue-800">
                        <strong>Document Status:</strong> <?= number_format($intake['actual_count']) ?> of <?= number_format($intake['expected_count']) ?> documents received
                        <?php if ($intake['actual_count'] >= $intake['expected_count']): ?>
                            <span class="text-green-600 font-medium ml-2">✓ Complete</span>
                        <?php else: ?>
                            <span class="text-orange-600 font-medium ml-2">⏳ In Progress</span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Process Intake Modal -->
<?php if ($intake['status'] === 'pending'): ?>
<div id="processModal" class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm hidden items-center justify-center z-50 p-4">
    <div class="bg-white rounded-3xl shadow-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-2xl font-bold mb-1">Process Intake - Enhanced Workflow</h3>
                    <p class="text-blue-100 text-sm">Reference: <?= e($intake['reference_number']) ?> • Client: <?= e($intake['client_name']) ?></p>
                </div>
                <button type="button" onclick="hideProcessModal()" class="text-white hover:text-gray-200 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-8 overflow-y-auto max-h-[calc(95vh-120px)]">
            <form action="<?= url('/app/intake/' . $intake['id'] . '/process') ?>" method="POST" class="space-y-8">

                <!-- Physical Document Processing -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6 shadow-sm">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold text-blue-900">Physical Document Processing</h4>
                            <p class="text-blue-700 text-sm">Configure physical storage and tracking options</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Document Sorting Category -->
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-800 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                    Sorting Category <span class="text-red-500 ml-1">*</span>
                                </span>
                            </label>
                            <div class="flex gap-2">
                                <select id="sorting_category" name="sorting_category" class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white shadow-sm">
                                    <option value="">Select sorting category</option>
                                    <?php foreach ($sortingCategories as $category): ?>
                                        <option value="<?= e($category['category_code']) ?>">
                                            <?= e($category['emoji'] ?? '') ?> <?= e($category['category_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="button" onclick="showCreateSortingCategoryModal()"
                                        class="px-4 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md"
                                        title="Create New Sorting Category">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                </button>
                            </div>
                            <p class="text-xs text-gray-600 mt-2">Choose how documents will be physically sorted and organized</p>
                        </div>

                        <!-- Digitization Options -->
                        <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                            <label class="flex items-start cursor-pointer group">
                                <input type="checkbox" name="digitization_required" value="1"
                                       class="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 mt-0.5 group-hover:border-blue-400 transition-colors">
                                <div class="ml-3">
                                    <span class="text-sm font-semibold text-gray-800 group-hover:text-blue-700 transition-colors">Digitization Required</span>
                                    <p class="text-xs text-gray-600 mt-1">Documents will be scanned for digital backup and online access</p>
                                </div>
                            </label>
                        </div>

                        <!-- Barcode Generation -->
                        <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                            <label class="flex items-start cursor-pointer group">
                                <input type="checkbox" name="generate_barcode" value="yes" checked
                                       class="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 mt-0.5 group-hover:border-blue-400 transition-colors">
                                <div class="ml-3">
                                    <span class="text-sm font-semibold text-gray-800 group-hover:text-blue-700 transition-colors">Generate Barcode/QR Code</span>
                                    <p class="text-xs text-gray-600 mt-1">Create unique barcode for physical document tracking and identification</p>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Box Assignment -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 shadow-sm">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-green-600 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold text-green-900">Box Assignment</h4>
                            <p class="text-green-700 text-sm">Organize documents into physical storage boxes</p>
                        </div>
                    </div>

                    <!-- Assignment Options -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <label class="relative cursor-pointer group">
                            <input type="radio" name="assign_box" value="no" checked
                                   class="sr-only peer"
                                   onchange="toggleBoxAssignment('no')">
                            <div class="bg-white border-2 border-gray-200 rounded-xl p-4 peer-checked:border-green-500 peer-checked:bg-green-50 group-hover:border-green-300 transition-all duration-200">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-green-500 peer-checked:bg-green-500 mr-3 flex items-center justify-center">
                                        <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                    </div>
                                    <div>
                                        <span class="font-semibold text-gray-800 group-hover:text-green-700">Bundle Only</span>
                                        <p class="text-xs text-gray-600 mt-1">Organize into bundles without box assignment</p>
                                    </div>
                                </div>
                            </div>
                        </label>

                        <label class="relative cursor-pointer group">
                            <input type="radio" name="assign_box" value="yes"
                                   class="sr-only peer"
                                   onchange="toggleBoxAssignment('yes')">
                            <div class="bg-white border-2 border-gray-200 rounded-xl p-4 peer-checked:border-green-500 peer-checked:bg-green-50 group-hover:border-green-300 transition-all duration-200">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-green-500 peer-checked:bg-green-500 mr-3 flex items-center justify-center">
                                        <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                    </div>
                                    <div>
                                        <span class="font-semibold text-gray-800 group-hover:text-green-700">Assign to Box</span>
                                        <p class="text-xs text-gray-600 mt-1">Place documents in physical storage box</p>
                                    </div>
                                </div>
                            </div>
                        </label>
                    </div>

                    <!-- Box Assignment Options -->
                    <div id="box-assignment-section" class="hidden">
                        <div class="bg-white rounded-xl border border-gray-200 p-5 shadow-sm">
                            <div class="flex space-x-6 mb-6">
                                <label class="flex items-center cursor-pointer group">
                                    <input type="radio" name="box_option" value="existing"
                                           class="w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500 group-hover:border-green-400"
                                           onchange="toggleBoxOption('existing')">
                                    <span class="ml-2 text-sm font-medium text-gray-700 group-hover:text-green-700">Use Existing Box</span>
                                </label>
                                <label class="flex items-center cursor-pointer group">
                                    <input type="radio" name="box_option" value="new"
                                           class="w-4 h-4 text-green-600 border-gray-300 focus:ring-green-500 group-hover:border-green-400"
                                           onchange="toggleBoxOption('new')">
                                    <span class="ml-2 text-sm font-medium text-gray-700 group-hover:text-green-700">Create New Box</span>
                                </label>
                            </div>

                            <!-- Existing Box Selection -->
                            <div id="existing-box-section" class="hidden">
                                <label class="block text-sm font-semibold text-gray-800 mb-2">Select Box</label>
                                <select name="box_id" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white shadow-sm">
                                    <option value="">Choose an available box...</option>
                                    <!-- Boxes will be loaded dynamically -->
                                </select>
                            </div>

                            <!-- New Box Creation -->
                            <div id="new-box-section" class="hidden">
                                <div class="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                                    <div class="flex items-center mb-4">
                                        <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="text-lg font-bold text-green-900">Create New Box</h4>
                                            <p class="text-green-700 text-sm">Configure a new storage box for this intake</p>
                                        </div>
                                    </div>

                                    <!-- Box Basic Information -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-800 mb-2">
                                                Box Name <span class="text-red-500">*</span>
                                            </label>
                                            <div class="flex gap-2">
                                                <input type="text" name="box_name" required
                                                       class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                                       placeholder="e.g., Box A-001, Legal Documents Box">
                                                <button type="button" onclick="syncBoxNameWithIdentifier()"
                                                        class="px-4 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-all duration-200"
                                                        title="Sync Box Name with Identifier">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1">Choose a descriptive name for easy identification. Click sync button to match with identifier.</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-800 mb-2">
                                                Box Identifier <span class="text-red-500">*</span>
                                            </label>
                                            <div class="flex gap-2">
                                                <input type="text" name="box_identifier" required
                                                       class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                                       placeholder="e.g., BOX-A001, CLIENT01-BOX001">
                                                <button type="button" onclick="autoGenerateBoxIdentifier()"
                                                        class="px-4 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-200"
                                                        title="Auto-generate Box Identifier">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1">Unique identifier for tracking and labeling. Click the refresh button to auto-generate.</p>
                                        </div>
                                    </div>

                                    <!-- Warehouse and Storage Type -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-800 mb-2">
                                                Warehouse <span class="text-red-500">*</span>
                                            </label>
                                            <div class="flex gap-2">
                                                <select name="warehouse_id" required
                                                        class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200">
                                                    <option value="">Select a warehouse</option>
                                                    <?php
                                                    // Get warehouses for dropdown
                                                    $warehouses = $this->db->fetchAll(
                                                        "SELECT id, name, city, state FROM warehouses WHERE company_id = ? AND status = 'active' ORDER BY name",
                                                        [$this->user['company_id']]
                                                    );
                                                    foreach ($warehouses as $warehouse): ?>
                                                        <option value="<?= $warehouse['id'] ?>">
                                                            <?= e($warehouse['name']) ?> - <?= e($warehouse['city']) ?>, <?= e($warehouse['state']) ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <button type="button" onclick="showCreateWarehouseModal()"
                                                        class="px-4 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-200"
                                                        title="Create New Warehouse">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-800 mb-2">
                                                Storage Type <span class="text-red-500">*</span>
                                            </label>
                                            <select name="storage_type" required
                                                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200">
                                                <option value="">Select storage type</option>
                                                <option value="physical" selected>Physical Storage</option>
                                                <option value="online">Online Storage</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Capacity and Barcode -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-800 mb-2">
                                                Capacity (number of documents)
                                            </label>
                                            <input type="number" name="capacity" value="100" min="1" max="1000"
                                                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200">
                                            <p class="text-xs text-gray-600 mt-1">Maximum number of documents this box can hold</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-800 mb-2">
                                                Barcode (optional)
                                            </label>
                                            <input type="text" name="box_barcode"
                                                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                                   placeholder="Leave empty to auto-generate">
                                            <p class="text-xs text-gray-600 mt-1">Custom barcode or leave blank for auto-generation</p>
                                        </div>
                                    </div>

                                    <!-- Box Description -->
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-800 mb-2">
                                            Box Description
                                        </label>
                                        <textarea name="box_description" rows="3"
                                                  class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 resize-none"
                                                  placeholder="Optional description for this storage box (e.g., purpose, contents, special instructions)"></textarea>
                                        <p class="text-xs text-gray-600 mt-1">Describe the intended use or contents of this box</p>
                                    </div>

                                    <!-- Box Creation Tips -->
                                    <div class="mt-4 bg-white/70 border border-green-200 rounded-lg p-4">
                                        <h5 class="text-sm font-semibold text-green-800 mb-2">💡 Box Creation Tips</h5>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-green-700">
                                            <div>
                                                <strong>Naming Best Practices:</strong>
                                                <ul class="mt-1 space-y-1">
                                                    <li>• Use consistent naming patterns</li>
                                                    <li>• Include client/project codes</li>
                                                    <li>• Make names searchable</li>
                                                </ul>
                                            </div>
                                            <div>
                                                <strong>Identifier Guidelines:</strong>
                                                <ul class="mt-1 space-y-1">
                                                    <li>• Keep identifiers short but unique</li>
                                                    <li>• Use alphanumeric characters</li>
                                                    <li>• Consider sequential numbering</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bundle Assignment Options -->
                <div class="bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 rounded-2xl p-6 shadow-sm">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-purple-600 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold text-purple-900">Bundle Assignment</h4>
                            <p class="text-purple-700 text-sm">Organize documents into logical bundles <span class="text-red-500 font-semibold">*</span></p>
                        </div>
                    </div>

                    <!-- Bundle Options -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <label class="relative cursor-pointer group">
                            <input type="radio" name="bundle_option" value="existing" checked
                                   class="sr-only peer"
                                   onchange="toggleBundleOption('existing')">
                            <div class="bg-white border-2 border-gray-200 rounded-xl p-4 peer-checked:border-purple-500 peer-checked:bg-purple-50 group-hover:border-purple-300 transition-all duration-200">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-purple-500 peer-checked:bg-purple-500 mr-3 flex items-center justify-center">
                                        <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                    </div>
                                    <div>
                                        <span class="font-semibold text-gray-800 group-hover:text-purple-700">Existing Bundle</span>
                                        <p class="text-xs text-gray-600 mt-1">Add to an existing document bundle</p>
                                    </div>
                                </div>
                            </div>
                        </label>

                        <label class="relative cursor-pointer group">
                            <input type="radio" name="bundle_option" value="new"
                                   class="sr-only peer"
                                   onchange="toggleBundleOption('new')">
                            <div class="bg-white border-2 border-gray-200 rounded-xl p-4 peer-checked:border-purple-500 peer-checked:bg-purple-50 group-hover:border-purple-300 transition-all duration-200">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-purple-500 peer-checked:bg-purple-500 mr-3 flex items-center justify-center">
                                        <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                    </div>
                                    <div>
                                        <span class="font-semibold text-gray-800 group-hover:text-purple-700">New Bundle</span>
                                        <p class="text-xs text-gray-600 mt-1">Create a new document bundle</p>
                                    </div>
                                </div>
                            </div>
                        </label>
                    </div>

                    <!-- Existing Bundle Selection -->
                    <div id="existing-bundle-section" class="bg-white rounded-xl border border-gray-200 p-5 shadow-sm">
                        <label class="block text-sm font-semibold text-gray-800 mb-3">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                Select Bundle
                            </span>
                        </label>
                        <select id="bundle_id" name="bundle_id" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-white shadow-sm">
                            <option value="">Choose an existing bundle...</option>
                            <?php foreach ($bundles as $bundle): ?>
                                <option value="<?= $bundle['id'] ?>"><?= e($bundle['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="text-xs text-gray-600 mt-2">Select from available bundles in your organization</p>
                    </div>

                <!-- New Bundle Creation -->
                <div id="new-bundle-section" class="hidden">
                    <div class="bg-white rounded-xl border border-purple-200 p-6 shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold text-purple-900">Create New Bundle</h4>
                                <p class="text-purple-700 text-sm">Configure a comprehensive document bundle with all required fields</p>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <!-- Bundle Name -->
                            <div>
                                <label for="new_bundle_name" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Bundle Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="new_bundle_name" name="new_bundle_name" required
                                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                                       placeholder="e.g., Q1 2024 Financial Documents">
                                <p class="text-xs text-gray-600 mt-1">Choose a descriptive name for easy identification</p>
                            </div>

                            <!-- Department -->
                            <div>
                                <label for="new_bundle_department" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Department
                                </label>
                                <select id="new_bundle_department" name="new_bundle_department"
                                        class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="">Select department</option>
                                    <option value="HR">HR</option>
                                    <option value="Finance">Finance</option>
                                    <option value="Legal">Legal</option>
                                    <option value="IT">IT</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Operations">Operations</option>
                                    <option value="Sales">Sales</option>
                                    <option value="General">General</option>
                                </select>
                            </div>
                        </div>

                        <!-- Bundle Description -->
                        <div class="mb-6">
                            <label for="new_bundle_description" class="block text-sm font-semibold text-gray-800 mb-2">
                                Description
                            </label>
                            <textarea id="new_bundle_description" name="new_bundle_description" rows="3"
                                      class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 resize-none"
                                      placeholder="Describe the purpose and contents of this bundle..."></textarea>
                        </div>

                        <!-- Category and Priority -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div>
                                <label for="new_bundle_category" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Category
                                </label>
                                <div class="flex gap-2">
                                    <select id="new_bundle_category" name="new_bundle_category"
                                            class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <?php foreach ($categories as $value => $label): ?>
                                            <option value="<?= e($value) ?>"><?= e($label) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <button type="button" onclick="showCreateCategoryModal()"
                                            class="px-4 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-200"
                                            title="Create New Category">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label for="new_bundle_priority" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Priority
                                </label>
                                <select id="new_bundle_priority" name="new_bundle_priority"
                                        class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>

                        <!-- Document Type and Year -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div>
                                <label for="new_bundle_document_type" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Document Type
                                </label>
                                <div class="flex gap-2">
                                    <select id="new_bundle_document_type" name="new_bundle_document_type"
                                            class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                        <option value="">Select document type</option>
                                        <option value="contract">Contract</option>
                                        <option value="invoice">Invoice</option>
                                        <option value="receipt">Receipt</option>
                                        <option value="report">Report</option>
                                        <option value="correspondence">Correspondence</option>
                                        <option value="legal">Legal Document</option>
                                        <option value="financial">Financial Document</option>
                                        <option value="hr">HR Document</option>
                                        <option value="technical">Technical Document</option>
                                        <option value="other">Other</option>
                                        <?php foreach ($documentTypes as $type): ?>
                                            <option value="<?= e(strtolower(str_replace(' ', '_', $type))) ?>"><?= e($type) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <button type="button" onclick="showCreateDocumentTypeModal()"
                                            class="px-4 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-200"
                                            title="Create New Document Type">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label for="new_bundle_year" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Year
                                </label>
                                <input type="number" id="new_bundle_year" name="new_bundle_year"
                                       min="1900" max="2100" value="<?= date('Y') ?>"
                                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                                       placeholder="<?= date('Y') ?>">
                            </div>
                        </div>

                        <!-- Pages/Volume and Confidentiality -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div>
                                <label for="new_bundle_pages_volume" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Pages/Volume
                                </label>
                                <input type="number" id="new_bundle_pages_volume" name="new_bundle_pages_volume"
                                       min="1" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                                       placeholder="Number of pages or volume">
                                <p class="text-xs text-gray-600 mt-1">Estimated number of pages or documents</p>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">
                                    Confidentiality
                                </label>
                                <div class="flex items-center space-x-3 mt-3">
                                    <input type="checkbox" id="new_bundle_confidentiality_flag" name="new_bundle_confidentiality_flag" value="1"
                                           class="w-5 h-5 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                    <label for="new_bundle_confidentiality_flag" class="text-sm text-gray-700">
                                        Mark as confidential
                                    </label>
                                </div>
                                <p class="text-xs text-gray-600 mt-1">Check if bundle contains sensitive information</p>
                            </div>
                        </div>

                        <!-- Retention and Access -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div>
                                <label for="new_bundle_retention_period" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Retention Period (years)
                                </label>
                                <input type="number" id="new_bundle_retention_period" name="new_bundle_retention_period"
                                       min="1" value="7" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                                       placeholder="7">
                                <p class="text-xs text-gray-600 mt-1">How long to retain these documents</p>
                            </div>
                            <div>
                                <label for="new_bundle_access_level" class="block text-sm font-semibold text-gray-800 mb-2">
                                    Access Level
                                </label>
                                <select id="new_bundle_access_level" name="new_bundle_access_level"
                                        class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200">
                                    <option value="public">Public</option>
                                    <option value="private" selected>Private</option>
                                    <option value="restricted">Restricted</option>
                                </select>
                                <p class="text-xs text-gray-600 mt-1">Who can access this bundle</p>
                            </div>
                        </div>

                        <!-- Contents Summary -->
                        <div>
                            <label for="new_bundle_contents_summary" class="block text-sm font-semibold text-gray-800 mb-2">
                                Contents Summary
                            </label>
                            <textarea id="new_bundle_contents_summary" name="new_bundle_contents_summary" rows="3"
                                      class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 resize-none"
                                      placeholder="Brief summary of bundle contents for easy searching and identification..."></textarea>
                            <p class="text-xs text-gray-600 mt-1">Detailed description of what's included in this bundle</p>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Storage Location Assignment -->
                <div class="bg-gradient-to-br from-orange-50 to-amber-50 border border-orange-200 rounded-2xl p-6 shadow-sm">
                    <div class="flex items-center mb-6">
                        <div class="w-10 h-10 bg-orange-600 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold text-orange-900">Storage Location Assignment</h4>
                            <p class="text-orange-700 text-sm">Define the physical storage location for the box</p>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl border border-orange-200 p-5 shadow-sm">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Storage Row -->
                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">
                                    Storage Row
                                </label>
                                <div class="flex gap-2">
                                    <select name="storage_row"
                                            class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200">
                                        <option value="">Select row</option>
                                        <option value="R1">Row 1</option>
                                        <option value="R2">Row 2</option>
                                        <option value="R3">Row 3</option>
                                        <option value="R4">Row 4</option>
                                        <option value="R5">Row 5</option>
                                    </select>
                                    <button type="button" onclick="showCreateStorageRowModal()"
                                            class="px-4 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-200"
                                            title="Create Custom Storage Row">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Storage Shelf -->
                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">
                                    Shelf Number
                                </label>
                                <div class="flex gap-2">
                                    <select name="storage_shelf"
                                            class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200">
                                        <option value="">Select shelf</option>
                                        <option value="S1">Shelf 1</option>
                                        <option value="S2">Shelf 2</option>
                                        <option value="S3">Shelf 3</option>
                                        <option value="S4">Shelf 4</option>
                                        <option value="S5">Shelf 5</option>
                                    </select>
                                    <button type="button" onclick="showCreateStorageShelfModal()"
                                            class="px-4 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-200"
                                            title="Create Custom Storage Shelf">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Storage Position -->
                            <div>
                                <label class="block text-sm font-semibold text-gray-800 mb-2">
                                    Position
                                </label>
                                <input type="text" name="storage_position"
                                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
                                       placeholder="e.g., B03, P001">
                                <p class="text-xs text-gray-600 mt-1">Specific position on the shelf</p>
                            </div>
                        </div>

                        <!-- Generated Storage Code Preview -->
                        <div class="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h5 class="text-sm font-semibold text-orange-800">Generated Storage Code:</h5>
                                    <p class="text-lg font-mono text-orange-900" id="storage-code-preview">WH-[ROW]-[SHELF]-[POSITION]</p>
                                </div>
                                <div class="text-xs text-orange-700">
                                    <strong>Example:</strong> WH-R1-S2-B03<br>
                                    <span class="text-orange-600">Warehouse Row 1, Shelf 2, Box 3</span>
                                </div>
                            </div>
                        </div>

                        <!-- Storage Instructions -->
                        <div class="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h5 class="text-sm font-semibold text-blue-800 mb-2">📋 Storage Instructions</h5>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-blue-700">
                                <div>
                                    <strong>Physical Storage Process:</strong>
                                    <ul class="mt-1 space-y-1">
                                        <li>• Print box label with storage code</li>
                                        <li>• Place documents in designated box</li>
                                        <li>• Transport to assigned location</li>
                                        <li>• Update storage status to "Stored"</li>
                                    </ul>
                                </div>
                                <div>
                                    <strong>Location Format:</strong>
                                    <ul class="mt-1 space-y-1">
                                        <li>• WH = Warehouse identifier</li>
                                        <li>• R# = Row number (1-5)</li>
                                        <li>• S# = Shelf number (1-5)</li>
                                        <li>• Position = Specific box location</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Processing Notes -->
                <div class="bg-gradient-to-br from-gray-50 to-slate-50 border border-gray-200 rounded-2xl p-6 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-md font-bold text-gray-900">Processing Notes</h4>
                            <p class="text-gray-600 text-sm">Add any additional notes or instructions</p>
                        </div>
                    </div>
                    <textarea id="processing_notes" name="processing_notes" rows="4"
                              class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white shadow-sm resize-none"
                              placeholder="Enter any special instructions, observations, or notes about this intake processing..."></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="text-sm text-gray-600">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            All fields marked with <span class="text-red-500 font-semibold">*</span> are required
                        </span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button type="button" onclick="hideProcessModal()"
                                class="px-6 py-3 bg-white border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 shadow-sm">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            Start Processing
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Complete Processing Modal -->
<?php if ($intake['status'] === 'processing'): ?>
<div id="completeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-8 max-w-lg mx-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Complete Processing</h3>
        <p class="text-gray-600 mb-6">
            Are you sure you want to mark this intake as completed? This will finalize the processing and move the intake to completed status.
        </p>

        <form action="<?= url('/app/intake/' . $intake['id'] . '/complete') ?>" method="POST">
            <!-- Completion Notes -->
            <div class="mb-6">
                <label for="completion_notes" class="block text-sm font-medium text-gray-700 mb-2">
                    Completion Notes
                </label>
                <textarea id="completion_notes" name="completion_notes" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="Optional notes about the completion..."></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4">
                <button type="button" onclick="hideCompleteModal()"
                        class="px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors">
                    Complete Processing
                </button>
            </div>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- Create Warehouse Modal -->
<div id="createWarehouseModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Header -->
        <div class="bg-gradient-to-r from-green-600 to-emerald-600 p-6 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white">Create New Warehouse</h3>
                        <p class="text-green-100 text-sm">Add a new storage facility</p>
                    </div>
                </div>
                <button type="button" onclick="closeWarehouseModal()"
                        class="w-8 h-8 rounded-lg bg-white bg-opacity-20 hover:bg-opacity-30 flex items-center justify-center transition-all duration-200">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Form Content -->
        <div class="p-6">
            <form id="warehouseForm">
                <!-- Basic Information -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Basic Information
                    </h4>

                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Warehouse Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="name" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                   placeholder="e.g., Main Storage Facility">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea name="description" rows="2"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors resize-none"
                                      placeholder="Brief description of the warehouse"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Location Information -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Location Information
                    </h4>

                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Street Address <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="address" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                   placeholder="123 Industrial Blvd, Suite 100">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    City <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="city" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                       placeholder="City">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    State <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="state" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                       placeholder="State">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    ZIP Code <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="zip_code" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                       placeholder="12345">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                            <div class="flex gap-2">
                                <select name="country" id="warehouseCountrySelect"
                                        class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    <option value="USA" selected>United States</option>
                                    <option value="Canada">Canada</option>
                                    <option value="Mexico">Mexico</option>
                                    <option value="UK">United Kingdom</option>
                                    <option value="Germany">Germany</option>
                                    <option value="France">France</option>
                                    <option value="Australia">Australia</option>
                                    <option value="Japan">Japan</option>
                                    <option value="Brazil">Brazil</option>
                                    <option value="India">India</option>
                                    <option value="China">China</option>
                                </select>
                                <button type="button" onclick="addCustomCountry()"
                                        class="px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                                        title="Add Custom Country">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact & Capacity Information -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Contact & Capacity
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" name="phone"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                   placeholder="(*************">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" name="email"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Storage Capacity (boxes)</label>
                        <input type="number" name="capacity" min="1" value="1000"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                               placeholder="1000">
                        <p class="text-xs text-gray-600 mt-1">Maximum number of storage boxes</p>
                    </div>
                </div>
            </form>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 rounded-b-2xl flex justify-between items-center">
            <div class="text-sm text-gray-600">
                Fields marked with <span class="text-red-500">*</span> are required
            </div>
            <div class="flex space-x-3">
                <button type="button" onclick="closeWarehouseModal()"
                        class="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="button" onclick="submitWarehouseForm()" id="submitWarehouseBtn"
                        class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <span class="submit-text">Create Warehouse</span>
                    <span class="loading-text hidden">Creating...</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create Storage Row Modal -->
<div id="createStorageRowModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-6 max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Create Custom Storage Row</h3>
        <form id="createStorageRowForm">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Row Code *</label>
                <input type="text" name="row_code" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="e.g., R6, R7, RA, RB"
                       pattern="[A-Z0-9]+"
                       title="Use uppercase letters and numbers only">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Row Name *</label>
                <input type="text" name="row_name" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="e.g., Row 6, Row A, Special Row">
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="hideCreateStorageRowModal()"
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                    Cancel
                </button>
                <button type="button" onclick="createStorageRow()"
                        class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700">
                    Create Row
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Create Storage Shelf Modal -->
<div id="createStorageShelfModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-6 max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Create Custom Storage Shelf</h3>
        <form id="createStorageShelfForm">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Shelf Code *</label>
                <input type="text" name="shelf_code" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="e.g., S6, S7, SA, SB"
                       pattern="[A-Z0-9]+"
                       title="Use uppercase letters and numbers only">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Shelf Name *</label>
                <input type="text" name="shelf_name" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="e.g., Shelf 6, Shelf A, Special Shelf">
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="hideCreateStorageShelfModal()"
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                    Cancel
                </button>
                <button type="button" onclick="createStorageShelf()"
                        class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700">
                    Create Shelf
                </button>
            </div>
        </form>
    </div>
</div>



<!-- Create Category Modal -->
<div id="createCategoryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-6 max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Create New Category</h3>
        <form id="createCategoryForm">
            <div class="mb-4">
                <label for="category_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Name <span class="text-red-500">*</span>
                </label>
                <input type="text" id="category_name" name="name" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                       placeholder="Enter category name">
            </div>
            <div class="mb-4">
                <label for="category_description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="category_description" name="description" rows="2"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Optional description"></textarea>
            </div>
            <div class="mb-6">
                <label for="category_color" class="block text-sm font-medium text-gray-700 mb-2">
                    Color
                </label>
                <input type="color" id="category_color" name="color" value="#3B82F6"
                       class="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            <div class="flex items-center justify-end space-x-4">
                <button type="button" onclick="hideCreateCategoryModal()"
                        class="px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    Create Category
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Create Document Type Modal -->
<div id="createDocumentTypeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-6 max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Create New Document Type</h3>
        <form id="createDocumentTypeForm">
            <div class="mb-4">
                <label for="document_type_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Document Type Name <span class="text-red-500">*</span>
                </label>
                <input type="text" id="document_type_name" name="type_name" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                       placeholder="Enter document type name">
            </div>
            <div class="mb-6">
                <label for="document_type_description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="document_type_description" name="description" rows="2"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Optional description"></textarea>
            </div>
            <div class="flex items-center justify-end space-x-4">
                <button type="button" onclick="hideCreateDocumentTypeModal()"
                        class="px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    Create Document Type
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Create Sorting Category Modal -->
<div id="createSortingCategoryModal" class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm hidden items-center justify-center z-50">
    <div class="bg-white rounded-3xl shadow-2xl max-w-lg w-full mx-4">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4 text-white rounded-t-3xl">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-xl font-bold">Create New Sorting Category</h3>
                    <p class="text-blue-100 text-sm">Add a custom category for physical document sorting</p>
                </div>
                <button type="button" onclick="hideCreateSortingCategoryModal()" class="text-white hover:text-gray-200 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <form id="createSortingCategoryForm">
                <div class="space-y-4">
                    <div>
                        <label for="sorting_category_name" class="block text-sm font-semibold text-gray-800 mb-2">
                            Category Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="sorting_category_name" name="category_name" required
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="e.g., Research & Development, Customer Service">
                    </div>

                    <div>
                        <label for="sorting_category_code" class="block text-sm font-semibold text-gray-800 mb-2">
                            Category Code <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="sorting_category_code" name="category_code" required maxlength="10"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                               placeholder="e.g., RND, CS (max 10 characters)"
                               style="text-transform: uppercase;">
                    </div>

                    <div>
                        <label for="sorting_category_description" class="block text-sm font-semibold text-gray-800 mb-2">
                            Description
                        </label>
                        <textarea id="sorting_category_description" name="description" rows="3"
                                  class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none"
                                  placeholder="Describe what types of documents belong in this category..."></textarea>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="sorting_category_color" class="block text-sm font-semibold text-gray-800 mb-2">
                                Color
                            </label>
                            <input type="color" id="sorting_category_color" name="color_code" value="#3B82F6"
                                   class="w-full h-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                        </div>
                        <div>
                            <label for="sorting_category_emoji" class="block text-sm font-semibold text-gray-800 mb-2">
                                Emoji (Optional)
                            </label>
                            <input type="text" id="sorting_category_emoji" name="emoji" maxlength="2"
                                   class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-center text-xl"
                                   placeholder="📋">
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex items-center justify-end space-x-4 mt-6 pt-4 border-t border-gray-200">
                    <button type="button" onclick="hideCreateSortingCategoryModal()"
                            class="px-6 py-3 bg-white border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Create Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Enhanced radio button styling */
.peer:checked ~ div .w-5.h-5 {
    border-color: currentColor;
    background-color: currentColor;
}

.peer:checked ~ div .w-2.h-2 {
    opacity: 1;
}

/* Smooth transitions for all interactive elements */
.transition-all {
    transition: all 0.2s ease-in-out;
}

/* Enhanced focus states */
.focus\:ring-2:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Custom scrollbar for modal */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Enhanced modal backdrop */
.backdrop-blur-sm {
    backdrop-filter: blur(4px);
}

/* Gradient text effects */
.bg-gradient-to-r {
    background: linear-gradient(to right, var(--tw-gradient-stops));
}

/* Enhanced shadow effects */
.shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
</style>

<script>
function showProcessModal() {
    document.getElementById('processModal').classList.remove('hidden');
    document.getElementById('processModal').classList.add('flex');
    // Reset form to default state
    resetProcessForm();
}

function hideProcessModal() {
    document.getElementById('processModal').classList.add('hidden');
    document.getElementById('processModal').classList.remove('flex');

    // Reset form when closing
    resetProcessForm();
}

function showCompleteModal() {
    document.getElementById('completeModal').classList.remove('hidden');
    document.getElementById('completeModal').classList.add('flex');
    document.getElementById('completion_notes').focus();
}

function hideCompleteModal() {
    document.getElementById('completeModal').classList.add('hidden');
    document.getElementById('completeModal').classList.remove('flex');
    document.getElementById('completion_notes').value = '';
}

function resetProcessForm() {
    // Reset physical document processing
    document.querySelector('select[name="sorting_category"]').value = '';
    document.querySelector('input[name="digitization_required"]').checked = false;
    document.querySelector('input[name="generate_barcode"]').checked = true;

    // Reset box assignment
    document.querySelector('input[name="assign_box"][value="no"]').checked = true;
    toggleBoxAssignment('no');

    // Reset bundle options
    document.querySelector('input[name="bundle_option"][value="existing"]').checked = true;
    toggleBundleOption('existing');

    // Clear form fields
    document.getElementById('bundle_id').value = '';
    document.getElementById('processing_notes').value = '';

    // Clear new bundle fields
    if (document.getElementById('new_bundle_name')) {
        document.getElementById('new_bundle_name').value = '';
        document.getElementById('new_bundle_description').value = '';
        document.getElementById('new_bundle_category').value = 'general';
        document.getElementById('new_bundle_priority').value = 'medium';
        document.getElementById('new_bundle_document_type').value = '';
        document.getElementById('new_bundle_year').value = '<?= date('Y') ?>';
    }

    // Clear box fields
    if (document.querySelector('select[name="box_id"]')) {
        document.querySelector('select[name="box_id"]').value = '';
    }

    // Clear new box creation fields
    const boxFields = [
        'input[name="box_name"]',
        'input[name="box_identifier"]',
        'select[name="warehouse_id"]',
        'select[name="storage_type"]',
        'input[name="capacity"]',
        'input[name="box_barcode"]',
        'textarea[name="box_description"]'
    ];

    boxFields.forEach(selector => {
        const field = document.querySelector(selector);
        if (field) {
            if (field.type === 'number' && field.name === 'capacity') {
                field.value = '100';
            } else if (field.tagName === 'SELECT') {
                field.selectedIndex = 0;
            } else {
                field.value = '';
            }
        }
    });
}

function toggleBundleOption(option) {
    const existingSection = document.getElementById('existing-bundle-section');
    const newSection = document.getElementById('new-bundle-section');
    const bundleSelect = document.getElementById('bundle_id');
    const newBundleName = document.getElementById('new_bundle_name');

    if (option === 'existing') {
        existingSection.classList.remove('hidden');
        newSection.classList.add('hidden');

        // Set validation
        bundleSelect.required = true;
        newBundleName.required = false;

        // Clear new bundle fields
        newBundleName.value = '';
        document.getElementById('new_bundle_description').value = '';
    } else {
        existingSection.classList.add('hidden');
        newSection.classList.remove('hidden');

        // Set validation
        bundleSelect.required = false;
        newBundleName.required = true;

        // Clear existing bundle selection
        bundleSelect.value = '';
    }
}

// Auto-suggest bundle name based on intake information
function suggestBundleName() {
    const category = document.getElementById('new_bundle_category').value;
    const nameInput = document.getElementById('new_bundle_name');

    if (!nameInput.value) {
        const currentDate = new Date();
        const quarter = Math.ceil((currentDate.getMonth() + 1) / 3);
        const year = currentDate.getFullYear();

        let suggestedName = '';

        switch(category) {
            case 'financial':
                suggestedName = `Q${quarter} ${year} Financial Documents`;
                break;
            case 'legal':
                suggestedName = `${year} Legal Documents`;
                break;
            case 'hr':
                suggestedName = `${year} HR Documents`;
                break;
            case 'contracts':
                suggestedName = `${year} Contracts`;
                break;
            case 'invoices':
                suggestedName = `${currentDate.toLocaleString('default', { month: 'long' })} ${year} Invoices`;
                break;
            default:
                suggestedName = `${category.charAt(0).toUpperCase() + category.slice(1)} Documents`;
        }

        nameInput.value = suggestedName;
    }
}

// Add event listener for category change
document.getElementById('new_bundle_category')?.addEventListener('change', suggestBundleName);

// Form validation before submit
document.querySelector('#processModal form')?.addEventListener('submit', function(e) {
    const bundleOption = document.querySelector('input[name="bundle_option"]:checked').value;

    if (bundleOption === 'existing') {
        const bundleId = document.getElementById('bundle_id').value;
        if (!bundleId) {
            e.preventDefault();
            alert('Please select a bundle or choose to create a new one.');
            return false;
        }
    } else {
        const bundleName = document.getElementById('new_bundle_name').value.trim();
        if (!bundleName) {
            e.preventDefault();
            alert('Please enter a name for the new bundle.');
            return false;
        }
    }
});

// Enhanced Workflow Functions
function toggleBoxAssignment(option) {
    const boxSection = document.getElementById('box-assignment-section');

    if (!boxSection) {
        console.warn('Box assignment section not found');
        return;
    }

    if (option === 'yes') {
        boxSection.classList.remove('hidden');
        // Default to existing box option
        const existingBoxOption = document.querySelector('input[name="box_option"][value="existing"]');
        if (existingBoxOption) {
            existingBoxOption.checked = true;
            toggleBoxOption('existing');
        }
    } else {
        boxSection.classList.add('hidden');
        // Clear box selections
        const boxIdInput = document.querySelector('input[name="box_id"]');
        if (boxIdInput) {
            boxIdInput.value = '';
        }
        const boxIdSelect = document.querySelector('select[name="box_id"]');
        if (boxIdSelect) {
            boxIdSelect.value = '';
        }
    }
}

function toggleBoxOption(option) {
    const existingBoxSection = document.getElementById('existing-box-section');
    const newBoxSection = document.getElementById('new-box-section');

    if (!existingBoxSection || !newBoxSection) {
        console.warn('Box option sections not found');
        return;
    }

    if (option === 'existing') {
        existingBoxSection.classList.remove('hidden');
        newBoxSection.classList.add('hidden');
        loadAvailableBoxes();
    } else {
        existingBoxSection.classList.add('hidden');
        newBoxSection.classList.remove('hidden');
    }
}

async function loadAvailableBoxes() {
    try {
        const response = await fetch('<?= url('/app/boxes/available') ?>', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success) {
            const boxSelect = document.querySelector('select[name="box_id"]');
            boxSelect.innerHTML = '<option value="">Select a box</option>';

            result.boxes.forEach(box => {
                const option = document.createElement('option');
                option.value = box.id;
                option.textContent = `${box.box_id} - ${box.name} (${box.available_capacity}% available)`;
                boxSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load boxes:', error);
        showNotification('Failed to load available boxes', 'error');
    }
}

// Enhanced form validation
function validateEnhancedForm() {
    const sortingCategory = document.querySelector('select[name="sorting_category"]').value;
    const assignBox = document.querySelector('input[name="assign_box"]:checked').value;

    if (!sortingCategory) {
        alert('Please select a sorting category for physical documents.');
        return false;
    }

    if (assignBox === 'yes') {
        const boxOption = document.querySelector('input[name="box_option"]:checked');
        if (!boxOption) {
            alert('Please select a box option.');
            return false;
        }

        if (boxOption.value === 'existing') {
            const boxId = document.querySelector('select[name="box_id"]').value;
            if (!boxId) {
                alert('Please select a box.');
                return false;
            }
        } else if (boxOption.value === 'new') {
            // Validate new box fields
            const boxName = document.querySelector('input[name="box_name"]').value.trim();
            const boxIdentifier = document.querySelector('input[name="box_identifier"]').value.trim();
            const warehouseId = document.querySelector('select[name="warehouse_id"]').value;
            const storageType = document.querySelector('select[name="storage_type"]').value;

            if (!boxName) {
                // Auto-sync box name with identifier if identifier exists
                const currentBoxIdentifier = document.querySelector('input[name="box_identifier"]').value.trim();
                if (currentBoxIdentifier) {
                    syncBoxNameWithIdentifier();
                    const newBoxName = document.querySelector('input[name="box_name"]').value.trim();
                    if (!newBoxName) {
                        alert('Please enter a box name.');
                        return false;
                    }
                } else {
                    alert('Please enter a box name.');
                    return false;
                }
            }

            if (!boxIdentifier) {
                // Auto-generate box identifier if empty
                autoGenerateBoxIdentifier();
                const newBoxIdentifier = document.querySelector('input[name="box_identifier"]').value;
                if (!newBoxIdentifier) {
                    alert('Please enter a box identifier or ensure auto-generation works.');
                    return false;
                }
            }

            if (!warehouseId) {
                alert('Please select a warehouse for the new box.');
                return false;
            }

            if (!storageType) {
                alert('Please select a storage type for the new box.');
                return false;
            }
        }
    }

    return true;
}

// Storage code preview update
function updateStorageCodePreview() {
    const row = document.querySelector('select[name="storage_row"]').value;
    const shelf = document.querySelector('select[name="storage_shelf"]').value;
    const position = document.querySelector('input[name="storage_position"]').value;
    const preview = document.getElementById('storage-code-preview');

    if (row && shelf && position) {
        preview.textContent = `WH-${row}-${shelf}-${position.toUpperCase()}`;
        preview.classList.remove('text-orange-900');
        preview.classList.add('text-green-700', 'font-bold');
    } else {
        preview.textContent = 'WH-[ROW]-[SHELF]-[POSITION]';
        preview.classList.remove('text-green-700', 'font-bold');
        preview.classList.add('text-orange-900');
    }
}

// Add event listeners for storage code preview
document.querySelector('select[name="storage_row"]')?.addEventListener('change', updateStorageCodePreview);
document.querySelector('select[name="storage_shelf"]')?.addEventListener('change', updateStorageCodePreview);
document.querySelector('input[name="storage_position"]')?.addEventListener('input', updateStorageCodePreview);

// Update form validation to include enhanced checks
document.querySelector('#processModal form')?.addEventListener('submit', function(e) {
    // Enhanced validation first
    if (!validateEnhancedForm()) {
        e.preventDefault();
        return false;
    }

    // Original bundle validation
    const bundleOption = document.querySelector('input[name="bundle_option"]:checked').value;

    if (bundleOption === 'existing') {
        const bundleId = document.getElementById('bundle_id').value;
        if (!bundleId) {
            e.preventDefault();
            alert('Please select a bundle or choose to create a new one.');
            return false;
        }
    } else {
        const bundleName = document.getElementById('new_bundle_name').value.trim();
        if (!bundleName) {
            e.preventDefault();
            alert('Please enter a name for the new bundle.');
            return false;
        }
    }
});

// Close modal when clicking outside
document.getElementById('processModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        hideProcessModal();
    }
});

// Category Modal Functions
function showCreateCategoryModal() {
    document.getElementById('createCategoryModal').classList.remove('hidden');
    document.getElementById('createCategoryModal').classList.add('flex');
    document.getElementById('category_name').focus();
}

function hideCreateCategoryModal() {
    document.getElementById('createCategoryModal').classList.add('hidden');
    document.getElementById('createCategoryModal').classList.remove('flex');
    document.getElementById('createCategoryForm').reset();
}

// Document Type Modal Functions
function showCreateDocumentTypeModal() {
    document.getElementById('createDocumentTypeModal').classList.remove('hidden');
    document.getElementById('createDocumentTypeModal').classList.add('flex');
    document.getElementById('document_type_name').focus();
}

function hideCreateDocumentTypeModal() {
    document.getElementById('createDocumentTypeModal').classList.add('hidden');
    document.getElementById('createDocumentTypeModal').classList.remove('flex');
    document.getElementById('createDocumentTypeForm').reset();
}

// Sorting Category Modal Functions
function showCreateSortingCategoryModal() {
    document.getElementById('createSortingCategoryModal').classList.remove('hidden');
    document.getElementById('createSortingCategoryModal').classList.add('flex');
    document.getElementById('sorting_category_name').focus();
}

function hideCreateSortingCategoryModal() {
    document.getElementById('createSortingCategoryModal').classList.add('hidden');
    document.getElementById('createSortingCategoryModal').classList.remove('flex');
    document.getElementById('createSortingCategoryForm').reset();
}

// Auto-generate category code from name
document.getElementById('sorting_category_name')?.addEventListener('input', function() {
    const name = this.value;
    const codeInput = document.getElementById('sorting_category_code');

    if (name && !codeInput.value) {
        // Generate code from first letters of words
        const words = name.split(' ');
        let code = '';

        if (words.length === 1) {
            code = words[0].substring(0, 3).toUpperCase();
        } else {
            code = words.map(word => word.charAt(0)).join('').substring(0, 5).toUpperCase();
        }

        codeInput.value = code;
    }
});

// Handle Category Form Submission
document.getElementById('createCategoryForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;

    try {
        submitButton.textContent = 'Creating...';
        submitButton.disabled = true;

        const response = await fetch('<?= url('/app/categories') ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success) {
            // Add new category to dropdown
            const categorySelect = document.getElementById('new_bundle_category');
            const newOption = document.createElement('option');
            newOption.value = result.category.name.toLowerCase().replace(/\s+/g, '_');
            newOption.textContent = result.category.name;
            newOption.selected = true;
            categorySelect.appendChild(newOption);

            // Trigger bundle name suggestion
            suggestBundleName();

            // Show success message
            showNotification('Category created successfully!', 'success');

            // Close modal
            hideCreateCategoryModal();
        } else {
            throw new Error(result.message || 'Failed to create category');
        }
    } catch (error) {
        showNotification('Error: ' + error.message, 'error');
    } finally {
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
});

// Handle Document Type Form Submission
document.getElementById('createDocumentTypeForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;

    try {
        submitButton.textContent = 'Creating...';
        submitButton.disabled = true;

        const response = await fetch('<?= url('/app/intake/document-types') ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success) {
            // Add new document type to dropdown
            const documentTypeSelect = document.getElementById('new_bundle_document_type');
            const newOption = document.createElement('option');
            newOption.value = result.document_type.type_name.toLowerCase().replace(/\s+/g, '_');
            newOption.textContent = result.document_type.type_name;
            newOption.selected = true;
            documentTypeSelect.appendChild(newOption);

            // Show success message
            showNotification('Document type created successfully!', 'success');

            // Close modal
            hideCreateDocumentTypeModal();
        } else {
            throw new Error(result.message || 'Failed to create document type');
        }
    } catch (error) {
        showNotification('Error: ' + error.message, 'error');
    } finally {
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
});

// Handle Sorting Category Form Submission
document.getElementById('createSortingCategoryForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;

    try {
        submitButton.textContent = 'Creating...';
        submitButton.disabled = true;

        const response = await fetch('<?= url('/app/intake/sorting-categories') ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success) {
            // Add new sorting category to dropdown
            const sortingCategorySelect = document.getElementById('sorting_category');
            const newOption = document.createElement('option');
            const emoji = result.category.emoji || '';
            newOption.value = result.category.category_code;
            newOption.textContent = `${emoji} ${result.category.category_name}`;
            newOption.selected = true;
            sortingCategorySelect.appendChild(newOption);

            // Show success message
            showNotification('Sorting category created successfully!', 'success');

            // Close modal
            hideCreateSortingCategoryModal();
        } else {
            throw new Error(result.message || 'Failed to create sorting category');
        }
    } catch (error) {
        showNotification('Error: ' + error.message, 'error');
    } finally {
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
});

// Notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Close modals when clicking outside
document.getElementById('createCategoryModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        hideCreateCategoryModal();
    }
});

document.getElementById('createDocumentTypeModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        hideCreateDocumentTypeModal();
    }
});

// Close sorting category modal when clicking outside
document.getElementById('createSortingCategoryModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        hideCreateSortingCategoryModal();
    }
});

// Close complete modal when clicking outside
document.getElementById('completeModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        hideCompleteModal();
    }
});

// Close storage row modal when clicking outside
document.getElementById('createStorageRowModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        hideCreateStorageRowModal();
    }
});

// Close storage shelf modal when clicking outside
document.getElementById('createStorageShelfModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        hideCreateStorageShelfModal();
    }
});

// Auto-sync Box Name when Box Identifier changes (optional real-time sync)
document.querySelector('input[name="box_identifier"]')?.addEventListener('blur', function() {
    const boxIdentifier = this.value.trim();
    const boxName = document.querySelector('input[name="box_name"]').value.trim();

    // Only auto-sync if Box Name is empty or follows the pattern "Storage Box [identifier]"
    if (boxIdentifier && (!boxName || boxName.startsWith('Storage Box '))) {
        const newBoxName = `Storage Box ${boxIdentifier}`;
        document.querySelector('input[name="box_name"]').value = newBoxName;
    }
});

// Auto-generate Box Identifier function
function autoGenerateBoxIdentifier() {
    const clientName = document.querySelector('input[name="client_name"]')?.value || 'CLIENT';
    const timestamp = Date.now().toString().slice(-6);
    const clientCode = clientName.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 6) || 'CLIENT';
    const boxIdentifier = `${clientCode}-BOX${timestamp}`;

    // Set the Box Identifier
    document.querySelector('input[name="box_identifier"]').value = boxIdentifier;

    // Auto-generate matching Box Name
    const boxName = `Storage Box ${boxIdentifier}`;
    document.querySelector('input[name="box_name"]').value = boxName;

    // Show notification
    showNotification('Box Identifier and Name auto-generated successfully!', 'success');
}

// Sync Box Name with current Box Identifier
function syncBoxNameWithIdentifier() {
    const boxIdentifier = document.querySelector('input[name="box_identifier"]').value.trim();

    if (!boxIdentifier) {
        showNotification('Please enter a Box Identifier first', 'error');
        return;
    }

    // Generate Box Name based on current identifier
    const boxName = `Storage Box ${boxIdentifier}`;
    document.querySelector('input[name="box_name"]').value = boxName;

    showNotification('Box Name synced with identifier!', 'success');
}

// Warehouse Modal Functions
function showCreateWarehouseModal() {
    const modal = document.getElementById('createWarehouseModal');
    if (modal) {
        modal.classList.remove('hidden');
        // Reset form
        const form = document.getElementById('warehouseForm');
        if (form) {
            form.reset();
        }
    }
}

function closeWarehouseModal() {
    const modal = document.getElementById('createWarehouseModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function submitWarehouseForm() {
    const form = document.getElementById('warehouseForm');
    if (!form) {
        console.error('Warehouse form not found');
        return;
    }

    // Get form data
    const formData = new FormData(form);

    // Validate required fields
    const name = formData.get('name')?.trim();
    const address = formData.get('address')?.trim();
    const city = formData.get('city')?.trim();
    const state = formData.get('state')?.trim();
    const zipCode = formData.get('zip_code')?.trim();

    if (!name || !address || !city || !state || !zipCode) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    // Show loading state
    const submitBtn = document.getElementById('submitWarehouseBtn');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');

    if (submitText && loadingText) {
        submitText.classList.add('hidden');
        loadingText.classList.remove('hidden');
    }
    submitBtn.disabled = true;

    // Make the request
    const baseUrl = '<?= rtrim(url(''), '/') ?>';
    const requestUrl = baseUrl + '/app/warehouse-ajax-create';

    console.log('Making request to:', requestUrl);

    fetch(requestUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);

        if (data.success && data.warehouse) {
            // Add to warehouse dropdown
            const warehouseSelect = document.querySelector('select[name="warehouse_id"]');
            if (warehouseSelect) {
                const option = document.createElement('option');
                option.value = data.warehouse.id;
                option.textContent = `${data.warehouse.name} - ${data.warehouse.city}, ${data.warehouse.state}`;
                warehouseSelect.appendChild(option);
                warehouseSelect.value = data.warehouse.id;
            }

            closeWarehouseModal();
            showNotification('Warehouse created successfully!', 'success');
        } else {
            throw new Error(data.message || 'Failed to create warehouse');
        }
    })
    .catch(error => {
        console.error('Error creating warehouse:', error);
        showNotification('Error: ' + error.message, 'error');
    })
    .finally(() => {
        // Reset button state
        if (submitText && loadingText) {
            submitText.classList.remove('hidden');
            loadingText.classList.add('hidden');
        }
        submitBtn.disabled = false;
    });
}

// Storage Row creation functions
function showCreateStorageRowModal() {
    document.getElementById('createStorageRowModal').classList.remove('hidden');
}

function hideCreateStorageRowModal() {
    document.getElementById('createStorageRowModal').classList.add('hidden');
    document.getElementById('createStorageRowForm').reset();
}

function createStorageRow() {
    const form = document.getElementById('createStorageRowForm');
    const formData = new FormData(form);

    const rowCode = formData.get('row_code');
    const rowName = formData.get('row_name');

    if (!rowCode || !rowName) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    // Add new row to dropdown
    const rowSelect = document.querySelector('select[name="storage_row"]');
    const option = document.createElement('option');
    option.value = rowCode;
    option.textContent = rowName;
    rowSelect.appendChild(option);
    rowSelect.value = rowCode;

    hideCreateStorageRowModal();
    showNotification('Storage row created successfully!', 'success');
}

// Storage Shelf creation functions
function showCreateStorageShelfModal() {
    document.getElementById('createStorageShelfModal').classList.remove('hidden');
}

function hideCreateStorageShelfModal() {
    document.getElementById('createStorageShelfModal').classList.add('hidden');
    document.getElementById('createStorageShelfForm').reset();
}

function createStorageShelf() {
    const form = document.getElementById('createStorageShelfForm');
    const formData = new FormData(form);

    const shelfCode = formData.get('shelf_code');
    const shelfName = formData.get('shelf_name');

    if (!shelfCode || !shelfName) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    // Add new shelf to dropdown
    const shelfSelect = document.querySelector('select[name="storage_shelf"]');
    const option = document.createElement('option');
    option.value = shelfCode;
    option.textContent = shelfName;
    shelfSelect.appendChild(option);
    shelfSelect.value = shelfCode;

    hideCreateStorageShelfModal();
    showNotification('Storage shelf created successfully!', 'success');
}

// Custom Country Function
function addCustomCountry() {
    const countryName = prompt('Enter country name:');
    if (countryName && countryName.trim()) {
        const select = document.getElementById('warehouseCountrySelect');
        if (select) {
            const option = document.createElement('option');
            option.value = countryName.trim();
            option.textContent = countryName.trim();
            option.selected = true;
            select.appendChild(option);
            showNotification('Custom country added successfully!', 'success');
        }
    }
}

// Initialize warehouse modal
document.addEventListener('DOMContentLoaded', function() {
    // Close modal when clicking outside
    const modal = document.getElementById('createWarehouseModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeWarehouseModal();
            }
        });
    }
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
