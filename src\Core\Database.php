<?php

namespace App\Core;

use PDO;
use PDOException;

/**
 * Database Class
 * 
 * Handles database connections and operations for the Document Management System
 */
class Database
{
    private static $instance = null;
    private $connection = null;
    private $config = null;
    
    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        $configPath = defined('CONFIG_PATH') ? CONFIG_PATH : dirname(__DIR__) . '/config';
        $this->config = require $configPath . '/database.php';
        $this->connect();
    }
    
    /**
     * Get database instance (Singleton)
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Get PDO connection
     */
    public function getConnection()
    {
        return $this->connection;
    }
    
    /**
     * Connect to database
     */
    private function connect()
    {
        try {
            $defaultConnection = $this->config['default'];
            $config = $this->config['connections'][$defaultConnection];
            
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
            
            $this->connection = new PDO(
                $dsn,
                $config['username'],
                $config['password'],
                $config['options']
            );
            
            logMessage("Database connected successfully");
            
        } catch (PDOException $e) {
            logMessage("Database connection failed: " . $e->getMessage(), 'error');
            throw new \Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Execute a query and return results
     */
    public function query($sql, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            
            return $stmt;
            
        } catch (PDOException $e) {
            logMessage("Query failed: " . $e->getMessage() . " SQL: " . $sql, 'error');
            throw new \Exception("Query failed: " . $e->getMessage());
        }
    }
    
    /**
     * Fetch all results
     */
    public function fetchAll($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Fetch single result
     */
    public function fetch($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Fetch single column value
     */
    public function fetchColumn($sql, $params = [], $columnNumber = 0)
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn($columnNumber);
    }

    /**
     * Execute a query and return last insert ID for INSERT statements
     */
    public function execute($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);

        // For INSERT statements, return the last insert ID
        if (stripos(trim($sql), 'INSERT') === 0) {
            return $this->connection->lastInsertId();
        }

        // For other statements, return the number of affected rows
        return $stmt->rowCount();
    }
    
    /**
     * Insert record and return last insert ID
     */
    public function insert($table, $data)
    {
        $columns = implode(',', array_keys($data));
        $placeholders = str_repeat('?,', count($data) - 1) . '?';

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";

        $this->query($sql, array_values($data));

        return $this->connection->lastInsertId();
    }
    
    /**
     * Update records
     */
    public function update($table, $data, $where, $whereParams = [])
    {
        $setParts = [];
        $setParams = [];
        $paramIndex = 1;

        // Build SET clause with positional parameters
        foreach ($data as $key => $value) {
            $setParts[] = "{$key} = ?";
            $setParams[] = $value;
        }
        $setClause = implode(', ', $setParts);

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";

        // Merge parameters: SET values first, then WHERE values
        $params = array_merge($setParams, $whereParams);

        $stmt = $this->query($sql, $params);

        return $stmt->rowCount();
    }
    
    /**
     * Delete records
     */
    public function delete($table, $where, $params = [])
    {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        
        $stmt = $this->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction()
    {
        return $this->connection->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit()
    {
        return $this->connection->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback()
    {
        return $this->connection->rollback();
    }
    
    /**
     * Check if table exists
     */
    public function tableExists($tableName)
    {
        $sql = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
        $result = $this->fetch($sql, [$tableName]);

        return $result && $result['count'] > 0;
    }
    
    /**
     * Get table schema
     */
    public function getTableSchema($tableName)
    {
        $sql = "DESCRIBE {$tableName}";
        return $this->fetchAll($sql);
    }
    
    /**
     * Execute raw SQL (for migrations)
     */
    public function exec($sql)
    {
        try {
            return $this->connection->exec($sql);
        } catch (PDOException $e) {
            logMessage("SQL execution failed: " . $e->getMessage() . " SQL: " . $sql, 'error');
            throw new \Exception("SQL execution failed: " . $e->getMessage());
        }
    }
    
    /**
     * Get database info
     */
    public function getDatabaseInfo()
    {
        $version = $this->fetch("SELECT VERSION() as version");
        $size = $this->fetch("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ");
        
        return [
            'version' => $version['version'],
            'size_mb' => $size['size_mb'] ?? 0
        ];
    }
    
    /**
     * Prevent cloning
     */
    private function __clone() {}
    
    /**
     * Prevent unserialization
     */
    public function __wakeup() {}
}
