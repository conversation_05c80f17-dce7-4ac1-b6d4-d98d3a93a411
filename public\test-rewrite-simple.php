<?php
/**
 * Simple Rewrite Test
 */

echo "<h1>Apache Rewrite Test</h1>";

echo "<h3>Server Information</h3>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>PATH_INFO:</strong> " . ($_SERVER['PATH_INFO'] ?? 'Not set') . "</p>";
echo "<p><strong>QUERY_STRING:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'Not set') . "</p>";

echo "<h3>Apache Modules</h3>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✅ mod_rewrite is enabled</p>";
    } else {
        echo "<p style='color: red;'>❌ mod_rewrite is not enabled</p>";
    }
} else {
    echo "<p>⚠️ Cannot check Apache modules (not running under Apache or function not available)</p>";
}

echo "<h3>.htaccess Test</h3>";
$htaccessFile = __DIR__ . '/.htaccess';
if (file_exists($htaccessFile)) {
    echo "<p>✅ .htaccess file exists</p>";
    echo "<details><summary>View .htaccess content</summary>";
    echo "<pre>" . htmlspecialchars(file_get_contents($htaccessFile)) . "</pre>";
    echo "</details>";
} else {
    echo "<p>❌ .htaccess file does not exist</p>";
}

echo "<h3>Test URLs</h3>";
echo "<p>Try these URLs to test rewriting:</p>";
echo '<ul>';
echo '<li><a href="/dms/public/app/billing" target="_blank">/dms/public/app/billing (Clean URL)</a></li>';
echo '<li><a href="/dms/public/index.php/app/billing" target="_blank">/dms/public/index.php/app/billing (Explicit)</a></li>';
echo '<li><a href="/dms/app/billing" target="_blank">/dms/app/billing (Root redirect)</a></li>';
echo '</ul>';

echo "<h3>Current File Access</h3>";
echo "<p>This file was accessed via: <strong>" . $_SERVER['REQUEST_URI'] . "</strong></p>";
?>
