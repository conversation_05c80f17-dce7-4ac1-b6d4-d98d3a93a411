<?php
/**
 * Create Demo Admin User
 * 
 * This script creates a demo company and admin user for testing
 */

// Define constants
define('APP_ROOT', __DIR__);
define('CONFIG_PATH', __DIR__ . '/src/config');

require_once 'src/autoload.php';

try {
    echo "Creating demo company and admin user...\n";
    
    $db = App\Core\Database::getInstance();
    
    // Check if demo company already exists
    $existingCompany = $db->fetch(
        "SELECT id FROM companies WHERE name = 'Demo Company' LIMIT 1"
    );
    
    if ($existingCompany) {
        echo "Demo company already exists. Checking for admin user...\n";
        $companyId = $existingCompany['id'];
    } else {
        // Create demo company
        $companyId = $db->insert('companies', [
            'name' => 'Demo Company',
            'domain' => 'demo.local',
            'email' => '<EMAIL>',
            'address' => '123 Demo Street, Demo City, DC 12345',
            'phone' => '+****************',
            'subscription_plan' => 'premium',
            'storage_limit' => 50 * 1024 * 1024 * 1024, // 50GB
            'status' => 'active'
        ]);
        echo "✓ Demo company created with ID: {$companyId}\n";
    }
    
    // Check if admin user already exists
    $existingUser = $db->fetch(
        "SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1"
    );
    
    if ($existingUser) {
        echo "✓ Demo admin user already exists\n";
    } else {
        // Create demo admin user
        $userId = $db->insert('users', [
            'company_id' => $companyId,
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
            'first_name' => 'System',
            'last_name' => 'Administrator',
            'role' => 'company_admin',
            'phone' => '+****************',
            'status' => 'active',
            'email_verified' => true
        ]);
        echo "✓ Demo admin user created with ID: {$userId}\n";
    }
    
    // Create some demo categories
    $categories = [
        ['name' => 'Contracts', 'description' => 'Legal contracts and agreements', 'color' => '#3B82F6', 'icon' => 'document-text'],
        ['name' => 'Invoices', 'description' => 'Financial invoices and receipts', 'color' => '#10B981', 'icon' => 'receipt-tax'],
        ['name' => 'Reports', 'description' => 'Business reports and analytics', 'color' => '#F59E0B', 'icon' => 'chart-bar'],
        ['name' => 'HR Documents', 'description' => 'Human resources documentation', 'color' => '#EF4444', 'icon' => 'users'],
        ['name' => 'Marketing', 'description' => 'Marketing materials and campaigns', 'color' => '#8B5CF6', 'icon' => 'speakerphone']
    ];
    
    foreach ($categories as $category) {
        $existing = $db->fetch(
            "SELECT id FROM document_categories WHERE company_id = ? AND name = ?",
            [$companyId, $category['name']]
        );
        
        if (!$existing) {
            $db->insert('document_categories', [
                'company_id' => $companyId,
                'name' => $category['name'],
                'description' => $category['description'],
                'color' => $category['color'],
                'icon' => $category['icon'],
                'sort_order' => 0
            ]);
            echo "✓ Created category: {$category['name']}\n";
        }
    }
    
    // Create demo warehouse
    $existingWarehouse = $db->fetch(
        "SELECT id FROM warehouses WHERE company_id = ? AND name = 'Main Warehouse'",
        [$companyId]
    );
    
    if (!$existingWarehouse) {
        $warehouseId = $db->insert('warehouses', [
            'company_id' => $companyId,
            'name' => 'Main Warehouse',
            'code' => 'WH001',
            'description' => 'Primary document storage facility',
            'address' => '456 Storage Ave, Demo City, DC 12345',
            'total_capacity' => 10000,
            'capacity_unit' => 'boxes',
            'status' => 'active'
        ]);
        echo "✓ Created demo warehouse with ID: {$warehouseId}\n";
        
        // Create some storage locations
        $locations = [
            ['type' => 'building', 'identifier' => 'BLDG-A', 'name' => 'Building A'],
            ['type' => 'floor', 'identifier' => 'FL-01', 'name' => 'Floor 1', 'parent_type' => 'building'],
            ['type' => 'room', 'identifier' => 'RM-101', 'name' => 'Room 101', 'parent_type' => 'floor'],
            ['type' => 'aisle', 'identifier' => 'A1', 'name' => 'Aisle A1', 'parent_type' => 'room'],
            ['type' => 'rack', 'identifier' => 'R001', 'name' => 'Rack 001', 'parent_type' => 'aisle']
        ];
        
        $parentId = null;
        foreach ($locations as $location) {
            $locationId = $db->insert('storage_locations', [
                'warehouse_id' => $warehouseId,
                'parent_id' => $parentId,
                'type' => $location['type'],
                'identifier' => $location['identifier'],
                'name' => $location['name'],
                'capacity' => 100,
                'status' => 'active'
            ]);
            $parentId = $locationId;
            echo "✓ Created storage location: {$location['name']}\n";
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "✅ DEMO SETUP COMPLETE!\n";
    echo str_repeat("=", 50) . "\n";
    echo "Company: Demo Company\n";
    echo "Admin Email: <EMAIL>\n";
    echo "Admin Password: admin123\n";
    echo "Login URL: http://localhost/dms/public/login\n";
    echo str_repeat("=", 50) . "\n";
    echo "\nYou can now log in and test the system!\n";
    
} catch (Exception $e) {
    echo "✗ Error creating demo data: " . $e->getMessage() . "\n";
    echo "Make sure the database is running and migrations have been executed.\n";
    echo "Run: php scripts/migrate.php\n";
}
