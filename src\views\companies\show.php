<?php
$title = 'Company Details - ' . ($company['name'] ?? 'Unknown');
ob_start();
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900"><?= e($company['name'] ?? 'Unknown Company') ?></h1>
                <p class="text-gray-600 mt-1">Company details and management</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="<?= url('/app/companies') ?>" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Companies
                </a>
                <a href="<?= url("/app/companies/{$company['id']}/edit") ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Company
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <!-- Company Information -->
        <div class="lg:col-span-2 space-y-8">
            
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Company Name</label>
                        <p class="text-gray-900"><?= e($company['name'] ?? 'N/A') ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Domain</label>
                        <p class="text-gray-900"><?= e($company['domain'] ?? 'N/A') ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Email</label>
                        <p class="text-gray-900"><?= e($company['email'] ?? 'N/A') ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Phone</label>
                        <p class="text-gray-900"><?= e($company['phone'] ?? 'N/A') ?></p>
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-500 mb-1">Address</label>
                        <p class="text-gray-900"><?= e($company['address'] ?? 'N/A') ?></p>
                    </div>
                </div>
            </div>

            <!-- Subscription Details -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Plan</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            <?php if ($company['subscription_plan'] === 'enterprise'): ?>
                                bg-purple-100 text-purple-800
                            <?php elseif ($company['subscription_plan'] === 'premium'): ?>
                                bg-blue-100 text-blue-800
                            <?php else: ?>
                                bg-green-100 text-green-800
                            <?php endif; ?>">
                            <?= ucfirst($company['subscription_plan'] ?? 'basic') ?>
                        </span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Status</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            <?php if ($company['status'] === 'active'): ?>
                                bg-green-100 text-green-800
                            <?php elseif ($company['status'] === 'suspended'): ?>
                                bg-yellow-100 text-yellow-800
                            <?php else: ?>
                                bg-red-100 text-red-800
                            <?php endif; ?>">
                            <?= ucfirst($company['status'] ?? 'inactive') ?>
                        </span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Storage Used</label>
                        <p class="text-gray-900"><?= formatFileSize($company['storage_used'] ?? 0) ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Storage Limit</label>
                        <p class="text-gray-900"><?= formatFileSize($company['storage_limit'] ?? 0) ?></p>
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-500 mb-2">Storage Usage</label>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <?php 
                            $percentage = $company['storage_limit'] > 0 ? 
                                min(100, ($company['storage_used'] / $company['storage_limit']) * 100) : 0;
                            ?>
                            <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: <?= $percentage ?>%"></div>
                        </div>
                        <p class="text-sm text-gray-500 mt-1"><?= number_format($percentage, 1) ?>% used</p>
                    </div>
                </div>
            </div>

            <!-- Company Users -->
            <?php if (isset($companyUsers) && !empty($companyUsers)): ?>
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Users</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Role</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Last Login</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php foreach ($companyUsers as $user): ?>
                            <tr>
                                <td class="px-4 py-3">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?= e($user['first_name'] . ' ' . $user['last_name']) ?>
                                        </div>
                                        <div class="text-sm text-gray-500"><?= e($user['email']) ?></div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <?= ucfirst(str_replace('_', ' ', $user['role'])) ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        <?= $user['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                        <?= ucfirst($user['status']) ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500">
                                    <?= $user['last_login'] ? date('M j, Y g:i A', strtotime($user['last_login'])) : 'Never' ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            
            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                <div class="space-y-4">
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Total Users</span>
                        <span class="font-medium text-gray-900"><?= $companyStats['user_count'] ?? 0 ?></span>
                    </div>

                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Total Documents</span>
                        <span class="font-medium text-gray-900"><?= $companyStats['document_count'] ?? 0 ?></span>
                    </div>

                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Total Bundles</span>
                        <span class="font-medium text-gray-900"><?= $companyStats['bundle_count'] ?? 0 ?></span>
                    </div>

                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Total Boxes</span>
                        <span class="font-medium text-gray-900"><?= $companyStats['box_count'] ?? 0 ?></span>
                    </div>

                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Created</span>
                        <span class="font-medium text-gray-900">
                            <?= date('M j, Y', strtotime($company['created_at'])) ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    
                    <a href="<?= url("/app/users?company_id={$company['id']}") ?>" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        Manage Users
                    </a>

                    <a href="<?= url("/app/documents?company_id={$company['id']}") ?>" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        View Documents
                    </a>

                    <a href="<?= url("/app/reports?company_id={$company['id']}") ?>" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        View Reports
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
