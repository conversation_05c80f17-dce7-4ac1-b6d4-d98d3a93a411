-- Update documents table to match controller expectations
-- Add missing columns for storage location, bundle, intake, and storage type

-- Add location_id column for linking to storage_locations (boxes)
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS location_id INT NULL AFTER category_id,
ADD FOREIGN KEY IF NOT EXISTS fk_documents_location (location_id) REFERENCES storage_locations(id) ON DELETE SET NULL,
ADD INDEX IF NOT EXISTS idx_documents_location (location_id);

-- Add bundle_id column for linking to bundles (if not already exists)
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS bundle_id INT NULL AFTER company_id,
ADD FOREIGN KEY IF NOT EXISTS fk_documents_bundle (bundle_id) REFERENCES bundles(id) ON DELETE SET NULL,
ADD INDEX IF NOT EXISTS idx_documents_bundle (bundle_id);

-- Add intake_id column for linking to document_intake
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS intake_id INT NULL AFTER bundle_id,
ADD FOREIGN KEY IF NOT EXISTS fk_documents_intake (intake_id) REFERENCES document_intake(id) ON DELETE SET NULL,
ADD INDEX IF NOT EXISTS idx_documents_intake (intake_id);

-- Add storage_type column for physical vs online storage
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER location_id,
ADD INDEX IF NOT EXISTS idx_documents_storage_type (storage_type);

-- Add online_storage_path for online documents
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS online_storage_path VARCHAR(500) NULL AFTER storage_type;

-- Add physical_location_notes for additional location info
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS physical_location_notes TEXT NULL AFTER online_storage_path;

-- Update status enum to include 'deleted' for soft deletes
ALTER TABLE documents 
MODIFY COLUMN status ENUM('draft', 'pending', 'approved', 'rejected', 'archived', 'deleted') DEFAULT 'draft';

-- Add indexes for better performance
ALTER TABLE documents 
ADD INDEX IF NOT EXISTS idx_documents_company_storage (company_id, storage_type),
ADD INDEX IF NOT EXISTS idx_documents_bundle_status (bundle_id, status),
ADD INDEX IF NOT EXISTS idx_documents_location_status (location_id, status);

-- Create some sample storage locations if they don't exist
INSERT IGNORE INTO storage_locations (
    warehouse_id, name, identifier, type, storage_type, capacity, status, created_by, created_at, updated_at
) 
SELECT 
    w.id as warehouse_id,
    CONCAT('Box-', LPAD(ROW_NUMBER() OVER (PARTITION BY w.id ORDER BY w.id), 3, '0')) as name,
    CONCAT('BOX-', w.id, '-', LPAD(ROW_NUMBER() OVER (PARTITION BY w.id ORDER BY w.id), 3, '0')) as identifier,
    'box' as type,
    'physical' as storage_type,
    100 as capacity,
    'active' as status,
    1 as created_by,
    NOW() as created_at,
    NOW() as updated_at
FROM warehouses w
WHERE w.status = 'active'
AND NOT EXISTS (SELECT 1 FROM storage_locations sl WHERE sl.warehouse_id = w.id)
LIMIT 10;

-- Create some sample bundles if they don't exist and bundles table exists
INSERT IGNORE INTO bundles (
    company_id, name, description, reference_number, category, priority, 
    retention_period, access_level, created_by, created_at, updated_at
) VALUES 
(1, 'General Documents', 'General document storage bundle', 'BUN-2024-000001', 'general', 'medium', 7, 'private', 1, NOW(), NOW()),
(1, 'Financial Records', 'Financial documents and reports', 'BUN-2024-000002', 'financial', 'high', 10, 'restricted', 1, NOW(), NOW()),
(1, 'Legal Documents', 'Contracts and legal paperwork', 'BUN-2024-000003', 'legal', 'high', 15, 'restricted', 1, NOW(), NOW()),
(1, 'HR Documents', 'Human resources files', 'BUN-2024-000004', 'hr', 'medium', 7, 'private', 1, NOW(), NOW());

-- Update existing documents to have proper relationships
-- Assign documents to the first available bundle and storage location
UPDATE documents d
SET 
    bundle_id = (SELECT id FROM bundles WHERE company_id = d.company_id LIMIT 1),
    location_id = (SELECT id FROM storage_locations WHERE type = 'box' AND status = 'active' LIMIT 1),
    storage_type = 'physical'
WHERE d.bundle_id IS NULL 
AND d.location_id IS NULL 
AND d.status != 'deleted';
