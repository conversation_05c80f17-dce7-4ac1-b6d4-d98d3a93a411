<?php
$title = 'Invoice #' . $invoice['invoice_number'];
$currentPage = 'billing';
include __DIR__ . '/../layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="<?= url('/app/billing') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Billing
                    </a>
                    <h1 class="h3 mb-0 d-inline">Invoice #<?= htmlspecialchars($invoice['invoice_number']) ?></h1>
                </div>
                <div>
                    <?php if ($invoice['payment_status'] !== 'paid'): ?>
                        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#markPaidModal">
                            <i class="fas fa-check"></i> Mark as Paid
                        </button>
                    <?php endif; ?>
                    <button type="button" class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> Print
                    </button>
                </div>
            </div>

            <!-- Invoice Details -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <!-- Invoice Header -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h4>Invoice Details</h4>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Invoice Number:</strong></td>
                                            <td><?= htmlspecialchars($invoice['invoice_number']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Invoice Date:</strong></td>
                                            <td><?= date('F j, Y', strtotime($invoice['invoice_date'])) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Due Date:</strong></td>
                                            <td><?= date('F j, Y', strtotime($invoice['due_date'])) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Billing Period:</strong></td>
                                            <td>
                                                <?= date('M j', strtotime($invoice['billing_period_start'])) ?> - 
                                                <?= date('M j, Y', strtotime($invoice['billing_period_end'])) ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'draft' => 'secondary',
                                                    'sent' => 'info',
                                                    'paid' => 'success',
                                                    'overdue' => 'danger',
                                                    'cancelled' => 'dark',
                                                    'disputed' => 'warning'
                                                ];
                                                $class = $statusClass[$invoice['payment_status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $class ?>"><?= ucfirst($invoice['payment_status']) ?></span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h4>Client Information</h4>
                                    <div class="border p-3 rounded">
                                        <h5><?= htmlspecialchars($invoice['client_name']) ?></h5>
                                        <?php if (!empty($invoice['client_address'])): ?>
                                            <p class="mb-1"><?= nl2br(htmlspecialchars($invoice['client_address'])) ?></p>
                                        <?php endif; ?>
                                        <?php if (!empty($invoice['client_email'])): ?>
                                            <p class="mb-1">Email: <?= htmlspecialchars($invoice['client_email']) ?></p>
                                        <?php endif; ?>
                                        <?php if (!empty($invoice['client_phone'])): ?>
                                            <p class="mb-0">Phone: <?= htmlspecialchars($invoice['client_phone']) ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Line Items -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4>Services Provided</h4>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>Service</th>
                                                    <th>Description</th>
                                                    <th class="text-end">Quantity</th>
                                                    <th class="text-end">Unit Rate</th>
                                                    <th class="text-end">Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($invoice['line_items'] as $item): ?>
                                                    <tr>
                                                        <td><strong><?= htmlspecialchars($item['service_name']) ?></strong></td>
                                                        <td>
                                                            <small class="text-muted"><?= htmlspecialchars($item['service_code']) ?></small>
                                                        </td>
                                                        <td class="text-end">
                                                            <?= number_format($item['quantity'], 2) ?> <?= htmlspecialchars($item['unit']) ?>
                                                        </td>
                                                        <td class="text-end">$<?= number_format($item['unit_rate'], 2) ?></td>
                                                        <td class="text-end"><strong>$<?= number_format($item['total_amount'], 2) ?></strong></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Invoice Totals -->
                            <div class="row">
                                <div class="col-md-8"></div>
                                <div class="col-md-4">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Subtotal:</strong></td>
                                            <td class="text-end">$<?= number_format($invoice['subtotal'], 2) ?></td>
                                        </tr>
                                        <?php if ($invoice['tax_amount'] > 0): ?>
                                            <tr>
                                                <td><strong>Tax (<?= number_format($invoice['tax_rate'] * 100, 2) ?>%):</strong></td>
                                                <td class="text-end">$<?= number_format($invoice['tax_amount'], 2) ?></td>
                                            </tr>
                                        <?php endif; ?>
                                        <tr class="border-top">
                                            <td><h4><strong>Total:</strong></h4></td>
                                            <td class="text-end"><h4><strong>$<?= number_format($invoice['total_amount'], 2) ?></strong></h4></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Payment Information -->
                            <?php if ($invoice['payment_status'] === 'paid'): ?>
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="alert alert-success">
                                            <h5><i class="fas fa-check-circle"></i> Payment Received</h5>
                                            <p class="mb-0">
                                                <strong>Payment Date:</strong> <?= date('F j, Y', strtotime($invoice['payment_date'])) ?><br>
                                                <strong>Payment Method:</strong> <?= htmlspecialchars($invoice['payment_method']) ?>
                                                <?php if (!empty($invoice['payment_reference'])): ?>
                                                    <br><strong>Reference:</strong> <?= htmlspecialchars($invoice['payment_reference']) ?>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Invoice Notes -->
                            <?php if (!empty($invoice['invoice_notes'])): ?>
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h5>Notes</h5>
                                        <div class="border p-3 rounded bg-light">
                                            <?= nl2br(htmlspecialchars($invoice['invoice_notes'])) ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Events -->
            <?php if (!empty($events)): ?>
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Related Billing Events</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Service</th>
                                                <th>Event Type</th>
                                                <th>Entity</th>
                                                <th class="text-end">Quantity</th>
                                                <th class="text-end">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($events as $event): ?>
                                                <tr>
                                                    <td><?= date('M j, Y H:i', strtotime($event['performed_at'])) ?></td>
                                                    <td><?= htmlspecialchars($event['service_name']) ?></td>
                                                    <td><small class="text-muted"><?= htmlspecialchars($event['event_type']) ?></small></td>
                                                    <td>
                                                        <?php if ($event['entity_type'] && $event['entity_id']): ?>
                                                            <?= htmlspecialchars($event['entity_type']) ?> #<?= $event['entity_id'] ?>
                                                        <?php endif; ?>
                                                        <?php if ($event['reference_number']): ?>
                                                            <br><small class="text-muted"><?= htmlspecialchars($event['reference_number']) ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-end"><?= number_format($event['quantity'], 2) ?></td>
                                                    <td class="text-end">$<?= number_format($event['total_amount'], 2) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Mark as Paid Modal -->
<?php if ($invoice['payment_status'] !== 'paid'): ?>
<div class="modal fade" id="markPaidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="<?= url('/app/billing/invoice/' . $invoice['id'] . '/mark-paid') ?>">
                <div class="modal-header">
                    <h5 class="modal-title">Mark Invoice as Paid</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">Payment Date</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" 
                               value="<?= date('Y-m-d') ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="">Select payment method...</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="check">Check</option>
                            <option value="cash">Cash</option>
                            <option value="credit_card">Credit Card</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="payment_reference" class="form-label">Payment Reference</label>
                        <input type="text" class="form-control" id="payment_reference" name="payment_reference" 
                               placeholder="Transaction ID, check number, etc.">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Additional payment notes..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Mark as Paid</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include __DIR__ . '/../layouts/footer.php'; ?>
