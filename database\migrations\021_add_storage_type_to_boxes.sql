-- Add storage_type column to boxes table
-- This column is needed for the intake workflow to distinguish between physical and online storage

-- Add storage_type column
ALTER TABLE boxes 
ADD COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER warehouse_id;

-- Add index for storage_type
ALTER TABLE boxes 
ADD INDEX idx_storage_type (storage_type);

-- Update existing boxes to have physical storage type
UPDATE boxes 
SET storage_type = 'physical' 
WHERE storage_type IS NULL;

-- Add comment to document the purpose
ALTER TABLE boxes 
MODIFY COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' COMMENT 'Storage type: physical for warehouse storage, online for digital-only storage';
